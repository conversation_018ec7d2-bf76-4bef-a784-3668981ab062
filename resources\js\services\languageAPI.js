import axios from 'axios';
import { API_BASE_URL } from '../config/api.js';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

export const languageAPI = {
  getLanguages: async (params = {}) => {
    const response = await api.get('/languages', { params });
    return response.data;
  },

  getLanguage: async (id) => {
    const response = await api.get(`/languages/${id}`);
    return response.data;
  },

  createLanguage: async (data) => {
    const response = await api.post('/languages', data);
    return response.data;
  },

  updateLanguage: async (id, data) => {
    const response = await api.put(`/languages/${id}`, data);
    return response.data;
  },

  deleteLanguage: async (id) => {
    const response = await api.delete(`/languages/${id}`);
    return response.data;
  },

  getStatistics: async () => {
    const response = await api.get('/languages-statistics');
    return response.data;
  },

  setDefault: async (id) => {
    const response = await api.patch(`/languages/${id}/set-default`);
    return response.data;
  }
};

export default languageAPI;

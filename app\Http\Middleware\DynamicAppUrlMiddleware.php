<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class DynamicAppUrlMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Only update APP_URL for local environment
        if (app()->environment('local')) {
            $scheme = $request->isSecure() ? 'https' : 'http';
            $host = $request->getHost();
            $port = $request->getPort();
            
            // Build the correct URL
            $url = $scheme . '://' . $host;
            
            // Add port if it's not standard
            if (($scheme === 'http' && $port != 80) || ($scheme === 'https' && $port != 443)) {
                $url .= ':' . $port;
            }
            
            // Update the configuration
            config(['app.url' => $url]);
        }

        return $next($request);
    }
}

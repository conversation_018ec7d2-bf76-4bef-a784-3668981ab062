import axios from 'axios';
import { API_BASE_URL, getValidatedApiUrl, getEnvironmentInfo } from '../config/api.js';

// Log environment info for debugging
console.log('🌍 Environment Info:', getEnvironmentInfo());

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Accept': 'application/json',
  },
  timeout: 30000, // 30 second timeout
});

// Store for dynamic API URL
let currentApiBaseUrl = API_BASE_URL;

// Initialize and test API connectivity
const initializeApi = async () => {
  try {
    console.log('🚀 Initializing API connection...');
    const validatedUrl = await getValidatedApiUrl();
    
    if (validatedUrl !== currentApiBaseUrl) {
     
      currentApiBaseUrl = validatedUrl;
      api.defaults.baseURL = validatedUrl;
    }
    
    console.log('API initialized successfully with URL:', currentApiBaseUrl);
  } catch (error) {
    console.error(' API initialization failed:', error);
  }
};

// Initialize API on module load
initializeApi();

// Export function to manually test/reinitialize API
export const reinitializeApi = initializeApi;

// Export function to get current API info
export const getApiInfo = () => ({
  currentUrl: currentApiBaseUrl,
  originalUrl: API_BASE_URL,
  environmentInfo: getEnvironmentInfo()
});

// Add request interceptor for authentication and dynamic URL handling
api.interceptors.request.use(
  async (config) => {
    // Update base URL if it has changed
    if (currentApiBaseUrl !== API_BASE_URL) {
      config.baseURL = currentApiBaseUrl;
      console.log('🔄 Updated API Base URL to:', currentApiBaseUrl);
    }
    
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Only set Content-Type to application/json for non-FormData requests
    if (!(config.data instanceof FormData)) {
      config.headers['Content-Type'] = 'application/json';
    }
    
    // Log request details for debugging
    console.log('📤 API Request:', {
      method: config.method.toUpperCase(),
      url: `${config.baseURL}${config.url}`,
      hasAuth: !!token,
      isFormData: config.data instanceof FormData
    });
    
    return config;
  },
  (error) => {
    console.error('❌ Request setup error:', error);
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling and automatic URL switching
api.interceptors.response.use(
  (response) => {
    // Log successful response
    console.log('📥 API Response:', {
      status: response.status,
      url: response.config.url,
      method: response.config.method.toUpperCase()
    });
    return response;
  },
  async (error) => {
    console.error('❌ API Error:', {
      status: error.response?.status,
      message: error.message,
      url: error.config?.url,
      baseURL: error.config?.baseURL
    });
    
    // Handle authentication errors
    if (error.response?.status === 401) {
      console.warn('🔒 Authentication failed - clearing token');
      localStorage.removeItem('auth_token');
      window.location.reload();
      return Promise.reject(error);
    }
    
    // Handle connection errors - try to find a working API URL
    if (!error.response && (error.code === 'ECONNABORTED' || error.message.includes('Network Error'))) {
      console.warn('🌐 Network error detected, attempting to find working API URL...');
      
      try {
        const newApiUrl = await getValidatedApiUrl();
        if (newApiUrl !== currentApiBaseUrl) {
          console.log('🔄 Switching to working API URL:', newApiUrl);
          currentApiBaseUrl = newApiUrl;
          api.defaults.baseURL = newApiUrl;
          
          // Retry the original request with new URL
          const retryConfig = { ...error.config };
          retryConfig.baseURL = newApiUrl;
          console.log('🔁 Retrying request with new URL...');
          return api.request(retryConfig);
        }
      } catch (urlError) {
        console.error('❌ Failed to find working API URL:', urlError);
      }
    }
    
    return Promise.reject(error);
  }
);

// Land Owner API functions

// Get all land owners with pagination and search
export const getAll = async (params = {}) => {
  try {
    const response = await api.get('/land-owners', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching land owners:', error);
    throw error;
  }
};

// Get single land owner by ID
export const getById = async (id) => {
  try {
    const response = await api.get(`/land-owners/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching land owner ${id}:`, error);
    throw error;
  }
};

// Create new land owner
export const create = async (data) => {
  try {
    console.log('🔄 Creating land owner with data:', data);
    
    // Check if we have any file data that needs FormData
    const hasFiles = Object.keys(data).some(key => {
      const value = data[key];
      return value instanceof File;
    });

    console.log('📁 Has files:', hasFiles);

    let requestData;
    let headers = {};
    
    if (hasFiles) {
      console.log('📁 Using FormData for file upload');
      // Use FormData for file uploads
      requestData = new FormData();
      
      Object.keys(data).forEach(key => {
        const value = data[key];
        if (value !== null && value !== undefined && value !== '') {
          requestData.append(key, value);
        }
      });
      
      // Don't set Content-Type header - let browser set it with proper boundary
    } else {
      console.log('📄 Using JSON for regular data');
      // Use regular JSON for non-file data
      requestData = {};
      Object.keys(data).forEach(key => {
        const value = data[key];
        if (value !== null && value !== undefined && value !== '') {
          requestData[key] = value;
        }
      });
      headers['Content-Type'] = 'application/json';
    }

    const response = await api.post('/land-owners', requestData, { headers });
    console.log('✅ Land owner created successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error creating land owner:', error);
    // Log more details about the error
    if (error.response) {
      console.error('📊 Response status:', error.response.status);
      console.error('📄 Response data:', error.response.data);
      console.error('📋 Response headers:', error.response.headers);
      
      // Return the error response data for better error handling
      if (error.response.data) {
        return error.response.data;
      }
    } else if (error.request) {
      console.error('📡 Request was made but no response received:', error.request);
    } else {
      console.error('⚙️ Error setting up request:', error.message);
    }
    
    // Re-throw for handling in component
    throw error;
  }
};

// Update existing land owner
export const update = async (id, data) => {
  try {
    console.log('🔄 Updating land owner:', { id, data });

    // Clean the data - remove empty strings and null values
    const cleanData = {};
    Object.keys(data).forEach(key => {
      if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
        cleanData[key] = data[key];
      }
    });

    console.log('📋 Clean data being sent:', cleanData);

    const response = await api.put(`/land-owners/${id}`, cleanData, {
      headers: {
        'Content-Type': 'application/json',
      }
    });

    console.log('✅ Land owner updated successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error updating land owner:', error);
    
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
      
      // Log specific validation errors if available
      if (error.response.data && error.response.data.errors) {
        console.error('🚫 Validation errors:', error.response.data.errors);
      }
      
      // Return a more structured error response
      return {
        success: false,
        message: error.response.data?.message || `Server error: ${error.response.status}`,
        errors: error.response.data?.errors || null,
        status: error.response.status
      };
    } else if (error.request) {
      console.error('📡 Request was made but no response received:', error.request);
      return {
        success: false,
        message: 'Network error. Please check your connection.',
        errors: null,
        status: null
      };
    } else {
      console.error('⚙️ Error setting up request:', error.message);
      return {
        success: false,
        message: error.message || 'Unknown error occurred',
        errors: null,
        status: null
      };
    }
  }
};

// Delete land owner
export const delete_ = async (id) => {
  try {
    const response = await api.delete(`/land-owners/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error deleting land owner ${id}:`, error);
    throw error;
  }
};

// Get land owners for dropdown (if this endpoint exists)
export const getForDropdown = async () => {
  try {
    const response = await api.get('/land-owners/dropdown');
    return response.data;
  } catch (error) {
    console.error('Error fetching land owners for dropdown:', error);
    throw error;
  }
};

// Get statistics (if this endpoint exists)
export const getStatistics = async () => {
  try {
    const response = await api.get('/land-owners/statistics');
    return response.data;
  } catch (error) {
    console.error('Error fetching land owner statistics:', error);
    throw error;
  }
};

// Audit Log API functions

// Get all audit logs
export const getAllAudits = async (params = {}) => {
  try {
    console.log('🔍 Requesting all audit logs with params:', params);
    const response = await api.get('/land-owners-audits', { params });
    console.log('📊 Audit logs API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching land owner audit logs:', error);
    throw error;
  }
};

// Get audit statistics
export const getAuditStats = async () => {
  try {
    const response = await api.get('/land-owners-audit-stats');
    return response.data;
  } catch (error) {
    console.error('Error fetching audit statistics:', error);
    throw error;
  }
};

// Get detailed audit history for a specific land owner
export const getDetailedAuditHistory = async (id, params = {}) => {
  try {
    const response = await api.get(`/land-owners/${id}/detailed-audit-history`, { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching detailed audit history:', error);
    throw error;
  }
};



<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('unit_details', function (Blueprint $table) {
            // Remove unnecessary columns, keeping only unit_name, unit_type, and status
            $table->dropColumn([
                'bedrooms',
                'bathrooms', 
                'unit_size_sqft',
                'price',
                'description',
                'floor_number',
                'unit_number',
                'has_balcony',
                'has_parking',
                'amenities'
            ]);
            
            // Drop the old index
            $table->dropIndex(['unit_type', 'bedrooms']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('unit_details', function (Blueprint $table) {
            // Restore removed columns
            $table->integer('bedrooms')->default(0);
            $table->integer('bathrooms')->default(0);
            $table->decimal('unit_size_sqft', 10, 2)->nullable();
            $table->decimal('price', 15, 2)->nullable();
            $table->text('description')->nullable();
            $table->integer('floor_number')->nullable();
            $table->string('unit_number')->nullable();
            $table->boolean('has_balcony')->default(false);
            $table->boolean('has_parking')->default(false);
            $table->json('amenities')->nullable();
            
            // Restore the index
            $table->index(['unit_type', 'bedrooms']);
        });
    }
};

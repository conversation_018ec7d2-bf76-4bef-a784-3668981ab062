<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PropertyAmenity;

class PropertyAmenitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $amenities = [
            // Basic Amenities
            [
                'name' => 'Power Backup',
                'description' => '24/7 power backup with generator',
                'icon' => 'power-backup',
                'category' => 'basic',
                'sort_order' => 1
            ],
            [
                'name' => 'Water Supply',
                'description' => '24/7 water supply with backup tank',
                'icon' => 'water-supply',
                'category' => 'basic',
                'sort_order' => 2
            ],
            [
                'name' => 'Elevator',
                'description' => 'High-speed elevators with backup power',
                'icon' => 'elevator',
                'category' => 'basic',
                'sort_order' => 3
            ],
            [
                'name' => 'WiFi',
                'description' => 'High-speed internet connectivity',
                'icon' => 'wifi',
                'category' => 'basic',
                'sort_order' => 4
            ],

            // Recreational Amenities
            [
                'name' => 'Swimming Pool',
                'description' => 'Olympic size swimming pool with lifeguard',
                'icon' => 'swimming-pool',
                'category' => 'recreational',
                'sort_order' => 5
            ],
            [
                'name' => 'Gymnasium',
                'description' => 'Fully equipped modern gymnasium',
                'icon' => 'gym',
                'category' => 'recreational',
                'sort_order' => 6
            ],
            [
                'name' => 'Clubhouse',
                'description' => 'Community clubhouse for events',
                'icon' => 'clubhouse',
                'category' => 'recreational',
                'sort_order' => 7
            ],
            [
                'name' => 'Children Playground',
                'description' => 'Safe and secure playground for children',
                'icon' => 'playground',
                'category' => 'recreational',
                'sort_order' => 8
            ],
            [
                'name' => 'Garden',
                'description' => 'Landscaped gardens and green spaces',
                'icon' => 'garden',
                'category' => 'recreational',
                'sort_order' => 9
            ],

            // Security Amenities
            [
                'name' => '24/7 Security',
                'description' => 'Round the clock security personnel',
                'icon' => 'security',
                'category' => 'security',
                'sort_order' => 10
            ],
            [
                'name' => 'CCTV Surveillance',
                'description' => 'Complete CCTV coverage of all areas',
                'icon' => 'cctv',
                'category' => 'security',
                'sort_order' => 11
            ],
            [
                'name' => 'Intercom Facility',
                'description' => 'Video intercom system for each unit',
                'icon' => 'intercom',
                'category' => 'security',
                'sort_order' => 12
            ],

            // Parking & Transportation
            [
                'name' => 'Covered Parking',
                'description' => 'Covered parking spaces for all residents',
                'icon' => 'parking',
                'category' => 'parking',
                'sort_order' => 13
            ],
            [
                'name' => 'Visitor Parking',
                'description' => 'Dedicated parking spaces for visitors',
                'icon' => 'parking',
                'category' => 'parking',
                'sort_order' => 14
            ],

            // Utilities
            [
                'name' => 'Gas Pipeline',
                'description' => 'Piped gas connection to all units',
                'icon' => 'utilities',
                'category' => 'utilities',
                'sort_order' => 15
            ],
            [
                'name' => 'Waste Management',
                'description' => 'Organized waste collection and disposal',
                'icon' => 'utilities',
                'category' => 'utilities',
                'sort_order' => 16
            ],

            // Maintenance
            [
                'name' => 'Housekeeping',
                'description' => 'Regular housekeeping of common areas',
                'icon' => 'maintenance',
                'category' => 'maintenance',
                'sort_order' => 17
            ],
            [
                'name' => 'Maintenance Service',
                'description' => '24/7 maintenance and repair services',
                'icon' => 'maintenance',
                'category' => 'maintenance',
                'sort_order' => 18
            ]
        ];

        foreach ($amenities as $amenity) {
            PropertyAmenity::create($amenity);
        }
    }
}

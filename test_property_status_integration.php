<?php
require_once 'vendor/autoload.php';

// Initialize Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== Testing Property Status Dropdown Integration ===\n\n";

use App\Models\PropertyStatus;
use App\Models\Project;

try {
    // Test 1: Check PropertyStatus dropdown data
    echo "1. Testing PropertyStatus dropdown data:\n";
    $propertyStatuses = PropertyStatus::active()->orderBy('sort_order')->get(['id', 'name', 'color', 'slug']);
    
    echo "   Available Property Statuses:\n";
    foreach ($propertyStatuses as $status) {
        echo "   - ID: {$status->id}, Name: {$status->name}, Color: {$status->color}, Slug: {$status->slug}\n";
    }
    
    // Test 2: Check if projects can use property_status_id
    echo "\n2. Testing Project with PropertyStatus relationship:\n";
    $projectsCount = Project::count();
    echo "   Total Projects: $projectsCount\n";
    
    if ($projectsCount > 0) {
        $project = Project::with('propertyStatus')->first();
        if ($project) {
            echo "   First Project: {$project->title}\n";
            echo "   Property Status ID: " . ($project->property_status_id ?? 'NULL') . "\n";
            echo "   Property Status Name: " . ($project->propertyStatus?->name ?? 'No Status') . "\n";
            echo "   Property Status Color: " . ($project->propertyStatus?->color ?? 'No Color') . "\n";
        }
    }
    
    // Test 3: Test creating a project with property_status_id
    echo "\n3. Testing Project creation with property_status_id:\n";
    if ($propertyStatuses->count() > 0) {
        $firstStatus = $propertyStatuses->first();
        echo "   Using Property Status: {$firstStatus->name} (ID: {$firstStatus->id})\n";
        
        // This would be how the API request data looks
        $sampleProjectData = [
            'title' => 'Test Project',
            'description' => 'Test Description',
            'property_type_id' => 1, // Assuming property type 1 exists
            'property_status_id' => $firstStatus->id,
            'property_stage' => 'new',
            'location' => 'Test Location',
            'address' => 'Test Address'
        ];
        
        echo "   Sample API Request Data:\n";
        echo "   " . json_encode($sampleProjectData, JSON_PRETTY_PRINT) . "\n";
        echo "   ✅ Structure looks correct for API integration\n";
    }
    
    echo "\n=== Test Complete ===\n";
    echo "✅ Property Status dropdown integration is ready!\n";
    echo "✅ Projects can use property_status_id field\n";
    echo "✅ Relationship between Project and PropertyStatus works\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

import axios from 'axios';

const API_URL = '/api/property-statuses';

// Create axios instance with default config
const api = axios.create({
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

const propertyStatusAPI = {
    // Get all property statuses with pagination and filters
    getAll: async (params = {}) => {
        try {
            const response = await api.get(API_URL, { params });
            return response.data;
        } catch (error) {
            console.error('Error fetching property statuses:', error);
            throw error.response?.data || error;
        }
    },

    // Get property status by ID
    getById: async (id) => {
        try {
            const response = await api.get(`${API_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching property status:', error);
            throw error.response?.data || error;
        }
    },

    // Create new property status
    create: async (data) => {
        try {
            const response = await api.post(API_URL, data);
            return response.data;
        } catch (error) {
            console.error('Error creating property status:', error);
            throw error.response?.data || error;
        }
    },

    // Update property status
    update: async (id, data) => {
        try {
            const response = await api.put(`${API_URL}/${id}`, data);
            return response.data;
        } catch (error) {
            console.error('Error updating property status:', error);
            throw error.response?.data || error;
        }
    },

    // Delete property status
    delete: async (id) => {
        try {
            const response = await api.delete(`${API_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting property status:', error);
            throw error.response?.data || error;
        }
    },

    // Toggle property status status (active/inactive)
    toggleStatus: async (id) => {
        try {
            const response = await api.patch(`${API_URL}/${id}/toggle-status`);
            return response.data;
        } catch (error) {
            console.error('Error toggling property status:', error);
            throw error.response?.data || error;
        }
    },

    // Get dropdown options (active property statuses only)
    getDropdown: async () => {
        try {
            const response = await axios.get('/api/property-statuses/dropdown');
            return response.data;
        } catch (error) {
            console.error('Error fetching property status dropdown:', error);
            throw error.response?.data || error;
        }
    },

    // Update sort orders
    updateSortOrders: async (propertyStatuses) => {
        try {
            const response = await api.patch(`${API_URL}/sort-orders`, {
                property_statuses: propertyStatuses
            });
            return response.data;
        } catch (error) {
            console.error('Error updating sort orders:', error);
            throw error.response?.data || error;
        }
    },

    // Search property statuses
    search: async (query, params = {}) => {
        try {
            const searchParams = { ...params, search: query };
            const response = await api.get(API_URL, { params: searchParams });
            return response.data;
        } catch (error) {
            console.error('Error searching property statuses:', error);
            throw error.response?.data || error;
        }
    }
};

export default propertyStatusAPI;

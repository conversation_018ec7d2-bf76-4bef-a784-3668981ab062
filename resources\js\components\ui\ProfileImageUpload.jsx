import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { 
  Camera, 
  Upload, 
  X, 
  Eye, 
  User
} from 'lucide-react';

const ProfileImageUpload = ({ 
  value, 
  onChange, 
  name = "Owner",
  disabled = false
}) => {
  const [preview, setPreview] = useState(value || null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef(null);

  // Helper function to get full image URL
  const getImageUrl = (photoPath) => {
    if (!photoPath) return null;
    
    // If it's already a full URL, return as is
    if (photoPath.startsWith('http://') || photoPath.startsWith('https://')) {
      return photoPath;
    }
    
    // If it starts with /landowners, use the base URL
    if (photoPath.startsWith('/landowners/')) {
      return `${window.location.protocol}//${window.location.host}${photoPath}`;
    }
    
    // If it starts with /storage, use the base URL
    if (photoPath.startsWith('/storage/')) {
      return `${window.location.protocol}//${window.location.host}${photoPath}`;
    }
    
    // If it's just a filename or relative path, assume it's in landowners/photo directory
    return `${window.location.protocol}//${window.location.host}/landowners/photo/${photoPath}`;
  };

  const handleFileSelect = (file) => {
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select a valid image file');
      return;
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB');
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreview(e.target.result);
    };
    reader.onerror = (e) => {
      console.error('Error creating preview:', e);
    };
    reader.readAsDataURL(file);

    // Store the file for form submission
    setSelectedFile(file);
    onChange(file);
  };

  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    if (!disabled) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    
    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleRemove = () => {
    setPreview(null);
    setSelectedFile(null);
    onChange(null); // Send null instead of empty string for better backend validation
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // If value is a URL (existing image), show it
  React.useEffect(() => {
    if (value && typeof value === 'string') {
      // For existing images from server, convert to full URL
      const fullImageUrl = getImageUrl(value);
      setPreview(fullImageUrl);
      setSelectedFile(null);
    } else if (value === null || value === '') {
      setPreview(null);
      setSelectedFile(null);
    } else if (value instanceof File) {
      // For new file uploads, preview is already set by handleFileSelect
      // Don't change it here
    }
  }, [value]);

  return (
    <div className="space-y-4">
      <div className="text-center">
        <Label className="text-base font-medium">Owner's Photo</Label>
        <p className="text-sm text-gray-500 mt-1">
          Upload a clear photo of the land owner
        </p>
      </div>
      
      <div className="flex flex-col items-center space-y-4">
        {/* Avatar Display */}
        <div className="relative">
          <div 
            className={`
              w-32 h-32 rounded-full border-4 border-gray-200 overflow-hidden bg-gray-100 
              cursor-pointer transition-all duration-200 hover:border-gray-300
              ${dragOver ? 'border-blue-400 scale-105' : ''}
              ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            `}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
            onClick={handleClick}
          >
            {preview ? (
              <img
                src={preview}
                alt={name}
                className="w-full h-full object-cover"
                onError={(e) => {
                  console.error('Failed to load image:', preview);
                  e.target.style.display = 'none';
                }}
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200">
                <User className="w-12 h-12 text-gray-400" />
              </div>
            )}
          </div>
          
          {/* Edit Button Overlay */}
          <div className="absolute bottom-2 right-2">
            <Button
              type="button"
              size="sm"
              variant="secondary"
              className="w-8 h-8 p-0 rounded-full shadow-lg"
              onClick={(e) => {
                e.stopPropagation();
                handleClick();
              }}
              disabled={disabled}
            >
              <Camera className="w-4 h-4" />
            </Button>
          </div>
        </div>

        {/* Upload Status */}
        <div className="text-center">
          {preview ? (
            <div className="space-y-2">
              <p className="text-sm font-medium text-green-600">
                {selectedFile ? 'New photo selected' : 'Photo uploaded'}
              </p>
              <div className="flex justify-center space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => window.open(preview, '_blank')}
                  disabled={disabled}
                >
                  <Eye className="w-4 h-4 mr-2" />
                  View
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleRemove}
                  disabled={disabled}
                >
                  <X className="w-4 h-4 mr-2" />
                  Remove
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              <p className="text-sm text-gray-500">No photo uploaded</p>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleClick}
                disabled={disabled}
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload Photo
              </Button>
            </div>
          )}
        </div>

        {/* Hidden File Input */}
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          className="hidden"
          disabled={disabled}
        />
      </div>
    </div>
  );
};

export default ProfileImageUpload;

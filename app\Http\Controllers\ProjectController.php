<?php

namespace App\Http\Controllers;

use App\Models\Project;
use App\Models\ProjectUnit;
use App\Models\ProjectImage;
use App\Models\ProjectVideo;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ProjectController extends Controller
{
    /**
     * Display a listing of projects
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Project::with(['creator', 'updater', 'images', 'videos', 'units', 'country', 'propertyType', 'propertyStatus']);

            // Apply filters
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%")
                      ->orWhere('location', 'like', "%{$search}%")
                      ->orWhere('address', 'like', "%{$search}%");
                });
            }

            if ($request->filled('property_type')) {
                $query->byType($request->property_type);
            }

            if ($request->filled('status')) {
                $query->byStatus($request->status);
            }

            if ($request->filled('is_featured')) {
                if ($request->is_featured === 'true') {
                    $query->featured();
                }
            }

            if ($request->filled('is_available')) {
                if ($request->is_available === 'true') {
                    $query->available();
                }
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 15);
            $projects = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $projects,
                'statistics' => Project::getStatistics()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch projects: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created project
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'property_type_id' => 'required|exists:property_types,id', // Changed from enum to foreign key
                'property_status_id' => 'required|exists:property_statuses,id', // Changed from enum to foreign key
                'property_stage' => 'required|in:new,old',
                'location' => 'required|string|max:255',
                'location_id' => 'nullable|exists:locations,id',
                'address' => 'required|string|max:500',
                'city' => 'nullable|string|max:100',
                'state_id' => 'nullable|exists:states,id',
                'country_id' => 'nullable|exists:countries,id',
                'postal_code' => 'nullable|string|max:20',
                'latitude' => 'nullable|string',
                'longitude' => 'nullable|string',
                'area_sqft' => 'nullable|string',
                'price_per_area_sqft' => 'nullable|string',
                'total_price' => 'nullable|string',
                'bedrooms' => 'nullable|string',
                'bathrooms' => 'nullable|string',
                'floors' => 'nullable|string',
                'parking_spaces' => 'nullable|string',
                'year_built' => 'nullable|string',
                'amenities' => 'nullable|string',
                'features' => 'nullable|string',
                'is_featured' => 'nullable',
                'is_available' => 'nullable',
                // Simplified validation for testing
                'units' => 'nullable|string',
                'videos' => 'nullable|string',
                'images' => 'nullable',
                'images.*' => 'nullable|file',
                'video_files' => 'nullable',
                'video_files.*' => 'nullable|file'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // Extract and validate project data
            $projectData = $request->only([
                'title', 'description', 'property_type_id', 'property_status_id', 'status', 'property_stage', 'location', // Changed to property_type_id and added property_status_id
                'location_id', 'address', 'city', 'state_id', 'country_id', 'postal_code',
                'latitude', 'longitude', 'area_sqft', 'price_per_area_sqft','total_price','bedrooms', 'bathrooms',
                'floors', 'parking_spaces', 'year_built', 'amenities', 'features',
                'is_featured', 'is_available'
            ]);
            
            // Convert string booleans to actual booleans
            if (isset($projectData['is_featured'])) {
                $projectData['is_featured'] = filter_var($projectData['is_featured'], FILTER_VALIDATE_BOOLEAN);
            }
            if (isset($projectData['is_available'])) {
                $projectData['is_available'] = filter_var($projectData['is_available'], FILTER_VALIDATE_BOOLEAN);
            }
            
            // Convert JSON strings to arrays
            if (isset($projectData['amenities']) && is_string($projectData['amenities'])) {
                $projectData['amenities'] = json_decode($projectData['amenities'], true) ?: [];
            }
            if (isset($projectData['features']) && is_string($projectData['features'])) {
                $projectData['features'] = json_decode($projectData['features'], true) ?: [];
            }
            
            // Handle empty numeric fields - convert empty strings to null
            $numericFields = ['latitude', 'longitude', 'area_sqft', 'bedrooms', 'bathrooms', 'floors', 'parking_spaces', 'year_built'];
            foreach ($numericFields as $field) {
                if (isset($projectData[$field]) && ($projectData[$field] === '' || $projectData[$field] === null)) {
                    $projectData[$field] = null;
                }
            }
            
            // Ensure address is not accidentally nullified
            if (!isset($projectData['address']) || $projectData['address'] === '') {
                return response()->json([
                    'success' => false,
                    'message' => 'Address field is required and cannot be empty',
                    'errors' => ['address' => ['The address field is required.']]
                ], 422);
            }
            
            $projectData['created_by'] = Auth::id();

            $project = Project::create($projectData);

            // Handle units
            if ($request->has('units')) {
                $units = $request->units;
                
                // If units is a JSON string, decode it
                if (is_string($units)) {
                    $units = json_decode($units, true);
                }
                
                // If we have valid units array, process them
                if (is_array($units) && !empty($units)) {
                    foreach ($units as $unitData) {
                        // Convert array to object-like access if needed
                        if (is_array($unitData)) {
                            $unitData['project_id'] = $project->id;
                            $unitData['created_by'] = Auth::id();
                            
                            // Handle features field - convert array to JSON string if needed
                            if (isset($unitData['features']) && is_array($unitData['features'])) {
                                $unitData['features'] = $unitData['features']; // Let the model handle casting
                            } else if (isset($unitData['features']) && is_string($unitData['features'])) {
                                $unitData['features'] = json_decode($unitData['features'], true) ?: [];
                            } else {
                                $unitData['features'] = [];
                            }
                            
                            // Handle empty numeric fields - convert empty strings to null
                            $numericFields = ['area_sqft', 'bedrooms', 'bathrooms', 'floor_number', 'propertyService',  'unit_size', 'rent_price', 'sell_price', 'lease_price'];
                            foreach ($numericFields as $field) {
                                if (isset($unitData[$field]) && ($unitData[$field] === '' || $unitData[$field] === null)) {
                                    $unitData[$field] = null;
                                }
                            }
                            
                            // Create unit
                            ProjectUnit::create($unitData);
                        }
                    }
                }
            }

            // Handle image uploads
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $index => $imageFile) {
                    $imagePath = $imageFile->store('projects/images', 'public');
                    
                    ProjectImage::create([
                        'project_id' => $project->id,
                        'image_path' => $imagePath,
                        'image_name' => $imageFile->getClientOriginalName(),
                        'image_type' => $request->image_types[$index] ?? 'gallery',
                        'image_size' => $imageFile->getSize(),
                        'alt_text' => $request->image_titles[$index] ?? $imageFile->getClientOriginalName(),
                        'caption' => $request->image_descriptions[$index] ?? null,
                        'sort_order' => $index + 1,
                        'is_featured' => $index === 0,
                        'created_by' => Auth::id()
                    ]);
                }
            }

            // Handle videos
            if ($request->has('videos')) {
                $videos = $request->videos;
                
                // If videos is a JSON string, decode it
                if (is_string($videos)) {
                    $videos = json_decode($videos, true);
                }
                
                // If we have valid videos array, process them
                if (is_array($videos) && !empty($videos)) {
                    foreach ($videos as $index => $videoData) {
                        if (is_array($videoData)) {
                            $videoRecord = [
                                'project_id' => $project->id,
                                'video_type' => $videoData['video_type'],
                                'title' => $videoData['title'],
                                'description' => $videoData['description'] ?? null,
                                'sort_order' => $index + 1,
                                'is_featured' => $index === 0,
                                'created_by' => Auth::id()
                            ];

                            if ($videoData['video_type'] === 'youtube') {
                                $videoRecord['youtube_url'] = $videoData['youtube_url'];
                            } elseif ($videoData['video_type'] === 'url') {
                                $videoRecord['video_url'] = $videoData['video_url'];
                            }

                            ProjectVideo::create($videoRecord);
                        }
                    }
                }
            }

            // Handle video file uploads
            if ($request->hasFile('video_files')) {
                foreach ($request->file('video_files') as $index => $videoFile) {
                    $videoPath = $videoFile->store('projects/videos', 'public');
                    
                    ProjectVideo::create([
                        'project_id' => $project->id,
                        'video_type' => 'upload',
                        'video_path' => $videoPath,
                        'title' => 'Video ' . ($index + 1),
                        'sort_order' => $index + 1,
                        'created_by' => Auth::id()
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Project created successfully',
                'data' => $project->load(['units', 'images', 'videos', 'country'])
            ], 201);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create project: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified project
     */
    public function show(Project $project): JsonResponse
    {
        try {
            $project->load(['units', 'images', 'videos', 'creator', 'updater', 'country', 'propertyType', 'propertyStatus']);

            return response()->json([
                'success' => true,
                'data' => $project
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch project: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified project
     */
    public function update(Request $request, Project $project): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'description' => 'required|string',
                'property_type_id' => 'required|exists:property_types,id', // Changed from enum to foreign key
                'property_status_id' => 'required|exists:property_statuses,id', // Changed from enum to foreign key
                'property_stage' => 'required|in:new,old',
                'location' => 'required|string|max:255',
                'location_id' => 'nullable|exists:locations,id',
                'address' => 'required|string|max:500',
                'city' => 'nullable|string|max:100',
                'state_id' => 'nullable|exists:states,id',
                'country_id' => 'nullable|exists:countries,id',
                'postal_code' => 'nullable|string|max:20',
                'latitude' => 'nullable|string',
                'longitude' => 'nullable|string',
                'area_sqft' => 'nullable|string',
                'price_per_area_sqft' => 'nullable|string',
                'total_price' => 'nullable|string',
                'bedrooms' => 'nullable|string',
                'bathrooms' => 'nullable|string',
                'floors' => 'nullable|string',
                'parking_spaces' => 'nullable|string',
                'year_built' => 'nullable|string',
                'amenities' => 'nullable|string',
                'features' => 'nullable|string',
                'is_featured' => 'nullable',
                'is_available' => 'nullable',
                // Simplified validation for testing
                'units' => 'nullable|string',
                'videos' => 'nullable|string',
                'images' => 'nullable',
                'images.*' => 'nullable|file',
                'video_files' => 'nullable',
                'video_files.*' => 'nullable|file'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::beginTransaction();

            // Update project
            $projectData = $request->only([
               'title', 'description', 'property_type_id', 'property_status_id', 'status', 'property_stage', 'location', // Changed to property_type_id and added property_status_id
                'location_id', 'address', 'city', 'state_id', 'country_id', 'postal_code',
                'latitude', 'longitude', 'area_sqft', 'price_per_area_sqft','total_price','bedrooms', 'bathrooms',
                'floors', 'parking_spaces', 'year_built', 'amenities', 'features',
                'is_featured', 'is_available'
            ]);
            
            // Handle boolean conversions for FormData
            if (isset($projectData['is_featured'])) {
                $projectData['is_featured'] = filter_var($projectData['is_featured'], FILTER_VALIDATE_BOOLEAN);
            }
            if (isset($projectData['is_available'])) {
                $projectData['is_available'] = filter_var($projectData['is_available'], FILTER_VALIDATE_BOOLEAN);
            }
            
            // Convert JSON strings to arrays
            if (isset($projectData['amenities']) && is_string($projectData['amenities'])) {
                $projectData['amenities'] = json_decode($projectData['amenities'], true) ?: [];
            }
            if (isset($projectData['features']) && is_string($projectData['features'])) {
                $projectData['features'] = json_decode($projectData['features'], true) ?: [];
            }
            
            // Handle empty numeric fields - convert empty strings to null
            $numericFields = ['latitude', 'longitude', 'area_sqft', 'bedrooms', 'bathrooms', 'floors', 'parking_spaces', 'year_built'];
            foreach ($numericFields as $field) {
                if (isset($projectData[$field]) && ($projectData[$field] === '' || $projectData[$field] === null)) {
                    $projectData[$field] = null;
                }
            }
            
            // Ensure address is not accidentally nullified
            if (!isset($projectData['address']) || $projectData['address'] === '') {
                return response()->json([
                    'success' => false,
                    'message' => 'Address field is required and cannot be empty',
                    'errors' => ['address' => ['The address field is required.']]
                ], 422);
            }
            
            $projectData['updated_by'] = Auth::id();

            $project->update($projectData);

            // Handle units update
            if ($request->has('units')) {
                $units = $request->units;
                
                // If units is a JSON string, decode it
                if (is_string($units)) {
                    $units = json_decode($units, true);
                }
                
                // Get existing units for this project
                $existingUnits = $project->units()->get()->keyBy('unit_number');
                $submittedUnitNumbers = [];
                
                // If we have valid units array, process them
                if (is_array($units) && !empty($units)) {
                    foreach ($units as $unitData) {
                        // Convert array to object-like access if needed
                        if (is_array($unitData)) {
                            $unitNumber = $unitData['unit_number'];
                            $submittedUnitNumbers[] = $unitNumber;
                            
                            $processedUnitData = [
                                'project_id' => $project->id,
                                'unit_number' => $unitNumber,
                                'rent_type'=>$unitData['rentType'] ?? $unitData['rent_type'] ?? null,
                                'unit_type' => $unitData['unit_type'] ?? null,
                                'floor_number' => $unitData['floor_number'] ?? null,
                                'propertyService' => $unitData['propertyService'] ?? null,
                                'unit_size'=>$unitData['unit_size'] ?? null,
                                'area_sqft' => $unitData['area_sqft'] ?? null,
                                'bedrooms' => $unitData['bedrooms'] ?? null,
                                'bathrooms' => $unitData['bathrooms'] ?? null,
                                'rent_price' => $unitData['rent_price'] ?? null,
                                'lease_for' => $unitData['lease_for'] ?? null,
                                'sell_price' => $unitData['sell_price'] ?? null,
                                'lease_price' => $unitData['lease_price'] ?? null,
                                'currency' => $unitData['currency'] ?? 'USD',
                                'status' => $unitData['status'] ?? 'available',
                                'description' => $unitData['description'] ?? null,
                                'is_available' => $unitData['is_available'] ?? true,
                                'updated_by' => Auth::id()
                            ];
                            
                            // Handle features field - convert array to JSON string if needed
                            if (isset($unitData['features']) && is_array($unitData['features'])) {
                                $processedUnitData['features'] = $unitData['features']; // Let the model handle casting
                            } else {
                                $processedUnitData['features'] = json_decode($unitData['features'] ?? '[]', true) ?: [];
                            }
                            
                            // Handle empty numeric fields - convert empty strings to null
                            $numericFields = ['area_sqft', 'bedrooms', 'bathrooms', 'floor_number','propertyService', 'unit_size', 'rent_price', 'sell_price', 'lease_price'];
                            foreach ($numericFields as $field) {
                                if (isset($processedUnitData[$field]) && ($processedUnitData[$field] === '' || $processedUnitData[$field] === null)) {
                                    $processedUnitData[$field] = null;
                                }
                            }
                            
                            // Update or create unit
                            if ($existingUnits->has($unitNumber)) {
                                // Update existing unit
                                $existingUnits[$unitNumber]->update($processedUnitData);
                            } else {
                                // Create new unit
                                $processedUnitData['created_by'] = Auth::id();
                                ProjectUnit::create($processedUnitData);
                            }
                        }
                    }
                }
                
                // Delete units that are no longer in the submitted data
                if (!empty($submittedUnitNumbers)) {
                    $project->units()->whereNotIn('unit_number', $submittedUnitNumbers)->delete();
                } else {
                    // If no units submitted, delete all existing units
                    $project->units()->delete();
                }
            }

            // Handle image uploads (if new images are being added)
            if ($request->hasFile('images')) {
                foreach ($request->file('images') as $index => $imageFile) {
                    $imagePath = $imageFile->store('projects/images', 'public');
                    
                    ProjectImage::create([
                        'project_id' => $project->id,
                        'image_path' => $imagePath,
                        'image_name' => $imageFile->getClientOriginalName(),
                        'image_type' => $request->image_types[$index] ?? 'gallery',
                        'image_size' => $imageFile->getSize(),
                        'alt_text' => $request->image_titles[$index] ?? $imageFile->getClientOriginalName(),
                        'caption' => $request->image_descriptions[$index] ?? null,
                        'sort_order' => $project->images()->count() + $index + 1,
                        'is_featured' => false,
                        'created_by' => Auth::id()
                    ]);
                }
            }

            // Handle videos update
            if ($request->has('videos')) {
                $videos = $request->videos;
                
                // If videos is a JSON string, decode it
                if (is_string($videos)) {
                    $videos = json_decode($videos, true);
                }
                
                // Delete existing videos for this project (only the ones that are not file uploads)
                $project->videos()->whereIn('video_type', ['youtube', 'url'])->delete();
                
                // If we have valid videos array, process them
                if (is_array($videos) && !empty($videos)) {
                    foreach ($videos as $index => $videoData) {
                        if (is_array($videoData)) {
                            $videoRecord = [
                                'project_id' => $project->id,
                                'video_type' => $videoData['video_type'],
                                'title' => $videoData['title'],
                                'description' => $videoData['description'] ?? null,
                                'sort_order' => $index + 1,
                                'is_featured' => $index === 0,
                                'created_by' => Auth::id()
                            ];

                            if ($videoData['video_type'] === 'youtube') {
                                $videoRecord['youtube_url'] = $videoData['youtube_url'];
                            } elseif ($videoData['video_type'] === 'url') {
                                $videoRecord['video_url'] = $videoData['video_url'];
                            }

                            ProjectVideo::create($videoRecord);
                        }
                    }
                }
            }

            // Handle video file uploads (if new video files are being added)
            if ($request->hasFile('video_files')) {
                foreach ($request->file('video_files') as $index => $videoFile) {
                    $videoPath = $videoFile->store('projects/videos', 'public');
                    
                    ProjectVideo::create([
                        'project_id' => $project->id,
                        'video_type' => 'upload',
                        'video_path' => $videoPath,
                        'title' => 'Video ' . ($project->videos()->count() + $index + 1),
                        'sort_order' => $project->videos()->count() + $index + 1,
                        'created_by' => Auth::id()
                    ]);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Project updated successfully',
                'data' => $project->fresh(['units', 'images', 'videos', 'country'])
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Failed to update project: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified project
     */
    public function destroy(Project $project): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Delete associated files
            foreach ($project->images as $image) {
                if ($image->image_path) {
                    Storage::disk('public')->delete($image->image_path);
                }
            }

            foreach ($project->videos as $video) {
                if ($video->video_path) {
                    Storage::disk('public')->delete($video->video_path);
                }
                if ($video->thumbnail) {
                    Storage::disk('public')->delete($video->thumbnail);
                }
            }

            $project->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Project deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete project: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a specific project image (hard delete)
     */
    public function deleteImage(Project $project, $imageId): JsonResponse
    {
        try {
            $image = $project->images()->find($imageId);
            
            if (!$image) {
                return response()->json([
                    'success' => false,
                    'message' => 'Image not found'
                ], 404);
            }

            // Delete the physical file from storage
            if ($image->image_path && Storage::disk('public')->exists($image->image_path)) {
                Storage::disk('public')->delete($image->image_path);
            }

            // Hard delete the image record from database
            $image->forceDelete();

            return response()->json([
                'success' => true,
                'message' => 'Image deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get project statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => Project::getStatistics()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get projects for dropdown
     */
    public function dropdown(): JsonResponse
    {
        try {
            $projects = Project::select('id', 'title', 'property_type', 'status')
                ->available()
                ->orderBy('title')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $projects
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch projects dropdown: ' . $e->getMessage()
            ], 500);
        }
    }
}

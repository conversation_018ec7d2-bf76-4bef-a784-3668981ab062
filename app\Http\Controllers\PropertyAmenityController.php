<?php

namespace App\Http\Controllers;

use App\Models\PropertyAmenity;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Exception;

class PropertyAmenityController extends Controller
{
    /**
     * Display a listing of property amenities.
     */
    public function index(Request $request)
    {
        try {
            $query = PropertyAmenity::with(['creator', 'updater']);

            // Search functionality
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('description', 'LIKE', "%{$search}%")
                      ->orWhere('category', 'LIKE', "%{$search}%");
                });
            }

            // Filter by category
            if ($request->has('category') && !empty($request->category)) {
                $query->byCategory($request->category);
            }

            // Filter by status
            if ($request->has('status') && $request->status !== '') {
                $query->where('is_active', $request->status);
            }

            // Sort functionality
            $sortBy = $request->get('sort_by', 'sort_order');
            $sortOrder = $request->get('sort_order', 'asc');
            
            if (in_array($sortBy, ['name', 'category', 'sort_order', 'created_at', 'updated_at'])) {
                $query->orderBy($sortBy, $sortOrder);
            } else {
                $query->ordered();
            }

            // Pagination
            $perPage = $request->get('per_page', 15);
            $amenities = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $amenities,
                'categories' => PropertyAmenity::CATEGORIES,
                'icons' => PropertyAmenity::COMMON_ICONS
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch property amenities',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created property amenity.
     */
    public function store(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:property_amenities,name',
                'description' => 'nullable|string|max:1000',
                'icon' => 'nullable|string|max:100',
                'category' => 'required|string|in:' . implode(',', array_keys(PropertyAmenity::CATEGORIES)),
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $amenity = PropertyAmenity::create([
                'name' => $request->name,
                'description' => $request->description,
                'icon' => $request->icon,
                'category' => $request->category,
                'is_active' => $request->get('is_active', true),
                'sort_order' => $request->get('sort_order', 0),
                'created_by' => Auth::id()
            ]);

            $amenity->load(['creator', 'updater']);

            return response()->json([
                'success' => true,
                'message' => 'Property amenity created successfully',
                'data' => $amenity
            ], 201);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create property amenity',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified property amenity.
     */
    public function show($id)
    {
        try {
            $amenity = PropertyAmenity::with(['creator', 'updater'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $amenity
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Property amenity not found',
                'error' => $e->getMessage()
            ], 404);
        }
    }

    /**
     * Update the specified property amenity.
     */
    public function update(Request $request, $id)
    {
        try {
            $amenity = PropertyAmenity::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:property_amenities,name,' . $id,
                'description' => 'nullable|string|max:1000',
                'icon' => 'nullable|string|max:100',
                'category' => 'required|string|in:' . implode(',', array_keys(PropertyAmenity::CATEGORIES)),
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $amenity->update([
                'name' => $request->name,
                'description' => $request->description,
                'icon' => $request->icon,
                'category' => $request->category,
                'is_active' => $request->get('is_active', true),
                'sort_order' => $request->get('sort_order', 0),
                'updated_by' => Auth::id()
            ]);

            $amenity->load(['creator', 'updater']);

            return response()->json([
                'success' => true,
                'message' => 'Property amenity updated successfully',
                'data' => $amenity
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update property amenity',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified property amenity.
     */
    public function destroy($id)
    {
        try {
            $amenity = PropertyAmenity::findOrFail($id);
            $amenity->delete();

            return response()->json([
                'success' => true,
                'message' => 'Property amenity deleted successfully'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete property amenity',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk update amenities status.
     */
    public function bulkUpdateStatus(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'ids' => 'required|array',
                'ids.*' => 'integer|exists:property_amenities,id',
                'is_active' => 'required|boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            PropertyAmenity::whereIn('id', $request->ids)
                ->update([
                    'is_active' => $request->is_active,
                    'updated_by' => Auth::id()
                ]);

            return response()->json([
                'success' => true,
                'message' => 'Amenities status updated successfully'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update amenities status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get active amenities for dropdown.
     */
    public function getActiveAmenities()
    {
        try {
            $amenities = PropertyAmenity::active()
                ->ordered()
                ->select('id', 'name', 'icon', 'category')
                ->get()
                ->groupBy('category');

            return response()->json([
                'success' => true,
                'data' => $amenities,
                'categories' => PropertyAmenity::CATEGORIES
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch active amenities',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reorder amenities.
     */
    public function reorder(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'amenities' => 'required|array',
                'amenities.*.id' => 'required|integer|exists:property_amenities,id',
                'amenities.*.sort_order' => 'required|integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            foreach ($request->amenities as $amenityData) {
                PropertyAmenity::where('id', $amenityData['id'])
                    ->update([
                        'sort_order' => $amenityData['sort_order'],
                        'updated_by' => Auth::id()
                    ]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Amenities reordered successfully'
            ]);

        } catch (Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder amenities',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}

<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        then: function () {
            Route::middleware('web')
                ->group(base_path('routes/debug.php'));
        },
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->alias([
            'permission' => \App\Http\Middleware\CheckPermission::class,
            'production.cors' => \App\Http\Middleware\ProductionCorsMiddleware::class,
            'production.api' => \App\Http\Middleware\ProductionApiMiddleware::class,
            'dynamic.url' => \App\Http\Middleware\DynamicAppUrlMiddleware::class,
        ]);
        
        // Add dynamic URL middleware for all requests
        $middleware->append(\App\Http\Middleware\DynamicAppUrlMiddleware::class);
        
        // Add CORS headers for API
        $middleware->api(prepend: [
            \Illuminate\Http\Middleware\HandleCors::class,
        ]);
        
        // Add production middleware for API routes (check environment properly)
        if (env('APP_ENV') === 'production') {
            $middleware->api(append: [
                \App\Http\Middleware\ProductionCorsMiddleware::class,
                \App\Http\Middleware\ProductionApiMiddleware::class,
            ]);
        }
        
        // Trust all proxies in production (adjust for your specific setup)
        $middleware->trustProxies(at: '*');
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        // Handle API authentication errors properly
        $exceptions->render(function (\Illuminate\Auth\AuthenticationException $e, $request) {
            // Check if this is an API request by multiple methods
            if ($request->is('api/*') || 
                $request->expectsJson() || 
                $request->wantsJson() ||
                $request->header('Accept') === 'application/json' ||
                $request->header('X-Requested-With') === 'XMLHttpRequest') {
                
                return response()->json([
                    'success' => false,
                    'message' => 'Please use the API endpoint /api/auth/login for authentication',
                    'login_endpoint' => '/api/auth/login',
                    'error' => 'Authentication required'
                ], 401);
            }
            
            // For web routes, redirect to login
            return redirect()->guest('/login');
        });
    })->create();

<?php

namespace App\Http\Controllers;

use App\Models\UnitType;
use App\Models\UnitDetail;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class UnitTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $projectId = $request->get('project_id');
        $includeInactive = $request->boolean('include_inactive', false);
        
        $query = UnitType::with('project');
        
        if ($projectId) {
            $query->where('project_id', $projectId);
        }
        
        // Only filter by is_active if we're not including inactive ones
        if (!$includeInactive) {
            $query->where('is_active', true);
        }
        
        $unitTypes = $query->orderBy('type_name', 'asc')
                          ->get();
        
        return response()->json([
            'status' => 'success',
            'data' => $unitTypes
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'type_name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('unit_types')->where('project_id', $request->project_id)
            ],
            'total_size_sqft' => 'nullable|numeric|min:0',
            'flat_size' => 'nullable|numeric|min:0',
            'total_bed_room' => 'nullable|integer|min:0|max:20',
            'total_bathroom' => 'nullable|integer|min:0|max:20',
            'description' => 'nullable|string',
            'base_price' => 'nullable|numeric|min:0',
            'is_active' => 'boolean',
            'auto_generate_units' => 'boolean' // Option to auto-generate units
        ]);

        // Start database transaction
        DB::beginTransaction();

        try {
            $unitType = UnitType::create($validated);
            
            // Auto-generate unit details if requested
            if ($request->boolean('auto_generate_units', true)) { // Default to true
                $this->generateUnitDetails($unitType);
            }

            $unitType->load('project');

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Unit type created successfully with auto-generated units',
                'data' => $unitType
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create unit type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Auto-generate unit details based on project floors and units per floor
     */
    private function generateUnitDetails(UnitType $unitType)
    {
        $project = $unitType->project;
        
        // Get project details
        $totalUnits = $project->total_unit ?? 1; // Get total units from project
        $unitsPerFloor = $project->unit_per_floor ?? 1; // Default to 1 unit per floor
        
        // Calculate total floors by dividing total units by units per floor
        $totalFloors = ceil($totalUnits / $unitsPerFloor); // Use ceil to round up
        
        $generatedUnits = [];
        
        // Generate units for each floor
        for ($floor = 1; $floor <= $totalFloors; $floor++) {
            // Use unit type as suffix (e.g., Studio, 1BR, 2BR, etc.)
            $typeSuffix = $unitType->type_name;
            
            // Generate unit number (e.g., 1Studio, 2Studio, 3Studio, etc.)
            $unitNumber = $floor . $typeSuffix;
            
            // Generate unit name
            $unitName = "{$unitType->type_name} - Floor {$floor}";
            
            $unitDetail = UnitDetail::create([
                'project_id' => $project->id,
                'unit_name' => $unitName,
                'unit_type' => $unitType->type_name,
                'status' => 'available',
                'floor_number' => $floor,
                'unit_number' => $unitNumber
            ]);
            
            $generatedUnits[] = $unitDetail;
        }
        
        return $generatedUnits;
    }

    /**
     * Display the specified resource.
     */
    public function show(UnitType $unitType)
    {
        $unitType->load('project');
        
        return response()->json([
            'status' => 'success',
            'data' => $unitType
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, UnitType $unitType)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'type_name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('unit_types')->where('project_id', $request->project_id)->ignore($unitType->id)
            ],
            'total_size_sqft' => 'nullable|numeric|min:0',
            'flat_size' => 'nullable|numeric|min:0',
            'total_bed_room' => 'nullable|integer|min:0|max:20',
            'total_bathroom' => 'nullable|integer|min:0|max:20',
            'description' => 'nullable|string',
            'base_price' => 'nullable|numeric|min:0',
            'is_active' => 'boolean'
        ]);

        $unitType->update($validated);
        $unitType->load('project');

        return response()->json([
            'status' => 'success',
            'message' => 'Unit type updated successfully',
            'data' => $unitType
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(UnitType $unitType)
    {
        // Start database transaction
        DB::beginTransaction();

        try {
            // Delete all associated unit details first
            UnitDetail::where('project_id', $unitType->project_id)
                     ->where('unit_type', $unitType->type_name)
                     ->delete();

            // Then delete the unit type
            $unitType->delete();

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Unit type and associated units deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete unit type: ' . $e->getMessage()
            ], 500);
        }
    }
}

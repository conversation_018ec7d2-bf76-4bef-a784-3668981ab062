<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('land_acquisitions', function (Blueprint $table) {
            // Check if land_size column doesn't exist and add it
            if (!Schema::hasColumn('land_acquisitions', 'land_size')) {
                $table->decimal('land_size', 10, 2)->after('mouza');
            }
            
            // Check if acquisition_price column doesn't exist and add it
            if (!Schema::hasColumn('land_acquisitions', 'acquisition_price')) {
                $table->decimal('acquisition_price', 15, 2)->after('land_size');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('land_acquisitions', function (Blueprint $table) {
            $table->dropColumn(['land_size', 'acquisition_price']);
        });
    }
};

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Nwidart\Modules\Facades\Module;

class ModuleAvailabilityController extends Controller
{
    /**
     * Get available modules by scanning the Modules folder and checking system modules
     */
    public function getAvailableModules(): JsonResponse
    {
        try {
            $availableModules = [];
            
            // First, scan the Modules folder for Laravel modules
            $modulesPath = base_path('Modules');
            if (is_dir($modulesPath)) {
                $moduleDirectories = array_filter(glob($modulesPath . '/*'), 'is_dir');
                
                foreach ($moduleDirectories as $moduleDir) {
                    $moduleName = basename($moduleDir);
                    $moduleJsonPath = $moduleDir . '/module.json';
                    
                    $moduleData = [
                        'name' => $moduleName,
                        'key' => strtolower(str_replace(['_', ' '], '-', $moduleName)),
                        'available' => false,
                        'controller_exists' => false,
                        'model_exists' => false,
                        'is_laravel_module' => true,
                        'errors' => [],
                        'path' => $moduleDir,
                        'version' => 'Unknown',
                        'description' => ''
                    ];
                    
                    // Read module.json if it exists
                    if (file_exists($moduleJsonPath)) {
                        try {
                            $moduleJson = json_decode(file_get_contents($moduleJsonPath), true);
                            if ($moduleJson) {
                                $moduleData['name'] = $moduleJson['name'] ?? $moduleName;
                                $moduleData['version'] = $moduleJson['version'] ?? '1.0.0';
                                $moduleData['description'] = $moduleJson['description'] ?? '';
                                $moduleData['key'] = strtolower(str_replace(['_', ' '], '-', $moduleData['name']));
                            }
                        } catch (\Exception $e) {
                            $moduleData['errors'][] = "Failed to read module.json: " . $e->getMessage();
                        }
                    }
                    
                    // Check Laravel module status
                    try {
                        $laravelModule = Module::find($moduleName);
                        if ($laravelModule) {
                            $moduleData['available'] = $laravelModule->isEnabled();
                            $moduleData['controller_exists'] = true; // Laravel modules typically have controllers
                            $moduleData['model_exists'] = true; // Assume models exist for enabled modules
                        }
                    } catch (\Exception $e) {
                        $moduleData['errors'][] = "Laravel module check failed: " . $e->getMessage();
                    }
                    
                    $availableModules[$moduleData['key']] = $moduleData;
                }
            }
            
            // Add system/app modules with their specific requirements
            $systemModules = [
                'Projects' => [
                    'controller' => 'App\\Http\\Controllers\\ProjectController',
                    'model' => 'App\\Models\\Project',
                    'key' => 'project'
                ],
                'Employees' => [
                    'controller' => 'App\\Http\\Controllers\\EmployeeController',
                    'model' => 'App\\Models\\Employee',
                    'key' => 'employees'
                ],
                'Contractors' => [
                    'controller' => 'App\\Http\\Controllers\\ContractorController',
                    'model' => 'App\\Models\\Contractor',
                    'key' => 'contractors'
                ],
                'Vendors' => [
                    'controller' => 'App\\Http\\Controllers\\VendorController',
                    'model' => 'App\\Models\\Vendor',
                    'key' => 'vendor'
                ],
                'VendorTypes' => [
                    'controller' => 'App\\Http\\Controllers\\VendorTypeController',
                    'model' => 'App\\Models\\VendorType',
                    'key' => 'vendor-type'
                ],
                'PropertyTypes' => [
                    'controller' => 'App\\Http\\Controllers\\PropertyTypeController',
                    'model' => 'App\\Models\\PropertyType',
                    'key' => 'property-type'
                ],
                'PropertyStatus' => [
                    'controller' => 'App\\Http\\Controllers\\PropertyStatusController',
                    'model' => 'App\\Models\\PropertyStatus',
                    'key' => 'property-status'
                ],
                'PropertyAmenities' => [
                    'controller' => 'App\\Http\\Controllers\\PropertyAmenityController',
                    'model' => 'App\\Models\\PropertyAmenity',
                    'key' => 'property-amenity'
                ],
                'AssignContractor' => [
                    'controller' => 'App\\Http\\Controllers\\ProjectContractorController',
                    'model' => 'App\\Models\\ProjectContractor',
                    'key' => 'assign-contractor'
                ],
                'AssignVendor' => [
                    'controller' => 'App\\Http\\Controllers\\ProjectVendorController',
                    'model' => 'App\\Models\\ProjectVendor',
                    'key' => 'assign-vendor'
                ],
                'AssignEmployee' => [
                    'controller' => 'App\\Http\\Controllers\\ProjectEmployeeController',
                    'model' => 'App\\Models\\ProjectEmployee',
                    'key' => 'assign-employee'
                ]
            ];

            
            foreach ($systemModules as $moduleName => $requirements) {
                // Skip if already added as Laravel module
                if (isset($availableModules[$requirements['key']])) {
                    continue;
                }
                
                $moduleData = [
                    'name' => $moduleName,
                    'key' => $requirements['key'],
                    'available' => false,
                    'controller_exists' => false,
                    'model_exists' => false,
                    'is_laravel_module' => false,
                    'errors' => [],
                    'version' => '1.0.0',
                    'description' => 'System module'
                ];

                // Check if controller exists
                try {
                    if (class_exists($requirements['controller'])) {
                        $moduleData['controller_exists'] = true;
                    } else {
                        $moduleData['errors'][] = "Controller {$requirements['controller']} not found";
                    }
                } catch (\Exception $e) {
                    $moduleData['errors'][] = "Controller check failed: " . $e->getMessage();
                }

                // Check if model exists
                try {
                    if (class_exists($requirements['model'])) {
                        $moduleData['model_exists'] = true;
                    } else {
                        $moduleData['errors'][] = "Model {$requirements['model']} not found";
                    }
                } catch (\Exception $e) {
                    $moduleData['errors'][] = "Model check failed: " . $e->getMessage();
                }

                // Module is available if both controller and model exist
                $moduleData['available'] = $moduleData['controller_exists'] && $moduleData['model_exists'];

                $availableModules[$requirements['key']] = $moduleData;
            }

        // Always available core modules
        $coreModules = [
            'dashboard' => [
                'name' => 'Dashboard',
                'key' => 'dashboard',
                'available' => true,
                'controller_exists' => true,
                'model_exists' => true,
                'is_laravel_module' => false,
                'errors' => []
            ],
            'land-acquisition' => [
                'name' => 'LandAcquisition',
                'key' => 'land-acquisition',
                'available' => class_exists('App\\Http\\Controllers\\LandAcquisitionController') && class_exists('App\\Models\\LandAcquisition'),
                'controller_exists' => class_exists('App\\Http\\Controllers\\LandAcquisitionController'),
                'model_exists' => class_exists('App\\Models\\LandAcquisition'),
                'is_laravel_module' => false,
                'errors' => []
            ],
            'role' => [
                'name' => 'RoleManagement',
                'key' => 'role',
                'available' => class_exists('App\\Http\\Controllers\\RoleController') && class_exists('App\\Models\\Role'),
                'controller_exists' => class_exists('App\\Http\\Controllers\\RoleController'),
                'model_exists' => class_exists('App\\Models\\Role'),
                'is_laravel_module' => false,
                'errors' => []
            ],
            'settings' => [
                'name' => 'Settings',
                'key' => 'settings',
                'available' => true,
                'controller_exists' => true,
                'model_exists' => true,
                'is_laravel_module' => false,
                'errors' => []
            ]
        ];

        $availableModules = array_merge($coreModules, $availableModules);

        // Convert associative array to indexed array for frontend compatibility
        $modulesList = [];
        foreach ($availableModules as $key => $module) {
            $module['key'] = $key; // Ensure the key is set
            $modulesList[] = $module;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'modules' => $modulesList,
                'available_keys' => array_keys(array_filter($availableModules, fn($module) => $module['available'])),
                'total_modules' => count($availableModules),
                'available_count' => count(array_filter($availableModules, fn($module) => $module['available']))
            ]
        ]);
        
        } catch (\Exception $e) {
            // If module detection completely fails, return core modules only
            $coreModules = [
                'dashboard' => [
                    'name' => 'Dashboard',
                    'key' => 'dashboard',
                    'available' => true,
                    'controller_exists' => true,
                    'model_exists' => true,
                    'is_laravel_module' => false,
                    'errors' => []
                ],
                'land-acquisition' => [
                    'name' => 'LandAcquisition',
                    'key' => 'land-acquisition',
                    'available' => true,
                    'controller_exists' => true,
                    'model_exists' => true,
                    'is_laravel_module' => false,
                    'errors' => []
                ],
                'role' => [
                    'name' => 'RoleManagement',
                    'key' => 'role',
                    'available' => true,
                    'controller_exists' => true,
                    'model_exists' => true,
                    'is_laravel_module' => false,
                    'errors' => []
                ],
                'settings' => [
                    'name' => 'Settings',
                    'key' => 'settings',
                    'available' => true,
                    'controller_exists' => true,
                    'model_exists' => true,
                    'is_laravel_module' => false,
                    'errors' => []
                ]
            ];

            // Convert to array format for frontend compatibility
            $modulesList = [];
            foreach ($coreModules as $key => $module) {
                $module['key'] = $key;
                $modulesList[] = $module;
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'modules' => $modulesList,
                    'available_keys' => array_keys($coreModules),
                    'total_modules' => count($coreModules),
                    'available_count' => count($coreModules),
                    'error' => 'Module detection failed: ' . $e->getMessage()
                ]
            ]);
        }
    }

    /**
     * Check if a specific module is available
     */
    public function checkModule(string $moduleKey): JsonResponse
    {
        $allModules = $this->getAvailableModules()->getData(true);
        $modules = $allModules['data']['modules'];

        if (!isset($modules[$moduleKey])) {
            return response()->json([
                'success' => false,
                'message' => 'Module not recognized',
                'data' => null
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $modules[$moduleKey]
        ]);
    }

    /**
     * Get sidebar menu configuration based on available modules
     */
    public function getSidebarConfig(): JsonResponse
    {
        $availableModules = $this->getAvailableModules()->getData(true)['data']['modules'];
        
        // Extract actual module keys from available modules
        $availableKeys = [];
        foreach ($availableModules as $module) {
            if (isset($module['available']) && $module['available']) {
                $availableKeys[] = $module['key'];
            }
        }

        // Define the complete sidebar structure
        $sidebarConfig = [
            'main_navigation' => [],
            'documents_navigation' => [],
            'hr_management' => []
        ];

        // Main navigation items
        $mainNavItems = [
            'dashboard' => ['order' => 1, 'required' => true],
            'analytics' => ['order' => 2, 'required' => false],
            'land-owners' => ['order' => 3, 'required' => false],
            'land-acquisition' => ['order' => 4, 'required' => false],
            'project' => ['order' => 5, 'required' => false],
            'property-status' => ['order' => 6, 'required' => false],
            'lifecycle' => ['order' => 7, 'required' => false],
            'customers' => ['order' => 8, 'required' => false],
            'orders' => ['order' => 9, 'required' => false]
        ];

        // Documents/Admin navigation items
        $documentsNavItems = [
            'vendor-type' => ['order' => 1, 'required' => false],
            'vendor' => ['order' => 2, 'required' => false],
            'components' => ['order' => 3, 'required' => false],
            'reports' => ['order' => 4, 'required' => false],
            'word-assistant' => ['order' => 5, 'required' => false],
            'role' => ['order' => 6, 'required' => true],
            'settings' => ['order' => 7, 'required' => true]
        ];

        // Human Resources Management - Development Process navigation items
        $hrManagementItems = [
            'employees' => ['order' => 1, 'required' => false],
            'contractors' => ['order' => 2, 'required' => false],
            'assign-contractor' => ['order' => 3, 'required' => false],
            'assign-vendor' => ['order' => 4, 'required' => false],
            'assign-employee' => ['order' => 5, 'required' => false]
        ];

        // Filter main navigation based on availability
        foreach ($mainNavItems as $key => $config) {
            if ($config['required'] || in_array($key, $availableKeys)) {
                $sidebarConfig['main_navigation'][] = [
                    'key' => $key,
                    'available' => in_array($key, $availableKeys),
                    'required' => $config['required'],
                    'order' => $config['order']
                ];
            }
        }

        // Filter documents navigation based on availability
        foreach ($documentsNavItems as $key => $config) {
            if ($config['required'] || in_array($key, $availableKeys)) {
                $sidebarConfig['documents_navigation'][] = [
                    'key' => $key,
                    'available' => in_array($key, $availableKeys),
                    'required' => $config['required'],
                    'order' => $config['order']
                ];
            }
        }

        // Filter HR Management navigation based on availability
        foreach ($hrManagementItems as $key => $config) {
            if ($config['required'] || in_array($key, $availableKeys)) {
                $sidebarConfig['hr_management'][] = [
                    'key' => $key,
                    'available' => in_array($key, $availableKeys),
                    'required' => $config['required'],
                    'order' => $config['order']
                ];
            }
        }

        // Sort by order
        usort($sidebarConfig['main_navigation'], fn($a, $b) => $a['order'] <=> $b['order']);
        usort($sidebarConfig['documents_navigation'], fn($a, $b) => $a['order'] <=> $b['order']);
        usort($sidebarConfig['hr_management'], fn($a, $b) => $a['order'] <=> $b['order']);

        return response()->json([
            'success' => true,
            'data' => [
                'sidebar_config' => $sidebarConfig,
                'available_modules' => $availableKeys,
                'module_details' => $availableModules
            ]
        ]);
    }
}

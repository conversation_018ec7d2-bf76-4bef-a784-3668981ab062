<?php

namespace App\Http\Controllers;

use App\Models\Currency;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;

class CurrencyController extends Controller
{
    /**
     * Display a listing of currencies.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Currency::query();

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('code', 'like', "%{$search}%")
                      ->orWhere('symbol', 'like', "%{$search}%");
                });
            }

            // Filter by active status
            if ($request->has('active') && $request->active !== '') {
                $query->where('is_active', $request->boolean('active'));
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'name');
            $sortOrder = $request->get('sort_order', 'asc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 10);
            $currencies = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $currencies
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch currencies: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created currency.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'code' => 'required|string|size:3|unique:currencies,code',
                'symbol' => 'required|string|max:10',
                'exchange_rate' => 'required|numeric|min:0.0001',
                'is_default' => 'boolean',
                'is_active' => 'boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // If this is set as default, unset all other defaults
            if ($request->get('is_default', false)) {
                Currency::where('is_default', true)->update(['is_default' => false]);
            }

            $currency = Currency::create($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Currency created successfully',
                'data' => $currency
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create currency: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified currency.
     */
    public function show(Currency $currency): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => $currency
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch currency: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified currency.
     */
    public function update(Request $request, Currency $currency): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'code' => 'required|string|size:3|unique:currencies,code,' . $currency->id,
                'symbol' => 'required|string|max:10',
                'exchange_rate' => 'required|numeric|min:0.0001',
                'is_default' => 'boolean',
                'is_active' => 'boolean'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            // If this is set as default, unset all other defaults
            if ($request->get('is_default', false)) {
                Currency::where('is_default', true)->where('id', '!=', $currency->id)->update(['is_default' => false]);
            }

            $currency->update($request->all());

            return response()->json([
                'success' => true,
                'message' => 'Currency updated successfully',
                'data' => $currency
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update currency: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified currency.
     */
    public function destroy(Currency $currency): JsonResponse
    {
        try {
            // Don't allow deletion of default currency
            if ($currency->is_default) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete the default currency'
                ], 400);
            }

            $currency->delete();

            return response()->json([
                'success' => true,
                'message' => 'Currency deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete currency: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get currency statistics.
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $stats = [
                'total_currencies' => Currency::count(),
                'active_currencies' => Currency::active()->count(),
                'inactive_currencies' => Currency::where('is_active', false)->count(),
                'default_currency' => Currency::default()->first(),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle currency status.
     */
    public function toggleStatus(Currency $currency): JsonResponse
    {
        try {
            $currency->is_active = !$currency->is_active;
            $currency->save();

            return response()->json([
                'success' => true,
                'message' => 'Currency status updated successfully',
                'data' => $currency
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update currency status: ' . $e->getMessage()
            ], 500);
        }
    }
}

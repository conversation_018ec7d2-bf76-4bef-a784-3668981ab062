import React, { useState, useEffect } from 'react';
import contractorAPI from '../../services/contractorAPI';
import Swal from 'sweetalert2';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { ContentModal, ContentModalContent, ContentModalHeader, ContentModalTitle, ContentModalOverlay } from '../ui/content-modal';
import StandardModal from '@/components/ui/StandardModal';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Textarea } from '../ui/textarea';
import { Search, Plus, Edit2, Trash2, Users, UserCheck, UserX, Filter, ChevronLeft, ChevronRight, HardHat, Phone, Mail, FileText, MapPin, Upload, X, Download } from 'lucide-react';

const ContractorPage = () => {
  const [contractors, setContractors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState({});
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingContractor, setEditingContractor] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    trade_license: '',
    email: '',
    address: '',
    status: 'active'
  });
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [existingFiles, setExistingFiles] = useState([]);

  // Pagination and filtering state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [sortBy, setSortBy] = useState('id');
  const [sortOrder, setSortOrder] = useState('desc');

  // Fetch contractors
  const fetchContractors = async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        per_page: perPage,
        search: searchTerm,
        status: statusFilter,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      const response = await contractorAPI.getContractors(params);
      setContractors(response.data.data.data);
      setCurrentPage(response.data.data.current_page);
      setTotalPages(response.data.data.last_page);
    } catch (error) {
      console.error('Error fetching contractors:', error);
      Swal.fire({
        title: 'Error!',
        text: 'Failed to fetch contractors',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const response = await contractorAPI.getContractorStatistics();
      setStatistics(response.data.data);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  useEffect(() => {
    fetchContractors(1);
    fetchStatistics();
  }, [perPage, searchTerm, statusFilter, sortBy, sortOrder]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    fetchContractors(1);
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      phone: '',
      trade_license: '',
      email: '',
      address: '',
      status: 'active'
    });
    setSelectedFiles([]);
    setExistingFiles([]);
  };

  // Handle file selection
  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setSelectedFiles(prevFiles => [...prevFiles, ...files]);
  };

  // Remove selected file
  const removeSelectedFile = (index) => {
    setSelectedFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
  };

  // Remove existing file
  const removeExistingFile = (index) => {
    setExistingFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
  };

  // Handle add contractor
  const handleAddContractor = async (e) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.name.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Name is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.phone.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Phone is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.trade_license.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Trade License is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.email.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Email is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    
    try {
      const formDataToSend = new FormData();
      
      // Add fields
      Object.keys(formData).forEach(key => {
        if (formData[key] !== null && formData[key] !== '') {
          formDataToSend.append(key, formData[key]);
        }
      });

      // Add files
      selectedFiles.forEach((file, index) => {
        formDataToSend.append(`files[${index}]`, file);
      });

      await contractorAPI.createContractor(formDataToSend);
      
      // Close modal and reset form first
      setShowAddModal(false);
      resetForm();
      fetchContractors();
      fetchStatistics();
      
      // Show success message
      Swal.fire({
        title: 'Success!',
        text: 'Contractor created successfully',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error creating contractor:', error);
      Swal.fire({
        title: 'Error!',
        text: error.response?.data?.message || 'Failed to create contractor',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
    }
  };

  // Handle edit contractor
  const handleEditContractor = (contractor) => {
    setEditingContractor(contractor);
    setFormData({
      name: contractor.name || '',
      phone: contractor.phone || '',
      trade_license: contractor.trade_license || '',
      email: contractor.email || '',
      address: contractor.address || '',
      status: contractor.status || 'active'
    });
    setSelectedFiles([]);
    setExistingFiles(contractor.files || []);
    setShowEditModal(true);
  };

  // Handle update contractor
  const handleUpdateContractor = async (e) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.name.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Name is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.phone.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Phone is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.trade_license.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Trade License is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.email.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Email is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    
    try {
      const formDataToSend = new FormData();
      
      // Add fields
      Object.keys(formData).forEach(key => {
        if (formData[key] !== null && formData[key] !== '') {
          formDataToSend.append(key, formData[key]);
        }
      });

      // Add new files
      selectedFiles.forEach((file, index) => {
        formDataToSend.append(`files[${index}]`, file);
      });

      // Add existing files to keep as an array
      existingFiles.forEach((file, index) => {
        const fileId = file.id || file;
        formDataToSend.append(`existing_files[]`, fileId);
      });

      console.log('Sending contractor update request:', {
        contractorId: editingContractor.id,
        formDataEntries: Array.from(formDataToSend.entries())
      });

      await contractorAPI.updateContractor(editingContractor.id, formDataToSend);
      
      // Close modal and reset form first
      setShowEditModal(false);
      setEditingContractor(null);
      resetForm();
      fetchContractors();
      fetchStatistics();
      
      // Show success message
      Swal.fire({
        title: 'Success!',
        text: 'Contractor updated successfully',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error updating contractor:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error headers:', error.response?.headers);
      
      let errorMessage = 'Failed to update contractor';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.errors) {
        const errorMessages = Object.values(error.response.data.errors).flat();
        errorMessage = errorMessages.join(', ');
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      Swal.fire({
        title: 'Error!',
        text: errorMessage,
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
    }
  };

  // Handle delete contractor
  const handleDeleteContractor = async (contractor) => {
    const result = await Swal.fire({
      title: 'Delete Contractor',
      text: `Are you sure you want to delete ${contractor.name}? This action cannot be undone.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel'
    });

    if (result.isConfirmed) {
      try {
        await contractorAPI.deleteContractor(contractor.id);
        fetchContractors();
        fetchStatistics();
        
        Swal.fire({
          title: 'Deleted!',
          text: 'Contractor has been deleted successfully.',
          icon: 'success',
          timer: 2000,
          showConfirmButton: false
        });
      } catch (error) {
        console.error('Error deleting contractor:', error);
        Swal.fire({
          title: 'Error!',
          text: 'Failed to delete contractor',
          icon: 'error',
          confirmButtonColor: '#ef4444'
        });
      }
    }
  };

  // Handle status toggle
  const handleStatusToggle = async (contractor) => {
    const newStatus = contractor.status === 'active' ? 'inactive' : 'active';
    
    try {
      const formData = new FormData();
      formData.append('name', contractor.name);
      formData.append('phone', contractor.phone);
      formData.append('trade_license', contractor.trade_license);
      formData.append('email', contractor.email);
      formData.append('address', contractor.address || '');
      formData.append('status', newStatus);

      await contractorAPI.updateContractor(contractor.id, formData);
      fetchContractors();
      fetchStatistics();
      
      Swal.fire({
        title: 'Success!',
        text: `Contractor ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`,
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error toggling status:', error);
      Swal.fire({
        title: 'Error!',
        text: 'Failed to update contractor status',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <HardHat className="w-8 h-8 mr-3 text-blue-600" />
            Contractor Management
          </h1>
          <p className="text-gray-600 mt-1">Manage construction contractors and their business information</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700" onClick={() => setShowAddModal(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Add Contractor
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-800">Total Contractors</CardTitle>
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <HardHat className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900">{statistics.total_contractors || 0}</div>
            <p className="text-xs text-blue-600 mt-1">All registered contractors</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-800">Active Contractors</CardTitle>
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
              <UserCheck className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">{statistics.active_contractors || 0}</div>
            <p className="text-xs text-green-600 mt-1">Currently available for projects</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-800">Inactive Contractors</CardTitle>
            <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
              <UserX className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-900">{statistics.inactive_contractors || 0}</div>
            <p className="text-xs text-red-600 mt-1">Not currently active</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="border-gray-200 shadow-sm">
        <CardHeader className="bg-gray-50 border-b">
          <CardTitle className="flex items-center text-lg">
            <Filter className="w-5 h-5 mr-2 text-blue-600" />
            Search & Filter Contractors
          </CardTitle>
          <CardDescription>
            Find contractors by name, license number, contact information or status
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search" className="text-sm font-medium text-gray-700">Search</Label>
                <div className="relative mt-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="search"
                    type="text"
                    placeholder="Search contractors..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="status_filter" className="text-sm font-medium text-gray-700">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="sort_by" className="text-sm font-medium text-gray-700">Sort By</Label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Company Name</SelectItem>
                    <SelectItem value="trade_license">Trade License</SelectItem>
                    <SelectItem value="phone">Phone</SelectItem>
                    <SelectItem value="email">Email</SelectItem>
                    <SelectItem value="status">Status</SelectItem>
                    <SelectItem value="created_at">Date Added</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="sort_order" className="text-sm font-medium text-gray-700">Order</Label>
                <Select value={sortOrder} onValueChange={setSortOrder}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">Ascending</SelectItem>
                    <SelectItem value="desc">Descending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                <Search className="w-4 h-4 mr-2" />
                Apply Filters
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  setSortBy('name');
                  setSortOrder('asc');
                }}
              >
                Clear Filters
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Contractors Table */}
      <Card>
        <CardHeader>
          <CardTitle>Contractors</CardTitle>
          <CardDescription>Manage your contractors and their information</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full table-auto">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Company Details</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Contact Info</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Trade License</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {contractors.map((contractor) => (
                      <tr key={contractor.id} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-start">
                            <div className="flex-shrink-0">
                              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <HardHat className="w-5 h-5 text-blue-600" />
                              </div>
                            </div>
                            <div className="ml-3">
                              <div className="font-medium text-gray-900">{contractor.name}</div>
                              {contractor.address && (
                                <div className="text-sm text-gray-500 flex items-center mt-1">
                                  <MapPin className="w-3 h-3 mr-1" />
                                  {contractor.address.length > 50 
                                    ? `${contractor.address.substring(0, 50)}...` 
                                    : contractor.address
                                  }
                                </div>
                              )}
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="space-y-1">
                            <div className="flex items-center text-sm text-gray-700">
                              <Phone className="w-3 h-3 mr-2 text-gray-400" />
                              {contractor.phone}
                            </div>
                            <div className="flex items-center text-sm text-gray-700">
                              <Mail className="w-3 h-3 mr-2 text-gray-400" />
                              {contractor.email}
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <FileText className="w-4 h-4 mr-2 text-gray-400" />
                            <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded border">
                              {contractor.trade_license}
                            </span>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <Badge 
                            variant={contractor.status === 'active' ? 'default' : 'secondary'}
                            className={contractor.status === 'active' 
                              ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                              : 'bg-red-100 text-red-800 hover:bg-red-200'
                            }
                          >
                            {contractor.status === 'active' ? (
                              <UserCheck className="w-3 h-3 mr-1" />
                            ) : (
                              <UserX className="w-3 h-3 mr-1" />
                            )}
                            {contractor.status}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditContractor(contractor)}
                              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            >
                              <Edit2 className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleStatusToggle(contractor)}
                              className={contractor.status === 'active' 
                                ? 'text-orange-600 hover:text-orange-700 hover:bg-orange-50' 
                                : 'text-green-600 hover:text-green-700 hover:bg-green-50'
                              }
                            >
                              {contractor.status === 'active' ? 'Deactivate' : 'Activate'}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteContractor(contractor)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-gray-700">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchContractors(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="w-4 h-4" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchContractors(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Contractor Modal */}
      <ContentModal open={showAddModal} onOpenChange={setShowAddModal}>
        <ContentModalOverlay />
        <ContentModalContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <ContentModalHeader>
            <ContentModalTitle className="flex items-center">
              <HardHat className="w-5 h-5 mr-2" />
              Add New Contractor
            </ContentModalTitle>
            <p className="text-sm text-muted-foreground">
              Create a new contractor record with their business information and trade license details.
            </p>
          </ContentModalHeader>
          <form onSubmit={handleAddContractor} className="space-y-4">
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Company Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    placeholder="Enter company name"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="trade_license">Trade License *</Label>
                  <div className="relative">
                    <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      id="trade_license"
                      value={formData.trade_license}
                      onChange={(e) => setFormData({...formData, trade_license: e.target.value})}
                      placeholder="TL-XXX-YYYY"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="email">Email Address *</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      placeholder="<EMAIL>"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="phone">Phone Number *</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      placeholder="+****************"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
              </div>
              
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="address">Business Address</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 text-gray-400 w-4 h-4" />
                  <Textarea
                    id="address"
                    value={formData.address}
                    onChange={(e) => setFormData({...formData, address: e.target.value})}
                    placeholder="Enter complete business address"
                    className="pl-10"
                    rows={3}
                  />
                </div>
              </div>

              {/* File Upload Section */}
              <div className="space-y-3">
                <Label className="text-slate-700 font-medium">
                  <Upload className="w-4 h-4 inline mr-2" />
                  Documents (Trade License, Certificates, etc.)
                </Label>
                
                <div className="border-2 border-dashed border-slate-300 rounded-lg p-4 hover:border-slate-400 transition-colors">
                  <input
                    type="file"
                    id="add-files"
                    multiple
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                  <label 
                    htmlFor="add-files" 
                    className="cursor-pointer flex flex-col items-center space-y-2 text-slate-600 hover:text-slate-800 transition-colors"
                  >
                    <Upload className="w-8 h-8" />
                    <span className="text-sm font-medium">Click to upload files</span>
                    <span className="text-xs text-slate-500">PDF, DOC, DOCX, JPG, JPEG, PNG (Max 10MB each)</span>
                  </label>
                </div>

                {/* Selected Files Display */}
                {selectedFiles.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-slate-700">Selected Files:</p>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {selectedFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between bg-slate-50 p-2 rounded">
                          <span className="text-sm text-slate-700 truncate flex-1">{file.name}</span>
                          <span className="text-xs text-slate-500 mr-2">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </span>
                          <button
                            type="button"
                            onClick={() => removeSelectedFile(index)}
                            className="text-red-500 hover:text-red-700 transition-colors"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex justify-end space-x-2 pt-4 border-t">
              <Button type="button" variant="outline" onClick={() => setShowAddModal(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <Plus className="mr-2 h-4 w-4" />
                Add Contractor
              </Button>
            </div>
          </form>
        </ContentModalContent>
      </ContentModal>

      {/* Edit Contractor Modal */}
      <ContentModal open={showEditModal} onOpenChange={setShowEditModal}>
        <ContentModalOverlay />
        <ContentModalContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <ContentModalHeader>
            <ContentModalTitle className="flex items-center">
              <HardHat className="w-5 h-5 mr-2" />
              Edit Contractor
            </ContentModalTitle>
            <p className="text-sm text-muted-foreground">
              Update contractor information and business details.
            </p>
          </ContentModalHeader>
          <form onSubmit={handleUpdateContractor} className="space-y-4">
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit_name">Company Name *</Label>
                  <Input
                    id="edit_name"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    placeholder="Enter company name"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="edit_trade_license">Trade License *</Label>
                  <div className="relative">
                    <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      id="edit_trade_license"
                      value={formData.trade_license}
                      onChange={(e) => setFormData({...formData, trade_license: e.target.value})}
                      placeholder="TL-XXX-YYYY"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="edit_email">Email Address *</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      id="edit_email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      placeholder="<EMAIL>"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
                <div>
                  <Label htmlFor="edit_phone">Phone Number *</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <Input
                      id="edit_phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => setFormData({...formData, phone: e.target.value})}
                      placeholder="+****************"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
              </div>
              
              <div>
                <Label htmlFor="edit_status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="edit_address">Business Address</Label>
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 text-gray-400 w-4 h-4" />
                  <Textarea
                    id="edit_address"
                    value={formData.address}
                    onChange={(e) => setFormData({...formData, address: e.target.value})}
                    placeholder="Enter complete business address"
                    className="pl-10"
                    rows={3}
                  />
                </div>
              </div>

              {/* File Upload Section */}
              <div className="space-y-3">
                <Label className="text-slate-700 font-medium">
                  <Upload className="w-4 h-4 inline mr-2" />
                  Documents (Trade License, Certificates, etc.)
                </Label>
                
                {/* Existing Files */}
                {existingFiles.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-slate-700">Existing Files:</p>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {existingFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between bg-blue-50 p-2 rounded">
                          <span className="text-sm text-slate-700 truncate flex-1">{file.name}</span>
                          <div className="flex items-center space-x-2">
                            <button
                              type="button"
                              onClick={() => window.open(file.url, '_blank')}
                              className="text-blue-500 hover:text-blue-700 transition-colors"
                            >
                              <Download className="w-4 h-4" />
                            </button>
                            <button
                              type="button"
                              onClick={() => removeExistingFile(index)}
                              className="text-red-500 hover:text-red-700 transition-colors"
                            >
                              <X className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                <div className="border-2 border-dashed border-slate-300 rounded-lg p-4 hover:border-slate-400 transition-colors">
                  <input
                    type="file"
                    id="edit-files"
                    multiple
                    accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                    onChange={handleFileChange}
                    className="hidden"
                  />
                  <label 
                    htmlFor="edit-files" 
                    className="cursor-pointer flex flex-col items-center space-y-2 text-slate-600 hover:text-slate-800 transition-colors"
                  >
                    <Upload className="w-8 h-8" />
                    <span className="text-sm font-medium">Click to upload additional files</span>
                    <span className="text-xs text-slate-500">PDF, DOC, DOCX, JPG, JPEG, PNG (Max 10MB each)</span>
                  </label>
                </div>

                {/* New Selected Files Display */}
                {selectedFiles.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium text-slate-700">New Files to Upload:</p>
                    <div className="space-y-2 max-h-32 overflow-y-auto">
                      {selectedFiles.map((file, index) => (
                        <div key={index} className="flex items-center justify-between bg-green-50 p-2 rounded">
                          <span className="text-sm text-slate-700 truncate flex-1">{file.name}</span>
                          <span className="text-xs text-slate-500 mr-2">
                            {(file.size / 1024 / 1024).toFixed(2)} MB
                          </span>
                          <button
                            type="button"
                            onClick={() => removeSelectedFile(index)}
                            className="text-red-500 hover:text-red-700 transition-colors"
                          >
                            <X className="w-4 h-4" />
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="flex justify-end space-x-2 pt-4 border-t">
              <Button type="button" variant="outline" onClick={() => setShowEditModal(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <Edit2 className="mr-2 h-4 w-4" />
                Update Contractor
              </Button>
            </div>
          </form>
        </ContentModalContent>
      </ContentModal>
    </div>
  );
};

export default ContractorPage;

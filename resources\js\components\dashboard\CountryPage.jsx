import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from '@/hooks/useTranslation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Globe,
  MapPin,
  Flag,
  ChevronLeft,
  ChevronRight,
  Eye,
  EyeOff,
  Download
} from 'lucide-react';
import Swal from 'sweetalert2';
import { countryAPI } from '../../services/countryAPI';

const CountryPage = () => {
  const { t } = useTranslation();
  
  // State management
  const [countries, setCountries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingCountry, setEditingCountry] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [statistics, setStatistics] = useState({
    total_countries: 0,
    active_countries: 0,
    inactive_countries: 0,
  });

  // Form data for new country
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    iso_code: '',
    capital: '',
    currency: '',
    phone_code: '',
    continent: '',
    population: '',
    status: 'active'
  });

  // Form data for editing country
  const [editFormData, setEditFormData] = useState({
    name: '',
    code: '',
    iso_code: '',
    capital: '',
    currency: '',
    phone_code: '',
    continent: '',
    population: '',
    status: 'active'
  });

  // Fetch countries from API
  useEffect(() => {
    fetchCountries();
  }, [searchTerm, currentPage, perPage]);

  useEffect(() => {
    fetchStatistics();
  }, []);

  const fetchCountries = async () => {
    setLoading(true);
    try {
      const params = {
        search: searchTerm,
        page: currentPage,
        per_page: perPage
      };
      
      const response = await countryAPI.getCountries(params);
      
      if (response.success) {
        setCountries(response.data);
        setTotalRecords(response.pagination.total);
        setTotalPages(response.pagination.last_page);
      } else {
        throw new Error(response.message || 'Failed to fetch countries');
      }
    } catch (error) {
      console.error('Error fetching countries:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: error.message || 'Failed to fetch countries'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await countryAPI.getStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditInputChange = (field, value) => {
    setEditFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      iso_code: '',
      capital: '',
      currency: '',
      phone_code: '',
      continent: '',
      population: '',
      status: 'active'
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await countryAPI.createCountry(formData);
      
      if (response.success) {
        Swal.fire({
          icon: 'success',
          title: 'Success!',
          text: 'Country added successfully',
          confirmButtonColor: '#10b981',
          timer: 2000,
          showConfirmButton: false
        });

        setShowAddModal(false);
        resetForm();
        fetchCountries();
      } else {
        throw new Error(response.message || 'Failed to add country');
      }
    } catch (error) {
      console.error('Error adding country:', error);
      
      let errorMessage = 'Failed to add country. Please try again.';
      if (error.response?.data?.errors) {
        const errors = error.response.data.errors;
        errorMessage = Object.values(errors).flat().join(', ');
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: errorMessage,
        confirmButtonColor: '#ef4444'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (country) => {
    setEditingCountry(country);
    setEditFormData({
      name: country.name,
      code: country.code,
      iso_code: country.iso_code,
      capital: country.capital,
      currency: country.currency,
      phone_code: country.phone_code,
      continent: country.continent,
      population: country.population,
      status: country.status
    });
    setShowEditModal(true);
  };

  const handleUpdate = async () => {
    setIsSubmitting(true);
    try {
      const response = await countryAPI.updateCountry(editingCountry.id, editFormData);
      
      if (response.success) {
        Swal.fire({
          icon: 'success',
          title: 'Updated!',
          text: 'Country updated successfully',
          confirmButtonColor: '#10b981',
          timer: 2000,
          showConfirmButton: false
        });

        setShowEditModal(false);
        setEditingCountry(null);
        fetchCountries();
      } else {
        throw new Error(response.message || 'Failed to update country');
      }
    } catch (error) {
      console.error('Error updating country:', error);
      
      let errorMessage = 'Failed to update country. Please try again.';
      if (error.response?.data?.errors) {
        const errors = error.response.data.errors;
        errorMessage = Object.values(errors).flat().join(', ');
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: errorMessage,
        confirmButtonColor: '#ef4444'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (country) => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: `This will permanently delete ${country.name}. This action cannot be undone!`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel'
    });

    if (result.isConfirmed) {
      try {
        const response = await countryAPI.deleteCountry(country.id);
        
        if (response.success) {
          Swal.fire({
            icon: 'success',
            title: 'Deleted!',
            text: 'Country has been deleted successfully',
            confirmButtonColor: '#10b981',
            timer: 2000,
            showConfirmButton: false
          });
          
          fetchCountries();
        } else {
          throw new Error(response.message || 'Failed to delete country');
        }
      } catch (error) {
        console.error('Error deleting country:', error);
        
        let errorMessage = 'Failed to delete country. Please try again.';
        if (error.response?.data?.message) {
          errorMessage = error.response.data.message;
        }
        
        Swal.fire({
          icon: 'error',
          title: 'Error!',
          text: errorMessage,
          confirmButtonColor: '#ef4444'
        });
      }
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchCountries();
  };

  const getStatusColor = (status) => {
    return status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  const getStatusBadge = (status) => {
    const Icon = status === 'active' ? Eye : EyeOff;
    return (
      <Badge className={`${getStatusColor(status)} border-0`}>
        <Icon className="mr-1 h-3 w-3" />
        {status}
      </Badge>
    );
  };

  const exportData = () => {
    Swal.fire({
      icon: 'info',
      title: 'Export Feature',
      text: 'Export functionality will be implemented soon!',
      confirmButtonColor: '#3b82f6'
    });
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('country.title')}</h1>
          <p className="text-muted-foreground">
            {t('country.description')}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={exportData}>
            <Download className="mr-2 h-4 w-4" />
            {t('common.buttons.export')}
          </Button>
          <Button onClick={() => setShowAddModal(true)}>
            <Plus className="mr-2 h-4 w-4" />
            {t('country.addCountry')}
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            {t('country.searchTitle')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder={t('country.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button type="submit">
              <Search className="mr-2 h-4 w-4" />
              {t('common.buttons.search')}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-800">{t('country.statistics.totalCountries')}</CardTitle>
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <Globe className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900">{statistics.total_countries}</div>
            <p className="text-xs text-blue-600 mt-1">
              {statistics.active_countries} active, {statistics.inactive_countries} inactive
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-800">{t('country.statistics.activeCountries')}</CardTitle>
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
              <Eye className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">
              {statistics.active_countries}
            </div>
            <p className="text-xs text-green-600 mt-1">
              Currently active
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-800">Inactive Countries</CardTitle>
            <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
              <EyeOff className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-900">
              {statistics.inactive_countries}
            </div>
            <p className="text-xs text-red-600 mt-1">
              Currently inactive
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Countries Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Countries ({totalRecords})
          </CardTitle>
          <CardDescription>
            Manage countries and their detailed information
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-blue-600"></div>
              <span className="ml-3 text-muted-foreground">Loading countries...</span>
            </div>
          ) : countries.length === 0 ? (
            <div className="text-center py-12">
              <Globe className="mx-auto h-16 w-16 text-gray-400" />
              <h3 className="mt-4 text-lg font-medium text-gray-900">No countries found</h3>
              <p className="mt-2 text-gray-500">
                {searchTerm ? 'Try adjusting your search terms' : 'Get started by adding a new country'}
              </p>
              {!searchTerm && (
                <Button className="mt-4" onClick={() => setShowAddModal(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add First Country
                </Button>
              )}
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-[200px]">Country</TableHead>
                      <TableHead>Code</TableHead>
                      <TableHead>Capital</TableHead>
                      <TableHead>Currency</TableHead>
                      <TableHead>Phone</TableHead>
                      <TableHead>Continent</TableHead>
                      <TableHead>Population</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {countries.map((country) => (
                      <TableRow key={country.id} className="hover:bg-muted/50">
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <Flag className="h-4 w-4 text-blue-600" />
                            <div>
                              <div className="font-medium">{country.name}</div>
                              <div className="text-xs text-muted-foreground">{country.iso_code}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="font-mono">
                            {country.code}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3 text-gray-500" />
                            {country.capital}
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-mono text-sm">{country.currency}</span>
                        </TableCell>
                        <TableCell>
                          <span className="font-mono text-sm">{country.phone_code}</span>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">{country.continent}</Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm">{country.population}</span>
                        </TableCell>
                        <TableCell>
                          {getStatusBadge(country.status)}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEdit(country)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                className="text-red-600"
                                onClick={() => handleDelete(country)}
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between px-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * perPage) + 1} to {Math.min(currentPage * perPage, totalRecords)} of {totalRecords} countries
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage <= 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1;
                        return (
                          <Button
                            key={page}
                            variant={currentPage === page ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentPage(page)}
                          >
                            {page}
                          </Button>
                        );
                      })}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage >= totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Country Modal */}
      <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Add New Country
            </DialogTitle>
            <DialogDescription>
              Fill in the details below to add a new country to the system.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Country Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="e.g., Bangladesh"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="code">Country Code *</Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                    placeholder="e.g., BD"
                    maxLength="2"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="iso_code">ISO Code *</Label>
                  <Input
                    id="iso_code"
                    value={formData.iso_code}
                    onChange={(e) => handleInputChange('iso_code', e.target.value.toUpperCase())}
                    placeholder="e.g., BGD"
                    maxLength="3"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="capital">Capital City</Label>
                  <Input
                    id="capital"
                    value={formData.capital}
                    onChange={(e) => handleInputChange('capital', e.target.value)}
                    placeholder="e.g., Dhaka"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Input
                    id="currency"
                    value={formData.currency}
                    onChange={(e) => handleInputChange('currency', e.target.value.toUpperCase())}
                    placeholder="e.g., BDT"
                    maxLength="3"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone_code">Phone Code</Label>
                  <Input
                    id="phone_code"
                    value={formData.phone_code}
                    onChange={(e) => handleInputChange('phone_code', e.target.value)}
                    placeholder="e.g., +880"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="continent">Continent</Label>
                  <select
                    id="continent"
                    value={formData.continent}
                    onChange={(e) => handleInputChange('continent', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Select Continent</option>
                    <option value="Asia">Asia</option>
                    <option value="Europe">Europe</option>
                    <option value="North America">North America</option>
                    <option value="South America">South America</option>
                    <option value="Africa">Africa</option>
                    <option value="Australia">Australia</option>
                    <option value="Antarctica">Antarctica</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="population">Population</Label>
                  <Input
                    id="population"
                    value={formData.population}
                    onChange={(e) => handleInputChange('population', e.target.value)}
                    placeholder="e.g., 165,000,000"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <select
                  id="status"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowAddModal(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Adding...
                  </>
                ) : (
                  'Add Country'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Country Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5" />
              Edit Country
            </DialogTitle>
            <DialogDescription>
              Update the country information below.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            {/* Same form fields as Add modal but with editFormData */}
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_name">Country Name *</Label>
                <Input
                  id="edit_name"
                  value={editFormData.name}
                  onChange={(e) => handleEditInputChange('name', e.target.value)}
                  placeholder="e.g., Bangladesh"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit_code">Country Code *</Label>
                <Input
                  id="edit_code"
                  value={editFormData.code}
                  onChange={(e) => handleEditInputChange('code', e.target.value.toUpperCase())}
                  placeholder="e.g., BD"
                  maxLength="2"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_iso_code">ISO Code *</Label>
                <Input
                  id="edit_iso_code"
                  value={editFormData.iso_code}
                  onChange={(e) => handleEditInputChange('iso_code', e.target.value.toUpperCase())}
                  placeholder="e.g., BGD"
                  maxLength="3"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit_capital">Capital City</Label>
                <Input
                  id="edit_capital"
                  value={editFormData.capital}
                  onChange={(e) => handleEditInputChange('capital', e.target.value)}
                  placeholder="e.g., Dhaka"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_currency">Currency</Label>
                <Input
                  id="edit_currency"
                  value={editFormData.currency}
                  onChange={(e) => handleEditInputChange('currency', e.target.value.toUpperCase())}
                  placeholder="e.g., BDT"
                  maxLength="3"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit_phone_code">Phone Code</Label>
                <Input
                  id="edit_phone_code"
                  value={editFormData.phone_code}
                  onChange={(e) => handleEditInputChange('phone_code', e.target.value)}
                  placeholder="e.g., +880"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_continent">Continent</Label>
                <select
                  id="edit_continent"
                  value={editFormData.continent}
                  onChange={(e) => handleEditInputChange('continent', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select Continent</option>
                  <option value="Asia">Asia</option>
                  <option value="Europe">Europe</option>
                  <option value="North America">North America</option>
                  <option value="South America">South America</option>
                  <option value="Africa">Africa</option>
                  <option value="Australia">Australia</option>
                  <option value="Antarctica">Antarctica</option>
                </select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit_population">Population</Label>
                <Input
                  id="edit_population"
                  value={editFormData.population}
                  onChange={(e) => handleEditInputChange('population', e.target.value)}
                  placeholder="e.g., 165,000,000"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_status">Status</Label>
              <select
                id="edit_status"
                value={editFormData.status}
                onChange={(e) => handleEditInputChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setShowEditModal(false)}>
              Cancel
            </Button>
            <Button type="button" onClick={handleUpdate} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Updating...
                </>
              ) : (
                'Update Country'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CountryPage;

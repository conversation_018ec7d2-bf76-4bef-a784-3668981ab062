import React from 'react';
import {
  Area,
  AreaChart,
  Bar,
  BarChart,
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

const SimpleChart = ({ 
  data, 
  type = 'area', 
  dataKey = 'value', 
  height = 300,
  color = 'hsl(var(--primary))',
  showGrid = false,
  showTooltip = true 
}) => {
  const commonProps = {
    data,
    margin: {
      top: 10,
      right: 30,
      left: 0,
      bottom: 0,
    },
  };

  const axisProps = {
    axisLine: false,
    tickLine: false,
    tick: { fontSize: 12, fill: 'hsl(var(--muted-foreground))' },
    grid: showGrid,
  };

  const tooltipContent = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="rounded-lg border bg-background p-2 shadow-sm">
          <div className="grid grid-cols-1 gap-2">
            <div className="flex flex-col">
              <span className="text-[0.70rem] uppercase text-muted-foreground">
                {label}
              </span>
              <span className="font-bold" style={{ color }}>
                {payload[0].value}
              </span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const renderChart = () => {
    switch (type) {
      case 'line':
        return (
          <LineChart {...commonProps}>
            <XAxis dataKey="name" {...axisProps} />
            <YAxis {...axisProps} />
            {showTooltip && <Tooltip content={tooltipContent} />}
            <Line
              type="monotone"
              dataKey={dataKey}
              stroke={color}
              strokeWidth={2}
              dot={{ fill: color }}
            />
          </LineChart>
        );
      
      case 'bar':
        return (
          <BarChart {...commonProps}>
            <XAxis dataKey="name" {...axisProps} />
            <YAxis {...axisProps} />
            {showTooltip && <Tooltip content={tooltipContent} />}
            <Bar dataKey={dataKey} fill={color} />
          </BarChart>
        );
      
      case 'area':
      default:
        return (
          <AreaChart {...commonProps}>
            <defs>
              <linearGradient id="colorGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor={color} stopOpacity={0.8}/>
                <stop offset="95%" stopColor={color} stopOpacity={0.1}/>
              </linearGradient>
            </defs>
            <XAxis dataKey="name" {...axisProps} />
            <YAxis {...axisProps} />
            {showTooltip && <Tooltip content={tooltipContent} />}
            <Area
              type="monotone"
              dataKey={dataKey}
              stroke={color}
              fillOpacity={1}
              fill="url(#colorGradient)"
              strokeWidth={2}
            />
          </AreaChart>
        );
    }
  };

  return (
    <ResponsiveContainer width="100%" height={height}>
      {renderChart()}
    </ResponsiveContainer>
  );
};

export default SimpleChart;

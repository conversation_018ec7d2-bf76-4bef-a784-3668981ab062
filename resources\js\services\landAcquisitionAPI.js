import axios from 'axios';
import { API_BASE_URL } from '../config/api.js';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Remove invalid token and reload page
      localStorage.removeItem('auth_token');
      window.location.reload();
    }
    return Promise.reject(error);
  }
);

// Land Acquisition API functions
export const getAll = async (params = {}) => {
  try {
    const response = await api.get('/land-acquisitions', { params });
    return response.data;
  } catch (error) {
    // Debug statement removed
    throw error;
  }
};

// Get single land acquisition by ID
export const getById = async (id) => {
  try {
    const response = await api.get(`/land-acquisitions/${id}`);
    return response.data;
  } catch (error) {
    // Debug statement removed
    throw error;
  }
};

// Create new land acquisition
export const create = async (data) => {
  try {
    // Check if we have any file data that needs FormData
    const hasFile = (data.photo && typeof data.photo === 'object' && data.photo instanceof File) ||
                   (data.nid_front && typeof data.nid_front === 'object' && data.nid_front instanceof File) ||
                   (data.nid_back && typeof data.nid_back === 'object' && data.nid_back instanceof File) ||
                   (data.passport_photo && typeof data.passport_photo === 'object' && data.passport_photo instanceof File);
    
    let requestData;
    let headers = {};
    
    if (hasFile) {
      // Debug statement removed
      // Use FormData for file uploads
      requestData = new FormData();
      
      // Append all form fields to FormData - include landOwners_id even if empty
      Object.keys(data).forEach(key => {
        const value = data[key];
        
        // Skip null, undefined, or empty string values (except landOwners_id which can be empty)
        if (value === null || value === undefined) {
          // Debug statement removed
          return;
        }
        
        // Handle nested objects like land_address
        if (key === 'land_address' && typeof value === 'object' && value !== null) {
          // Append each land_address field individually
          Object.keys(value).forEach(addressKey => {
            const addressValue = value[addressKey];
            if (addressValue !== null && addressValue !== undefined && addressValue !== '') {
              // Debug statement removed
              requestData.append(`land_address[${addressKey}]`, addressValue);
            }
          });
          return;
        }
        
        // For photo field, only append if it's a File object (actual upload)
        // Skip if it's null, empty string, or undefined (no photo selected)
        if (key === 'photo') {
          if (value instanceof File) {
            // Debug statement removed
            requestData.append(key, value);
          } else if (value === null || value === '' || value === undefined) {
            // Debug statement removed
          } else {
            // Debug statement removed
          }
          return;
        }
        
        // For document fields, only append if they are File objects
        if (['nid_front', 'nid_back', 'passport_photo'].includes(key)) {
          if (value instanceof File) {
            // Debug statement removed
            requestData.append(key, value);
          } else if (value === null || value === '' || value === undefined) {
            // Debug statement removed
          } else {
            // Debug statement removed
          }
          return;
        }
        
        // For all other fields, include landOwners_id even if empty string, skip other empty strings
        if (key === 'landOwners_id' || value !== '') {
          // Debug statement removed
          requestData.append(key, value);
        } else {
          // Debug statement removed
        }
      });
      
      // DO NOT set Content-Type for FormData - browser will set it with boundary
      const response = await api.post('/land-acquisitions', requestData);
      return response.data;
    } else {
      // Use regular JSON for non-file data
      requestData = data;
      headers['Content-Type'] = 'application/json';
      
      const response = await api.post('/land-acquisitions', requestData, { headers });
      return response.data;
    }
  } catch (error) {
    // Debug statement removed
    throw error;
  }
};

// Update existing land acquisition
export const update = async (id, data) => {
  try {
    // Check if we have file data that needs FormData
    const hasFile = (data.photo && typeof data.photo === 'object' && data.photo instanceof File) ||
                   (data.nid_front && typeof data.nid_front === 'object' && data.nid_front instanceof File) ||
                   (data.nid_back && typeof data.nid_back === 'object' && data.nid_back instanceof File) ||
                   (data.passport_photo && typeof data.passport_photo === 'object' && data.passport_photo instanceof File);
    
 
    
    let requestData;
    let headers = {};
    
    if (hasFile) {
      // Debug statement removed
      // Use FormData for file uploads
      requestData = new FormData();
      
      // Add _method for Laravel PUT request simulation
      requestData.append('_method', 'PUT');
      
      // Append all form fields to FormData
      Object.keys(data).forEach(key => {
        const value = data[key];
        
        // Skip null or undefined values
        if (value === null || value === undefined) {
          // Debug statement removed
          return;
        }
        
        // Handle nested objects like land_address
        if (key === 'land_address' && typeof value === 'object' && value !== null) {
          // Append each land_address field individually
          Object.keys(value).forEach(addressKey => {
            const addressValue = value[addressKey];
            if (addressValue !== null && addressValue !== undefined && addressValue !== '') {
              // Debug statement removed
              requestData.append(`land_address[${addressKey}]`, addressValue);
            }
          });
          return;
        }
        
        // For file fields, only append if it's a File object (actual upload)
        // Skip if it's null, empty string, or undefined (no file change)
        if (['photo', 'nid_front', 'nid_back', 'passport_photo'].includes(key)) {
          if (value instanceof File) {
            // Debug statement removed
            requestData.append(key, value);
          } else if (value === null || value === '' || value === undefined) {
            // Debug statement removed
          } else {
            // Debug statement removed
          }
          return;
        }
        
        // For all other fields, skip empty strings
        if (value !== '') {
          // Debug statement removed
          requestData.append(key, value);
        } else {
          // Debug statement removed
        }
      });
      
      // Don't set Content-Type header - let browser set it with proper boundary
      const response = await api.post(`/land-acquisitions/${id}`, requestData);
      return response.data;
    } else {
      // Debug statement removed
      // Use regular JSON for non-file data
      requestData = data;
      headers['Content-Type'] = 'application/json';
      
      const response = await api.put(`/land-acquisitions/${id}`, requestData, { headers });
      return response.data;
    }
  } catch (error) {
    // Debug statement removed
    throw error;
  }
};

// Delete land acquisition
export const delete_ = async (id) => {
  try {
    const response = await api.delete(`/land-acquisitions/${id}`);
    return response.data;
  } catch (error) {
    // Debug statement removed
    throw error;
  }
};

// Get statistics (if this endpoint exists)
export const getStatistics = async () => {
  try {
    const response = await api.get('/land-acquisitions-statistics');
    return response.data;
  } catch (error) {
    // Debug statement removed
    throw error;
  }
};

// Get land owners dropdown data
export const getLandOwnersDropdown = async () => {
  try {
    console.log('🔄 API: Calling land-owners/dropdown...');
    const response = await api.get('/land-owners/dropdown');
    console.log('✅ API: Land owners dropdown response:', response);
    return response.data;
  } catch (error) {
    console.error('❌ API: Land owners dropdown failed:', {
      message: error.message,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      config: {
        url: error.config?.url,
        baseURL: error.config?.baseURL,
        method: error.config?.method
      }
    });
    
    // Throw the error so it can be caught by the calling function
    throw error;
  }
};

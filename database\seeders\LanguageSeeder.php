<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $languages = [
            [
                'name' => 'English',
                'code' => 'en',
                'native_name' => 'English',
                'flag' => '🇺🇸',
                'direction' => 'ltr',
                'status' => 'active',
                'is_default' => true,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Spanish',
                'code' => 'es',
                'native_name' => 'Español',
                'flag' => '🇪🇸',
                'direction' => 'ltr',
                'status' => 'active',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'French',
                'code' => 'fr',
                'native_name' => 'Français',
                'flag' => '🇫🇷',
                'direction' => 'ltr',
                'status' => 'active',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'German',
                'code' => 'de',
                'native_name' => 'Deutsch',
                'flag' => '🇩🇪',
                'direction' => 'ltr',
                'status' => 'active',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Arabic',
                'code' => 'ar',
                'native_name' => 'العربية',
                'flag' => '🇸🇦',
                'direction' => 'rtl',
                'status' => 'active',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Italian',
                'code' => 'it',
                'native_name' => 'Italiano',
                'flag' => '🇮🇹',
                'direction' => 'ltr',
                'status' => 'active',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Portuguese',
                'code' => 'pt',
                'native_name' => 'Português',
                'flag' => '🇵🇹',
                'direction' => 'ltr',
                'status' => 'active',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Portuguese (Brazil)',
                'code' => 'pt-BR',
                'native_name' => 'Português (Brasil)',
                'flag' => '🇧🇷',
                'direction' => 'ltr',
                'status' => 'active',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Japanese',
                'code' => 'ja',
                'native_name' => '日本語',
                'flag' => '🇯🇵',
                'direction' => 'ltr',
                'status' => 'active',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Chinese (Simplified)',
                'code' => 'zh-CN',
                'native_name' => '简体中文',
                'flag' => '🇨🇳',
                'direction' => 'ltr',
                'status' => 'active',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Russian',
                'code' => 'ru',
                'native_name' => 'Русский',
                'flag' => '🇷🇺',
                'direction' => 'ltr',
                'status' => 'inactive',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Korean',
                'code' => 'ko',
                'native_name' => '한국어',
                'flag' => '🇰🇷',
                'direction' => 'ltr',
                'status' => 'active',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Hindi',
                'code' => 'hi',
                'native_name' => 'हिन्दी',
                'flag' => '🇮🇳',
                'direction' => 'ltr',
                'status' => 'active',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Dutch',
                'code' => 'nl',
                'native_name' => 'Nederlands',
                'flag' => '🇳🇱',
                'direction' => 'ltr',
                'status' => 'active',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Turkish',
                'code' => 'tr',
                'native_name' => 'Türkçe',
                'flag' => '🇹🇷',
                'direction' => 'ltr',
                'status' => 'inactive',
                'is_default' => false,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        ];

        // Clear existing languages first
        DB::table('languages')->truncate();

        // Insert new languages
        DB::table('languages')->insert($languages);
    }
}

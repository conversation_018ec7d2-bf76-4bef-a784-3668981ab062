<?php

namespace App\Http\Controllers;

use App\Models\LandDocument;
use App\Models\LandAcquisition;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class LandDocumentController extends Controller
{
    /**
     * Display a listing of documents for a specific land acquisition.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $landAcquisitionId = $request->get('land_acquisition_id');
            
            $query = LandDocument::with(['uploadedBy:id,name,email']);
            
            if ($landAcquisitionId) {
                $query->where('land_acquisition_id', $landAcquisitionId);
            }
            
            $documents = $query->orderBy('created_at', 'desc')->paginate(20);

            return response()->json([
                'success' => true,
                'data' => $documents
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch documents: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created document.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'land_acquisition_id' => 'required|exists:land_acquisitions,id',
                'document_name' => 'required|string|max:255',
                'document_file' => 'required|file|mimes:pdf,doc,docx,jpg,jpeg,png,gif,webp|max:10240', // 10MB max
                'description' => 'nullable|string|max:1000',
            ]);

            // Handle file upload
            $file = $request->file('document_file');
            $landAcquisition = LandAcquisition::findOrFail($validated['land_acquisition_id']);
            
            // Create unique filename
            $filename = 'land_doc_' . $landAcquisition->id . '_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
            
            // Create directory if it doesn't exist
            $publicDir = public_path('landowners/land-documents');
            if (!is_dir($publicDir)) {
                mkdir($publicDir, 0755, true);
            }
            
            // Move file
            $destinationPath = $publicDir . DIRECTORY_SEPARATOR . $filename;
            if (move_uploaded_file($file->getPathname(), $destinationPath)) {
                $filePath = '/landowners/land-documents/' . $filename;
                
                // Create document record
                $document = LandDocument::create([
                    'land_acquisition_id' => $validated['land_acquisition_id'],
                    'document_name' => $validated['document_name'],
                    'document_file_path' => $filePath,
                    'original_filename' => $file->getClientOriginalName(),
                    'file_type' => $file->getClientOriginalExtension(),
                    'file_size' => $file->getSize(),
                    'description' => $validated['description'] ?? null,
                    'uploaded_by' => Auth::id(),
                ]);

                return response()->json([
                    'success' => true,
                    'message' => 'Document uploaded successfully',
                    'data' => $document->load('uploadedBy:id,name,email')
                ], 201);
            } else {
                throw new \Exception('Failed to upload file');
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload document: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified document.
     */
    public function show(LandDocument $landDocument): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => $landDocument->load(['landAcquisition', 'uploadedBy:id,name,email'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch document: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified document.
     */
    public function update(Request $request, LandDocument $landDocument): JsonResponse
    {
        try {
            $validated = $request->validate([
                'document_name' => 'sometimes|required|string|max:255',
                'description' => 'sometimes|nullable|string|max:1000',
                'document_file' => 'sometimes|nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png,gif,webp|max:10240',
            ]);

            // Handle file upload if present
            if ($request->hasFile('document_file')) {
                // Delete old file
                $this->deleteDocumentFile($landDocument->document_file_path);
                
                // Upload new file
                $file = $request->file('document_file');
                $filename = 'land_doc_' . $landDocument->land_acquisition_id . '_' . time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
                
                $publicDir = public_path('landowners/land-documents');
                if (!is_dir($publicDir)) {
                    mkdir($publicDir, 0755, true);
                }
                
                $destinationPath = $publicDir . DIRECTORY_SEPARATOR . $filename;
                if (move_uploaded_file($file->getPathname(), $destinationPath)) {
                    $validated['document_file_path'] = '/landowners/land-documents/' . $filename;
                    $validated['original_filename'] = $file->getClientOriginalName();
                    $validated['file_type'] = $file->getClientOriginalExtension();
                    $validated['file_size'] = $file->getSize();
                } else {
                    throw new \Exception('Failed to upload new file');
                }
            }

            $landDocument->update($validated);

            return response()->json([
                'success' => true,
                'message' => 'Document updated successfully',
                'data' => $landDocument->fresh(['uploadedBy:id,name,email'])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update document: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified document.
     */
    public function destroy(LandDocument $landDocument): JsonResponse
    {
        try {
            // Delete the file
            $this->deleteDocumentFile($landDocument->document_file_path);
            
            // Delete the database record
            $landDocument->delete();

            return response()->json([
                'success' => true,
                'message' => 'Document deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete document: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download the specified document.
     */
    public function download(LandDocument $landDocument)
    {
        try {
            $filePath = public_path(ltrim($landDocument->document_file_path, '/'));
            
            if (!file_exists($filePath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'File not found'
                ], 404);
            }

            return response()->download($filePath, $landDocument->original_filename);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to download document: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete document file from storage
     */
    private function deleteDocumentFile(string $filePath): void
    {
        try {
            $fullPath = public_path(ltrim($filePath, '/'));
            if (file_exists($fullPath)) {
                unlink($fullPath);
            }
        } catch (\Exception $e) {
            // Log error but don't throw exception
            \Log::warning('Failed to delete document file: ' . $e->getMessage());
        }
    }
}

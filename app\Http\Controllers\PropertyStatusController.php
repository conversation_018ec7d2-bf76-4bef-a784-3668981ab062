<?php

namespace App\Http\Controllers;

use App\Models\PropertyStatus;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PropertyStatusController extends Controller
{
    /**
     * Display a listing of property statuses
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = PropertyStatus::query();

            // Search functionality
            if ($request->filled('search')) {
                $query->search($request->search);
            }

            // Status filter
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'sort_order');
            $sortDirection = $request->get('sort_direction', 'asc');
            
            if (in_array($sortBy, ['name', 'status', 'sort_order', 'created_at'])) {
                if ($sortBy === 'sort_order') {
                    $query->orderBy('sort_order')->orderBy('name');
                } else {
                    $query->orderBy($sortBy, $sortDirection);
                }
            } else {
                $query->ordered();
            }

            // Include projects count if requested
            if ($request->boolean('include_projects_count')) {
                $query->withProjectsCount();
            }

            // Pagination
            $perPage = $request->get('per_page', 15);
            $perPage = min($perPage, 100); // Limit max per page

            if ($request->boolean('paginate', true)) {
                $propertyStatuses = $query->paginate($perPage);
            } else {
                $propertyStatuses = $query->get();
            }

            return response()->json([
                'success' => true,
                'message' => 'Property statuses retrieved successfully',
                'data' => $propertyStatuses
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property statuses',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Store a newly created property status
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:100|unique:property_statuses,name',
                'slug' => 'nullable|string|max:100|unique:property_statuses,slug',
                'description' => 'nullable|string|max:1000',
                'color' => 'nullable|string|regex:/^#[A-Fa-f0-9]{6}$/',
                'icon' => 'nullable|string|max:50',
                'status' => 'required|in:active,inactive',
                'sort_order' => 'nullable|integer|min:0',
                'metadata' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            
            // Auto-generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = Str::slug($data['name']);
            }

            // Set default sort order if not provided
            if (!isset($data['sort_order'])) {
                $maxSortOrder = PropertyStatus::max('sort_order') ?? 0;
                $data['sort_order'] = $maxSortOrder + 1;
            }

            // Set default color if not provided
            if (empty($data['color'])) {
                $data['color'] = '#3B82F6';
            }

            $propertyStatus = PropertyStatus::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Property status created successfully',
                'data' => $propertyStatus
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create property status',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Display the specified property status
     */
    public function show(PropertyStatus $propertyStatus): JsonResponse
    {
        try {
            $propertyStatus->loadCount('projects');

            return response()->json([
                'success' => true,
                'message' => 'Property status retrieved successfully',
                'data' => $propertyStatus
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property status',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Update the specified property status
     */
    public function update(Request $request, PropertyStatus $propertyStatus): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:100|unique:property_statuses,name,' . $propertyStatus->id,
                'slug' => 'nullable|string|max:100|unique:property_statuses,slug,' . $propertyStatus->id,
                'description' => 'nullable|string|max:1000',
                'color' => 'nullable|string|regex:/^#[A-Fa-f0-9]{6}$/',
                'icon' => 'nullable|string|max:50',
                'status' => 'required|in:active,inactive',
                'sort_order' => 'nullable|integer|min:0',
                'metadata' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Auto-generate slug if name changed but slug not provided
            if (isset($data['name']) && !isset($data['slug'])) {
                $data['slug'] = Str::slug($data['name']);
            }

            $propertyStatus->update($data);

            return response()->json([
                'success' => true,
                'message' => 'Property status updated successfully',
                'data' => $propertyStatus->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update property status',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Remove the specified property status
     */
    public function destroy(PropertyStatus $propertyStatus): JsonResponse
    {
        try {
            // Check if property status is being used by any projects
            $projectsCount = $propertyStatus->projects()->count();
            
            if ($projectsCount > 0) {
                return response()->json([
                    'success' => false,
                    'message' => "Cannot delete property status. It is currently used by {$projectsCount} project(s)."
                ], 400);
            }

            $propertyStatus->delete();

            return response()->json([
                'success' => true,
                'message' => 'Property status deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete property status',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Toggle the status of a property status
     */
    public function toggleStatus(PropertyStatus $propertyStatus): JsonResponse
    {
        try {
            $newStatus = $propertyStatus->status === 'active' ? 'inactive' : 'active';
            $propertyStatus->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'message' => "Property status {$newStatus} successfully",
                'data' => $propertyStatus->fresh()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle property status',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get property statuses for dropdown
     */
    public function dropdown(Request $request): JsonResponse
    {
        try {
            $propertyStatuses = PropertyStatus::active()
                ->ordered()
                ->select('id', 'name', 'slug', 'color', 'icon')
                ->get();

            return response()->json([
                'success' => true,
                'message' => 'Property statuses for dropdown retrieved successfully',
                'data' => $propertyStatuses
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property statuses for dropdown',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Bulk update sort orders
     */
    public function updateSortOrders(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'property_statuses' => 'required|array',
                'property_statuses.*.id' => 'required|integer|exists:property_statuses,id',
                'property_statuses.*.sort_order' => 'required|integer|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            foreach ($request->property_statuses as $statusData) {
                PropertyStatus::where('id', $statusData['id'])
                    ->update(['sort_order' => $statusData['sort_order']]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Sort orders updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update sort orders',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
}

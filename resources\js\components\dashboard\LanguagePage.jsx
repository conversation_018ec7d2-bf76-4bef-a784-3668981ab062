import React, { useState, useEffect } from 'react';
import { Plus, Search, Edit, Trash2, Eye, Globe, Languages, Star } from 'lucide-react';
import { languageAPI } from '../../services/languageAPI';
import { useTranslation } from '@/hooks/useTranslation';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import {
    ContentModal,
    ContentModalContent,
    ContentModalHeader,
    ContentModalTitle,
    ContentModalOverlay,
} from '../ui/content-modal';
import Swal from 'sweetalert2';

const LanguagePage = () => {
  const { t } = useTranslation();
  const [languages, setLanguages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingLanguage, setEditingLanguage] = useState(null);
  const [statistics, setStatistics] = useState({});
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    native_name: '',
    flag: '',
    direction: 'ltr',
    status: 'active',
    is_default: false
  });

  useEffect(() => {
    fetchLanguages();
    fetchStatistics();
  }, [currentPage, searchTerm]);

  const fetchLanguages = async () => {
    try {
      const response = await languageAPI.getLanguages({
        search: searchTerm,
        page: currentPage,
        per_page: 10
      });
      
      if (response.success) {
        setLanguages(response.data);
        setTotalPages(response.pagination.last_page);
      }
    } catch (error) {
      console.error('Error fetching languages:', error);
      Swal.fire('Error', 'Failed to fetch languages', 'error');
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await languageAPI.getStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingLanguage) {
        await languageAPI.updateLanguage(editingLanguage.id, formData);
        Swal.fire('Success', 'Language updated successfully', 'success');
      } else {
        await languageAPI.createLanguage(formData);
        Swal.fire('Success', 'Language created successfully', 'success');
      }
      
      setShowModal(false);
      setEditingLanguage(null);
      setFormData({ name: '', code: '', native_name: '', flag: '', direction: 'ltr', status: 'active', is_default: false });
      fetchLanguages();
      fetchStatistics();
    } catch (error) {
      Swal.fire('Error', error.response?.data?.message || 'Operation failed', 'error');
    }
  };

  const handleEdit = (language) => {
    setEditingLanguage(language);
    setFormData({
      name: language.name,
      code: language.code,
      native_name: language.native_name || '',
      flag: language.flag || '',
      direction: language.direction,
      status: language.status,
      is_default: language.is_default || false
    });
    setShowModal(true);
  };

  const handleSetDefault = async (id) => {
    const result = await Swal.fire({
      title: 'Set as Default Language?',
      text: 'This will make this language the default for the system',
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#3085d6',
      cancelButtonColor: '#d33',
      confirmButtonText: 'Yes, set as default!'
    });

    if (result.isConfirmed) {
      try {
        await languageAPI.setDefault(id);
        Swal.fire('Success!', 'Default language updated successfully', 'success');
        fetchLanguages();
        fetchStatistics();
      } catch (error) {
        Swal.fire('Error', error.response?.data?.message || 'Failed to set default language', 'error');
      }
    }
  };

  const handleDelete = async (id) => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: 'This action cannot be undone',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, delete it!'
    });

    if (result.isConfirmed) {
      try {
        await languageAPI.deleteLanguage(id);
        Swal.fire('Deleted!', 'Language deleted successfully', 'success');
        fetchLanguages();
        fetchStatistics();
      } catch (error) {
        Swal.fire('Error', 'Failed to delete language', 'error');
      }
    }
  };

  const StatCard = ({ title, value, icon: Icon, color }) => {
    const colorClasses = {
      'bg-blue-500': 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200 text-blue-800',
      'bg-green-500': 'bg-gradient-to-r from-green-50 to-green-100 border-green-200 text-green-800',
      'bg-yellow-500': 'bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200 text-yellow-800',
      'bg-purple-500': 'bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200 text-purple-800',
      'bg-orange-500': 'bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200 text-orange-800',
    };
    
    const iconClasses = {
      'bg-blue-500': 'bg-blue-500',
      'bg-green-500': 'bg-green-500',
      'bg-yellow-500': 'bg-yellow-500',
      'bg-purple-500': 'bg-purple-500',
      'bg-orange-500': 'bg-orange-500',
    };
    
    const textClasses = {
      'bg-blue-500': 'text-blue-900',
      'bg-green-500': 'text-green-900',
      'bg-yellow-500': 'text-yellow-900',
      'bg-purple-500': 'text-purple-900',
      'bg-orange-500': 'text-orange-900',
    };
    
    return (
      <Card className={colorClasses[color] || 'bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200'}>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className={`text-sm font-medium ${colorClasses[color]?.split(' ').pop() || 'text-gray-800'}`}>
            {title}
          </CardTitle>
          <div className={`w-8 h-8 ${iconClasses[color] || 'bg-gray-500'} rounded-lg flex items-center justify-center`}>
            <Icon className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className={`text-2xl font-bold ${textClasses[color] || 'text-gray-900'}`}>
            {value}
          </div>
        </CardContent>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-48 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Language Management</h1>
        <button
          onClick={() => setShowModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-md flex items-center gap-2 hover:bg-blue-700"
        >
          <Plus className="h-4 w-4" />
          Add Language
        </button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
        <StatCard
          title="Total Languages"
          value={statistics.total_languages || 0}
          icon={Languages}
          color="bg-blue-500"
        />
        <StatCard
          title="Active Languages"
          value={statistics.active_languages || 0}
          icon={Globe}
          color="bg-green-500"
        />
        <StatCard
          title="Default Language"
          value={statistics.default_language || 'None'}
          icon={Star}
          color="bg-yellow-500"
        />
        <StatCard
          title="LTR Languages"
          value={statistics.ltr_languages || 0}
          icon={Languages}
          color="bg-purple-500"
        />
        <StatCard
          title="RTL Languages"
          value={statistics.rtl_languages || 0}
          icon={Languages}
          color="bg-orange-500"
        />
      </div>

      {/* Search */}
      <div className="bg-white rounded-lg shadow mb-6">
        <div className="p-4 border-b">
          <div className="flex items-center space-x-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search languages..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border rounded-md w-full focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Languages Table */}
      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Code</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Native Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Direction</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Default</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {languages.map((language) => (
              <tr key={language.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <span className="text-2xl mr-2">{language.flag || '🌐'}</span>
                    <span className="font-medium text-gray-900">{language.name}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{language.code}</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{language.native_name || '-'}</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    language.direction === 'ltr' ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                  }`}>
                    {language.direction.toUpperCase()}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    language.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {language.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <button
                      onClick={() => handleSetDefault(language.id)}
                      disabled={language.status !== 'active'}
                      className={`relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                        language.is_default ? 'bg-blue-600' : 'bg-gray-200'
                      } ${language.status !== 'active' ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      <span
                        className={`pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out ${
                          language.is_default ? 'translate-x-5' : 'translate-x-0'
                        }`}
                      />
                    </button>
                    {language.is_default && (
                      <Star className="h-4 w-4 text-yellow-500 ml-2" />
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleEdit(language)}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(language.id)}
                      className="text-red-600 hover:text-red-900"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Modal */}
      <ContentModal open={showModal} onOpenChange={(open) => {
        if (!open) {
          setShowModal(false);
          setEditingLanguage(null);
          setFormData({ name: '', code: '', is_default: false });
        }
      }}>
        <ContentModalOverlay />
        <ContentModalContent className="w-full max-w-md">
          <ContentModalHeader className="mb-4">
            <ContentModalTitle className="text-lg font-semibold">
              {editingLanguage ? 'Edit Language' : 'Add New Language'}
            </ContentModalTitle>
          </ContentModalHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Code</label>
                <input
                  type="text"
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Native Name</label>
                <input
                  type="text"
                  value={formData.native_name}
                  onChange={(e) => setFormData({ ...formData, native_name: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Flag (emoji)</label>
                <input
                  type="text"
                  value={formData.flag}
                  onChange={(e) => setFormData({ ...formData, flag: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Direction</label>
                <select
                  value={formData.direction}
                  onChange={(e) => setFormData({ ...formData, direction: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="ltr">Left to Right (LTR)</option>
                  <option value="rtl">Right to Left (RTL)</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={formData.status}
                  onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="is_default"
                  checked={formData.is_default}
                  onChange={(e) => setFormData({ ...formData, is_default: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <label htmlFor="is_default" className="ml-2 block text-sm text-gray-900">
                  Set as default language
                </label>
              </div>
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => {
                    setShowModal(false);
                    setEditingLanguage(null);
                    setFormData({ name: '', code: '', native_name: '', flag: '', direction: 'ltr', status: 'active', is_default: false });
                  }}
                  className="px-4 py-2 text-gray-600 border rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  {editingLanguage ? 'Update' : 'Create'}
                </button>
              </div>
            </form>
        </ContentModalContent>
      </ContentModal>
    </div>
  );
};

export default LanguagePage;

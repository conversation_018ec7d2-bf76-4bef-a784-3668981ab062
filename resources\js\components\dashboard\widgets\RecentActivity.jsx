import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

const RecentActivity = ({ activities = [] }) => {
  const defaultActivities = [
    {
      id: 1,
      user: "John Doe",
      action: "created a new order",
      target: "#12345",
      time: "2 minutes ago",
      type: "order"
    },
    {
      id: 2,
      user: "<PERSON>",
      action: "updated customer profile",
      target: "Acme Corp",
      time: "5 minutes ago",
      type: "customer"
    },
    {
      id: 3,
      user: "<PERSON>",
      action: "completed payment",
      target: "$1,234.56",
      time: "10 minutes ago",
      type: "payment"
    },
    {
      id: 4,
      user: "<PERSON>",
      action: "sent invoice",
      target: "#INV-001",
      time: "15 minutes ago",
      type: "invoice"
    },
    {
      id: 5,
      user: "<PERSON>",
      action: "registered new account",
      target: "Premium Plan",
      time: "1 hour ago",
      avatar: "/avatars/05.png",
      type: "registration"
    }
  ];

  const activityList = activities.length > 0 ? activities : defaultActivities;

  const getTypeBadge = (type) => {
    switch (type) {
      case 'order':
        return <Badge variant="default">Order</Badge>;
      case 'customer':
        return <Badge variant="secondary">Customer</Badge>;
      case 'payment':
        return <Badge variant="outline">Payment</Badge>;
      case 'invoice':
        return <Badge variant="destructive">Invoice</Badge>;
      case 'registration':
        return <Badge variant="default">New User</Badge>;
      default:
        return <Badge variant="secondary">{type}</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>
          Latest actions and updates from your team
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activityList.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3">
              <Avatar className="h-8 w-8">
                <AvatarFallback>
                  {activity.user.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 space-y-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm">
                    <span className="font-medium">{activity.user}</span>
                    {' '}{activity.action}{' '}
                    <span className="font-medium">{activity.target}</span>
                  </p>
                  {getTypeBadge(activity.type)}
                </div>
                <p className="text-xs text-muted-foreground">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default RecentActivity;

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use App\Traits\AuditableTrait;

class Project extends Model
{
    use HasFactory, SoftDeletes, AuditableTrait;

    protected $fillable = [
        'title',
        'description',
        'property_type_id', // Changed from property_type to property_type_id
        'property_status_id', // Changed from status to property_status_id
        'property_stage',
        'location',
        'location_id',
        'address',
        'city',
        'state_id',
        'country_id',
        'postal_code',
        'latitude',
        'longitude',
        'area_sqft',
        'price_per_area_sqft',
        'total_price',
        'bedrooms',
        'bathrooms',
        'floors',
        'parking_spaces',
        'year_built',
        'amenities',
        'features',
        'is_featured',
        'is_available',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'amenities' => 'array',
        'features' => 'array',
        'is_featured' => 'boolean',
        'is_available' => 'boolean',
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'area_sqft' => 'decimal:2',
        'price_per_area_sqft' => 'decimal:2',
        'total_price' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * The attributes that should be hidden for serialization.
     */
    protected $hidden = [];

    /**
     * Property types enum values
     */
    const PROPERTY_TYPES = [
        'residential' => 'Residential',
        'commercial' => 'Commercial',
        'industrial' => 'Industrial',
        'land' => 'Land',
        'mixed' => 'Mixed Use',
    ];

    /**
     * Status enum values
     */
    const STATUSES = [
        'planning' => 'Planning',
        'under_construction' => 'Under Construction',
        'completed' => 'Completed',
        'sold' => 'Sold',
        'rented' => 'Rented',
        'maintenance' => 'Under Maintenance',
    ];

    /**
     * Property stage enum values
     */
    const PROPERTY_STAGES = [
        'pre_planning' => 'Pre-Planning',
        'planning' => 'Planning',
        'design' => 'Design',
        'approvals' => 'Approvals',
        'pre_construction' => 'Pre-Construction',
        'foundation' => 'Foundation',
        'structure' => 'Structure',
        'roofing' => 'Roofing',
        'exterior' => 'Exterior Work',
        'interior' => 'Interior Work',
        'finishing' => 'Finishing',
        'inspection' => 'Inspection',
        'completed' => 'Completed',
        'handover' => 'Handover',
        'warranty' => 'Warranty Period',
    ];

    /**
     * Get formatted property stage name
     */
    public function getPropertyStageNameAttribute()
    {
        return self::PROPERTY_STAGES[$this->property_stage] ?? ucfirst(str_replace('_', ' ', $this->property_stage));
    }

    /**
     * Scope: Filter by property stage
     */
    public function scopeByPropertyStage($query, $stage)
    {
        return $query->where('property_stage', $stage);
    }

    /**
     * Get stage completion percentage (rough estimate)
     */
    public function getStageCompletionPercentage()
    {
        $stages = array_keys(self::PROPERTY_STAGES);
        $currentIndex = array_search($this->property_stage, $stages);
        
        if ($currentIndex === false) {
            return 0;
        }
        
        return round(($currentIndex / (count($stages) - 1)) * 100);
    }

    /**
     * Relationship: Project has many units
     */
    public function units()
    {
        return $this->hasMany(ProjectUnit::class);
    }

    /**
     * Relationship: Project has many images
     */
    public function images()
    {
        return $this->hasMany(ProjectImage::class)->orderBy('sort_order');
    }

    /**
     * Relationship: Project has many videos
     */
    public function videos()
    {
        return $this->hasMany(ProjectVideo::class)->orderBy('sort_order');
    }

    /**
     * Relationship: Project belongs to a creator (User)
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship: Project belongs to an updater (User)
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Relationship: Project belongs to a country
     */
    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    /**
     * Relationship: Project belongs to a state
     */
    public function state()
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    /**
     * Relationship: Project belongs to a location
     */
    public function location()
    {
        return $this->belongsTo(Location::class, 'location_id');
    }

    /**
     * Get the property type that owns the project.
     */
    public function propertyType()
    {
        return $this->belongsTo(PropertyType::class, 'property_type_id');
    }

    /**
     * Get the property status that owns the project.
     */
    public function propertyStatus()
    {
        return $this->belongsTo(PropertyStatus::class, 'property_status_id');
    }

    /**
     * Scope: Featured projects
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope: Available projects
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope: Filter by property type
     */
    public function scopePropertyType($query, $type)
    {
        if (is_numeric($type)) {
            return $query->where('property_type_id', $type);
        }
        
        // For backward compatibility, also support property type name/slug
        return $query->whereHas('propertyType', function($q) use ($type) {
            $q->where('slug', $type)->orWhere('name', $type);
        });
    }

    /**
     * Scope: Filter by status (legacy support)
     */
    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope: Filter by property status
     */
    public function scopePropertyStatus($query, $statusId)
    {
        return $query->where('property_status_id', $statusId);
    }

    /**
     * Scope: Filter by property status slug
     */
    public function scopePropertyStatusSlug($query, $statusSlug)
    {
        return $query->whereHas('propertyStatus', function ($q) use ($statusSlug) {
            $q->where('slug', $statusSlug);
        });
    }

    /**
     * Scope: Filter by location (city, state, or country)
     */
    public function scopeLocation($query, $location)
    {
        return $query->where(function ($q) use ($location) {
            $q->where('city', 'like', "%{$location}%")
              ->orWhere('state', 'like', "%{$location}%")
              ->orWhere('country', 'like', "%{$location}%")
              ->orWhere('location', 'like', "%{$location}%");
        });
    }

    /**
     * Scope: Search across multiple fields
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('title', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('location', 'like', "%{$search}%")
              ->orWhere('address', 'like', "%{$search}%")
              ->orWhere('city', 'like', "%{$search}%")
              ->orWhere('state', 'like', "%{$search}%");
        });
    }

    /**
     * Accessor: Get property type label
     */
    public function getPropertyTypeLabelAttribute()
    {
        return $this->propertyType ? $this->propertyType->name : 'Unknown Property Type';
    }

    /**
     * Accessor: Get status label (supports both new and legacy status)
     */
    public function getStatusLabelAttribute()
    {
        // If we have a property status relationship, use it
        if ($this->propertyStatus) {
            return $this->propertyStatus->name;
        }
        
        // Fall back to legacy status enum for backward compatibility
        return self::STATUSES[$this->status] ?? $this->status;
    }

    /**
     * Accessor: Get property status name
     */
    public function getPropertyStatusNameAttribute()
    {
        return $this->propertyStatus?->name ?? 'Unknown';
    }

    /**
     * Accessor: Get property status color
     */
    public function getPropertyStatusColorAttribute()
    {
        return $this->propertyStatus?->color ?? '#6B7280';
    }

    /**
     * Accessor: Get property status slug
     */
    public function getPropertyStatusSlugAttribute()
    {
        return $this->propertyStatus?->slug ?? '';
    }

    /**
     * Accessor: Get featured image
     */
    public function getFeaturedImageAttribute()
    {
        return $this->images()->where('is_featured', true)->first() ?: $this->images()->first();
    }

    /**
     * Accessor: Get featured video
     */
    public function getFeaturedVideoAttribute()
    {
        return $this->videos()->where('is_featured', true)->first() ?: $this->videos()->first();
    }

    /**
     * Accessor: Get available units count
     */
    public function getAvailableUnitsCountAttribute()
    {
        return $this->units()->where('is_available', true)->count();
    }

    /**
     * Accessor: Get total units count
     */
    public function getTotalUnitsCountAttribute()
    {
        return $this->units()->count();
    }

    /**
     * Accessor: Get images count
     */
    public function getImagesCountAttribute()
    {
        return $this->images()->count();
    }

    /**
     * Accessor: Get videos count
     */
    public function getVideosCountAttribute()
    {
        return $this->videos()->count();
    }

    /**
     * Get project statistics
     */
    public static function getStatistics()
    {
        return [
            'total' => self::count(),
            'featured' => self::where('is_featured', true)->count(),
            'available' => self::where('is_available', true)->count(),
            'planning' => self::whereHas('propertyStatus', function($q) { $q->where('slug', 'planning'); })->count(),
            'under_construction' => self::whereHas('propertyStatus', function($q) { $q->where('slug', 'under_construction'); })->count(),
            'completed' => self::whereHas('propertyStatus', function($q) { $q->where('slug', 'completed'); })->count(),
            'sold' => self::whereHas('propertyStatus', function($q) { $q->where('slug', 'sold'); })->count(),
            'rented' => self::whereHas('propertyStatus', function($q) { $q->where('slug', 'rented'); })->count(),
            'maintenance' => self::whereHas('propertyStatus', function($q) { $q->where('slug', 'maintenance'); })->count(),
        ];
    }

    /**
     * Boot method for model events
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-set created_by and updated_by when creating/updating
        static::creating(function ($model) {
            if (auth()->check()) {
                $model->created_by = auth()->id();
                $model->updated_by = auth()->id();
            }
        });

        static::updating(function ($model) {
            if (auth()->check()) {
                $model->updated_by = auth()->id();
            }
        });
    }
}

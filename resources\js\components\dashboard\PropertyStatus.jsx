import React, { useState } from "react";

const propertiesData = [
  { id: 1, title: "Luxury Villa in Dhaka", status: "Available", price: "$500,000" },
  { id: 2, title: "Beachside Apartment", status: "Sold", price: "$300,000" },
  { id: 3, title: "City Center Condo", status: "Pending", price: "$250,000" },
  { id: 4, title: "Suburban House", status: "Available", price: "$200,000" },
];

const statusColors = {
  Available: "bg-green-100 text-green-800",
  Sold: "bg-red-100 text-red-800",
  Pending: "bg-yellow-100 text-yellow-800",
};

export default function PropertyStatus() {
  const [statusFilter, setStatusFilter] = useState("All");
  const [search, setSearch] = useState("");

  const filteredProperties = propertiesData.filter((p) => {
    const matchStatus = statusFilter === "All" || p.status === statusFilter;
    const matchSearch = p.title.toLowerCase().includes(search.toLowerCase());
    return matchStatus && matchSearch;
  });

  return (
    <div className="p-6 max-w-5xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Property Status</h1>

      {/* Filters */}
      <div className="flex flex-col md:flex-row gap-3 mb-6">
        <input
          type="text"
          placeholder="Search properties..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="border rounded px-3 py-2 w-full md:w-1/3"
        />

        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="border rounded px-3 py-2 w-full md:w-1/4"
        >
          <option value="All">All Status</option>
          <option value="Available">Available</option>
          <option value="Sold">Sold</option>
          <option value="Pending">Pending</option>
        </select>
      </div>

      {/* Property List */}
      <div className="grid md:grid-cols-2 gap-4">
        {filteredProperties.length > 0 ? (
          filteredProperties.map((property) => (
            <div
              key={property.id}
              className="border rounded-lg p-4 shadow-sm hover:shadow-md transition"
            >
              <h2 className="text-lg font-semibold">{property.title}</h2>
              <p className="text-gray-600">{property.price}</p>
              <span
                className={`inline-block mt-2 px-3 py-1 text-sm rounded-full ${statusColors[property.status]}`}
              >
                {property.status}
              </span>
            </div>
          ))
        ) : (
          <p className="text-gray-500 col-span-2 text-center">
            No properties found.
          </p>
        )}
      </div>
    </div>
  );
}

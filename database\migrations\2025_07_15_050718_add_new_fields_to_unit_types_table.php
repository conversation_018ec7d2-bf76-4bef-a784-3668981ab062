<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('unit_types', function (Blueprint $table) {
            $table->decimal('flat_size', 10, 2)->nullable()->after('total_size_sqft');
            $table->integer('total_bed_room')->default(0)->after('flat_size');
            $table->integer('total_bathroom')->default(0)->after('total_bed_room');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('unit_types', function (Blueprint $table) {
            $table->dropColumn(['flat_size', 'total_bed_room', 'total_bathroom']);
        });
    }
};

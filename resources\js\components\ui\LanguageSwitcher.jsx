import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useTranslation } from '@/hooks/useTranslation';
import { languageAPI } from '@/services/languageAPI';
import { Globe, Languages, Check } from 'lucide-react';

const LanguageSwitcher = ({ variant = 'card', size = 'default' }) => {
  const { t, currentLanguage, changeLanguage } = useTranslation();
  const [availableLanguages, setAvailableLanguages] = React.useState([]);
  const [isLoading, setIsLoading] = React.useState(false);

  React.useEffect(() => {
    loadAvailableLanguages();
  }, []);

  const loadAvailableLanguages = async () => {
    try {
      const response = await languageAPI.getLanguages({ 
        search: '', 
        page: 1, 
        per_page: 100 
      });
      if (response.success) {
        // Only show active languages
        const activeLanguages = response.data.filter(lang => lang.status === 'active');
        setAvailableLanguages(activeLanguages);
      }
    } catch (error) {
      console.error('Error loading languages:', error);
    }
  };

  const handleLanguageChange = async (languageCode) => {
    setIsLoading(true);
    try {
      await changeLanguage(languageCode);
    } catch (error) {
      console.error('Error changing language:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // If variant is dropdown, render dropdown version
  if (variant === 'dropdown') {
    const currentLang = availableLanguages.find(lang => lang.code === currentLanguage);
    const displayName = currentLang?.native_name || currentLang?.name || currentLanguage;

    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" size={size} disabled={isLoading}>
            <Globe className="mr-2 h-4 w-4" />
            {currentLang?.flag} {displayName}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          {availableLanguages.map((language) => (
            <DropdownMenuItem
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className={`cursor-pointer ${
                language.code === currentLanguage
                  ? 'bg-accent text-accent-foreground'
                  : ''
              }`}
            >
              <span className="mr-2">{language.flag}</span>
              <span className="flex-1">{language.native_name}</span>
              {language.code === currentLanguage && (
                <Check className="h-4 w-4 ml-2" />
              )}
            </DropdownMenuItem>
          ))}
        </DropdownMenuContent>
      </DropdownMenu>
    );
  }

  // Default card variant

  return (
    <Card className="w-full max-w-md">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Languages className="h-5 w-5" />
          {t('settings.language')}
        </CardTitle>
        <CardDescription>
          {t('settings.languageDescription')}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-3">
        {availableLanguages.map((language) => (
          <div
            key={language.code}
            className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
          >
            <div className="flex items-center gap-3">
              <span className="text-2xl">{language.flag}</span>
              <div>
                <p className="font-medium">{language.name}</p>
                <p className="text-sm text-gray-500">{language.native_name}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {language.is_default && (
                <Badge variant="secondary" className="text-xs">
                  {t('language.default')}
                </Badge>
              )}
              <Button
                variant={currentLanguage === language.code ? "default" : "outline"}
                size="sm"
                onClick={() => handleLanguageChange(language.code)}
                disabled={isLoading || currentLanguage === language.code}
              >
                {currentLanguage === language.code ? t('language.current') : t('language.select')}
              </Button>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
};

export default LanguageSwitcher;

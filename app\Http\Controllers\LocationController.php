<?php

namespace App\Http\Controllers;

use App\Models\Location;
use App\Models\State;
use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class LocationController extends Controller
{
    /**
     * Display a listing of the locations.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Location::with(['state:id,name', 'country:id,name,code']);

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->search($search);
            }

            // Filter by state
            if ($request->has('state_id') && $request->state_id) {
                $query->where('state_id', $request->state_id);
            }

            // Filter by country
            if ($request->has('country_id') && $request->country_id) {
                $query->where('country_id', $request->country_id);
            }

            // Filter by status
            if ($request->has('status') && $request->status) {
                $query->where('status', $request->status);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'name');
            $sortOrder = $request->get('sort_order', 'asc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 15);
            $locations = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $locations->items(),
                'pagination' => [
                    'current_page' => $locations->currentPage(),
                    'last_page' => $locations->lastPage(),
                    'per_page' => $locations->perPage(),
                    'total' => $locations->total()
                ],
                'message' => 'Locations retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving locations: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created location in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // Log the incoming request data for debugging
            \Log::info('LocationController: Creating location', [
                'user_id' => auth()->id(),
                'request_data' => $request->all()
            ]);

            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'state_id' => 'required|exists:states,id',
                'country_id' => 'nullable|exists:countries,id',
                'status' => 'required|in:active,inactive'
            ]);

         

            // If country_id not provided, get it from the state
            if (!isset($validatedData['country_id'])) {
                $state = State::findOrFail($validatedData['state_id']);
                $validatedData['country_id'] = $state->country_id;
            }

            // Check for unique combination of name and state
            $existingLocation = Location::where('name', $validatedData['name'])
                ->where('state_id', $validatedData['state_id'])
                ->first();

            if ($existingLocation) {
                return response()->json([
                    'success' => false,
                    'message' => 'A location with this name already exists in the selected state',
                    'errors' => ['name' => ['This location name already exists in the selected state']]
                ], 422);
            }

            $location = Location::create($validatedData);
            $location->load(['state:id,name', 'country:id,name,code']);

          

            return response()->json([
                'success' => true,
                'data' => $location,
                'message' => 'Location created successfully'
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
           
            
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
           
            
            return response()->json([
                'success' => false,
                'message' => 'Error creating location: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified location.
     */
    public function show(Location $location): JsonResponse
    {
        try {
            $location->load(['state:id,name', 'country:id,name,code']);
            
            return response()->json([
                'success' => true,
                'data' => $location,
                'message' => 'Location retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving location: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified location in storage.
     */
    public function update(Request $request, Location $location): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'state_id' => 'required|exists:states,id',
                'country_id' => 'nullable|exists:countries,id',
                'status' => 'required|in:active,inactive'
            ]);

            // If country_id not provided, get it from the state
            if (!isset($validatedData['country_id'])) {
                $state = State::findOrFail($validatedData['state_id']);
                $validatedData['country_id'] = $state->country_id;
            }

            // Check for unique combination of name and state (excluding current location)
            $existingLocation = Location::where('name', $validatedData['name'])
                ->where('state_id', $validatedData['state_id'])
                ->where('id', '!=', $location->id)
                ->first();

            if ($existingLocation) {
                return response()->json([
                    'success' => false,
                    'message' => 'A location with this name already exists in the selected state',
                    'errors' => ['name' => ['This location name already exists in the selected state']]
                ], 422);
            }

            $location->update($validatedData);
            $location->load(['state:id,name', 'country:id,name,code']);

            return response()->json([
                'success' => true,
                'data' => $location,
                'message' => 'Location updated successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating location: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified location from storage.
     */
    public function destroy(Location $location): JsonResponse
    {
        try {
            $location->delete();
            
            return response()->json([
                'success' => true,
                'message' => 'Location deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting location: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle the status of the specified location.
     */
    public function toggleStatus(Location $location): JsonResponse
    {
        try {
            $newStatus = $location->status === 'active' ? 'inactive' : 'active';
            $location->update(['status' => $newStatus]);
            
            return response()->json([
                'success' => true,
                'data' => $location,
                'message' => "Location status changed to {$newStatus}"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error toggling location status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get locations dropdown data
     */
    public function dropdown(Request $request): JsonResponse
    {
        try {
            $query = Location::select('id', 'name', 'state_id', 'country_id')
                ->with(['state:id,name', 'country:id,name'])
                ->active()
                ->orderBy('name');

            // Filter by state
            if ($request->has('state_id') && $request->state_id) {
                $query->where('state_id', $request->state_id);
            }

            // Filter by country
            if ($request->has('country_id') && $request->country_id) {
                $query->where('country_id', $request->country_id);
            }

            $locations = $query->get();

            return response()->json([
                'success' => true,
                'data' => $locations,
                'message' => 'Locations dropdown retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving locations dropdown: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get locations by state
     */
    public function byState(Request $request, $stateId): JsonResponse
    {
        try {
            $locations = Location::byState($stateId)
                ->with(['state:id,name', 'country:id,name'])
                ->active()
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $locations,
                'message' => 'Locations retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving locations by state: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get locations by country
     */
    public function byCountry(Request $request, $countryId): JsonResponse
    {
        try {
            $locations = Location::byCountry($countryId)
                ->with(['state:id,name', 'country:id,name'])
                ->active()
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $locations,
                'message' => 'Locations retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving locations by country: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total_locations' => Location::count(),
                'active_locations' => Location::active()->count(),
                'inactive_locations' => Location::inactive()->count(),
                'locations_by_state' => Location::select('state_id', DB::raw('count(*) as total'))
                    ->with('state:id,name')
                    ->groupBy('state_id')
                    ->orderByDesc('total')
                    ->limit(10)
                    ->get(),
                'locations_by_country' => Location::select('country_id', DB::raw('count(*) as total'))
                    ->with('country:id,name')
                    ->groupBy('country_id')
                    ->orderByDesc('total')
                    ->limit(10)
                    ->get()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving statistics: ' . $e->getMessage()
            ], 500);
        }
    }
}

export default {
  // Common UI elements
  common: {
    buttons: {
      add: '追加',
      edit: '編集',
      delete: '削除',
      save: '保存',
      cancel: 'キャンセル',
      confirm: '確認',
      close: '閉じる',
      search: '検索',
      filter: 'フィルター',
      export: 'エクスポート',
      import: 'インポート',
      refresh: '更新',
      loading: '読み込み中...',
      submit: '送信',
      reset: 'リセット',
      clear: 'クリア',
      view: '表示',
      download: 'ダウンロード',
      upload: 'アップロード'
    },
    status: {
      active: 'アクティブ',
      inactive: '非アクティブ',
      pending: '保留中',
      approved: '承認済み',
      rejected: '却下',
      completed: '完了',
      draft: '下書き',
      published: '公開済み'
    },
    messages: {
      success: '操作が正常に完了しました',
      error: 'エラーが発生しました',
      warning: '警告',
      info: '情報',
      loading: '読み込み中です。しばらくお待ちください...',
      noData: 'データがありません',
      confirmDelete: 'このアイテムを削除してもよろしいですか？',
      confirmAction: 'この操作を実行してもよろしいですか？',
      saved: '変更が正常に保存されました',
      deleted: 'アイテムが正常に削除されました',
      updated: 'アイテムが正常に更新されました',
      created: 'アイテムが正常に作成されました'
    },
    navigation: {
      dashboard: 'ダッシュボード',
      analytics: '分析',
      landOwners: '土地所有者',
      landAcquisition: '土地取得',
      lifecycle: 'ライフサイクル',
      country: '国',
      language: '言語',
      currency: '通貨',
      customers: '顧客',
      orders: '注文',
      components: 'コンポーネント',
      reports: 'レポート',
      roleManagement: '役割管理',
      wordAssistant: 'ワードアシスタント',
      settings: '設定',
      documentsAdmin: 'ドキュメントと管理',
      landDevelopment: '土地と開発',
      hrManagement: '人事管理',
      developmentCosting: '主要開発コスト'
    },
    forms: {
      required: 'このフィールドは必須です',
      invalidEmail: '有効なメールアドレスを入力してください',
      invalidPhone: '有効な電話番号を入力してください',
      passwordMismatch: 'パスワードが一致しません',
      minLength: '最小文字数は {min} 文字です',
      maxLength: '最大文字数は {max} 文字です',
      selectOption: 'オプションを選択してください',
      invalidDate: '有効な日付を入力してください',
      invalidNumber: '有効な数値を入力してください'
    },
    table: {
      name: '名前',
      code: 'コード',
      status: 'ステータス',
      actions: 'アクション',
      createdAt: '作成日',
      updatedAt: '更新日',
      noRecords: 'レコードが見つかりません',
      showing: '{total} 件中 {start} から {end} を表示',
      previous: '前へ',
      next: '次へ',
      rowsPerPage: 'ページあたりの行数'
    },
    auditLog: '監査ログ',
    loading: '読み込み中...',
    noData: 'データがありません'
  },

  // Dashboard specific
  dashboard: {
    title: 'ダッシュボード',
    description: '今日のビジネスの状況をご覧ください。',
    welcome: 'おかえりなさい、{name}さん！',
    overview: '概要',
    statistics: '統計',
    recentActivity: '最近のアクティビティ',
    quickActions: 'クイックアクション',
    viewReport: 'レポートを表示',
    statistics: {
      totalRevenue: '総収入',
      subscriptions: 'サブスクリプション',
      sales: '売上',
      activeNow: '現在アクティブ',
      fromLastMonth: '先月から',
      fromLastHour: '先1時間から'
    }
  },

  // Language Management
  language: {
    title: '言語管理',
    addLanguage: '言語を追加',
    editLanguage: '言語を編集',
    default: 'デフォルト',
    current: '現在',
    select: '選択',
    fields: {
      name: '名前',
      code: 'コード',
      nativeName: 'ネイティブ名',
      flag: 'フラグ（絵文字）',
      direction: '方向',
      status: 'ステータス',
      isDefault: 'デフォルト言語として設定'
    },
    direction: {
      ltr: '左から右 (LTR)',
      rtl: '右から左 (RTL)'
    },
    statistics: {
      totalLanguages: '言語総数',
      activeLanguages: 'アクティブな言語',
      defaultLanguage: 'デフォルト言語',
      ltrLanguages: 'LTR言語',
      rtlLanguages: 'RTL言語'
    },
    messages: {
      setDefault: 'デフォルト言語として設定しますか？',
      setDefaultText: 'この言語をシステムのデフォルトにします',
      setDefaultConfirm: 'はい、デフォルトに設定します！',
      defaultUpdated: 'デフォルト言語が正常に更新されました',
      onlyActiveCanBeDefault: 'アクティブな言語のみデフォルトに設定できます'
    }
  },

  // Country Management
  country: {
    title: '国管理',
    addCountry: '国を追加',
    editCountry: '国を編集',
    fields: {
      name: '名前',
      code: 'コード',
      flag: 'フラグ',
      status: 'ステータス'
    },
    statistics: {
      totalCountries: '国総数',
      activeCountries: 'アクティブな国'
    }
  },

  // Currency Management
  currency: {
    title: '通貨管理',
    addCurrency: '通貨を追加',
    editCurrency: '通貨を編集',
    fields: {
      name: '名前',
      code: 'コード',
      symbol: 'シンボル',
      exchangeRate: '為替レート',
      status: 'ステータス'
    },
    statistics: {
      totalCurrencies: '通貨総数',
      activeCurrencies: 'アクティブな通貨'
    }
  },

  // Land Owners
  landOwners: {
    title: '土地所有者',
    addLandOwner: '土地所有者を追加',
    editLandOwner: '土地所有者を編集',
    fields: {
      name: '名前',
      email: 'メール',
      phone: '電話',
      address: '住所',
      status: 'ステータス'
    },
    statistics: {
      totalLandOwners: '土地所有者総数',
      activeLandOwners: 'アクティブな土地所有者'
    }
  },

  // Land Acquisition
  landAcquisition: {
    title: '土地取得',
    addAcquisition: '取得を追加',
    editAcquisition: '取得を編集',
    fields: {
      title: 'タイトル',
      description: '説明',
      area: '面積',
      price: '価格',
      status: 'ステータス',
      location: '場所'
    },
    statistics: {
      totalAcquisitions: '取得総数',
      activeAcquisitions: 'アクティブな取得'
    }
  },

  // Analytics
  analytics: {
    title: '分析',
    overview: '概要',
    performance: 'パフォーマンス',
    trends: 'トレンド',
    reports: 'レポート'
  },

  // Settings
  settings: {
    title: '設定',
    general: '一般',
    security: 'セキュリティ',
    notifications: '通知',
    preferences: '設定'
  },

  // Role Management
  role: {
    title: '役割管理',
    addRole: '役割を追加',
    editRole: '役割を編集',
    fields: {
      name: '名前',
      description: '説明',
      permissions: '権限',
      status: 'ステータス'
    },
    statistics: {
      totalRoles: '役割総数',
      activeRoles: 'アクティブな役割'
    }
  }
};

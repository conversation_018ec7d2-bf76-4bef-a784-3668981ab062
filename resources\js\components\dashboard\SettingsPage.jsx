import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { useAuth } from '../../contexts/AuthContext';
import Swal from 'sweetalert2';
import { 
  Save, 
  Upload, 
  Bell, 
  Shield, 
  CreditCard,
  Users,
  Settings as SettingsIcon,
  Lock,
  Eye,
  EyeOff,
  Globe,
  MapPin,
  Building2,
  Languages,
  DollarSign
} from 'lucide-react';

// Import the specific page components
import CountryPage from './CountryPage';
import StatePage from './StatePage';
import LocationPage from './LocationPage';
import LanguagePage from './LanguagePage';
import CurrencyPage from './CurrencyPage';

const SettingsPage = () => {
  const { user, updateProfile, loading } = useAuth();
  const [activeTab, setActiveTab] = useState('general');
  const [showPassword, setShowPassword] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    company: '',
    bio: '',
    phone: '',
    timezone: 'UTC',
    language: 'English',
    notifications: {
      email: true,
      push: false,
      sms: true,
      marketing: false,
      security: true
    },
    security: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
      twoFactor: false
    }
  });

  // Load user data when component mounts or user changes
  useEffect(() => {
    if (user) {
      setFormData(prev => ({
        ...prev,
        firstName: user.first_name || '',
        lastName: user.last_name || '',
        email: user.email || '',
        company: user.company || '',
        bio: user.bio || '',
        phone: user.phone || '',
        timezone: user.timezone || 'UTC',
        language: user.language || 'English'
      }));
    }
  }, [user]);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNotificationChange = (type, value) => {
    setFormData(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [type]: value
      }
    }));
  };

  const handleSecurityChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      security: {
        ...prev.security,
        [field]: value
      }
    }));
  };

  const tabSections = [
    {
      title: 'Account',
      tabs: [
        { id: 'general', label: 'General', icon: SettingsIcon },
        { id: 'notifications', label: 'Notifications', icon: Bell },
        { id: 'security', label: 'Security', icon: Shield }
      ]
    },
    {
      title: 'System Configuration',
      tabs: [
        { id: 'countries', label: 'Countries', icon: Globe },
        { id: 'states', label: 'States/Provinces', icon: MapPin },
        { id: 'location', label: 'Locations', icon: Building2 },
        { id: 'languages', label: 'Languages', icon: Languages },
        { id: 'currencies', label: 'Currencies', icon: DollarSign }

      ]
    },
    {
      title: 'Organization',
      tabs: [
        { id: 'billing', label: 'Billing', icon: CreditCard },
        { id: 'team', label: 'Team', icon: Users }
      ]
    }
  ];

  // Flatten tabs for easier access
  const tabs = tabSections.flatMap(section => section.tabs);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      if (activeTab === 'general') {
        // Update user profile
        const profileData = {
          first_name: formData.firstName,
          last_name: formData.lastName,
          email: formData.email,
          company: formData.company,
          bio: formData.bio,
          phone: formData.phone,
          timezone: formData.timezone,
          language: formData.language
        };
        
        const response = await updateProfile(profileData);
        if (response.success) {
          Swal.fire({
            title: 'Success!',
            text: 'Profile updated successfully!',
            icon: 'success',
            confirmButtonText: 'OK'
          });
        } else {
          Swal.fire({
            title: 'Error!',
            text: response.message || 'Failed to update profile',
            icon: 'error',
            confirmButtonText: 'OK'
          });
        }
      } else {
        // For other tabs, just show a success message for now
        console.log('Saving settings for tab:', activeTab, formData);
        Swal.fire({
          title: 'Success!',
          text: 'Settings saved successfully!',
          icon: 'success',
          confirmButtonText: 'OK'
        });
      }
    } catch (error) {
      console.error('Error saving settings:', error);
      Swal.fire({
        title: 'Error!',
        text: 'Error saving settings: ' + error.message,
        icon: 'error',
        confirmButtonText: 'OK'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const getInitials = () => {
    if (!user) return 'U';
    const firstName = user.first_name || '';
    const lastName = user.last_name || '';
    return (firstName.charAt(0) + lastName.charAt(0)).toUpperCase() || user.email?.charAt(0).toUpperCase() || 'U';
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralTab();
      case 'notifications':
        return renderNotificationsTab();
      case 'security':
        return renderSecurityTab();
      case 'countries':
        return <CountryPage />;
      case 'states':
        return <StatePage />;
      case 'location':
        return <LocationPage />;
      case 'languages':
        return <LanguagePage />;
      case 'currencies':
        return <CurrencyPage />;
      case 'billing':
        return renderBillingTab();
      case 'team':
        return renderTeamTab();
      default:
        return renderGeneralTab();
    }
  };

  const renderGeneralTab = () => (
    <Card>
      <CardHeader>
        <CardTitle>Profile Information</CardTitle>
        <CardDescription>
          Update your personal information and profile picture.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Avatar upload */}
        <div className="flex items-center space-x-4">
          <Avatar className="h-20 w-20">
            <AvatarFallback className="text-xl font-semibold">
              {getInitials()}
            </AvatarFallback>
          </Avatar>
          <div>
            <Button variant="outline" size="sm">
              <Upload className="mr-2 h-4 w-4" />
              Change Avatar
            </Button>
            <p className="text-xs text-muted-foreground mt-1">
              JPG, GIF or PNG. 1MB max.
            </p>
          </div>
        </div>

        <Separator />

        {/* Form fields */}
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="company">Company</Label>
          <Input
            id="company"
            value={formData.company}
            onChange={(e) => handleInputChange('company', e.target.value)}
          />
        </div>

        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="timezone">Timezone</Label>
            <Input
              id="timezone"
              value={formData.timezone}
              onChange={(e) => handleInputChange('timezone', e.target.value)}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="bio">Bio</Label>
          <Textarea
            id="bio"
            value={formData.bio}
            onChange={(e) => handleInputChange('bio', e.target.value)}
            rows={3}
          />
        </div>

        <div className="flex justify-end">
          <Button onClick={handleSave} disabled={isSaving}>
            <Save className="mr-2 h-4 w-4" />
            {isSaving ? 'Saving...' : 'Update Profile'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const renderNotificationsTab = () => (
    <Card>
      <CardHeader>
        <CardTitle>Notification Preferences</CardTitle>
        <CardDescription>
          Choose how you want to be notified about updates.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="font-medium">Email Notifications</p>
            <p className="text-sm text-muted-foreground">
              Receive notifications via email
            </p>
          </div>
          <Switch
            checked={formData.notifications.email}
            onCheckedChange={(checked) => handleNotificationChange('email', checked)}
          />
        </div>

        <Separator />

        <div className="flex items-center justify-between">
          <div>
            <p className="font-medium">Push Notifications</p>
            <p className="text-sm text-muted-foreground">
              Receive push notifications in your browser
            </p>
          </div>
          <Switch
            checked={formData.notifications.push}
            onCheckedChange={(checked) => handleNotificationChange('push', checked)}
          />
        </div>

        <Separator />

        <div className="flex items-center justify-between">
          <div>
            <p className="font-medium">SMS Notifications</p>
            <p className="text-sm text-muted-foreground">
              Receive important updates via SMS
            </p>
          </div>
          <Switch
            checked={formData.notifications.sms}
            onCheckedChange={(checked) => handleNotificationChange('sms', checked)}
          />
        </div>

        <Separator />

        <div className="flex items-center justify-between">
          <div>
            <p className="font-medium">Marketing Emails</p>
            <p className="text-sm text-muted-foreground">
              Receive marketing and promotional emails
            </p>
          </div>
          <Switch
            checked={formData.notifications.marketing}
            onCheckedChange={(checked) => handleNotificationChange('marketing', checked)}
          />
        </div>

        <Separator />

        <div className="flex items-center justify-between">
          <div>
            <p className="font-medium">Security Alerts</p>
            <p className="text-sm text-muted-foreground">
              Receive security-related notifications
            </p>
          </div>
          <Switch
            checked={formData.notifications.security}
            onCheckedChange={(checked) => handleNotificationChange('security', checked)}
          />
        </div>

        <div className="flex justify-end">
          <Button onClick={handleSave} disabled={isSaving}>
            <Save className="mr-2 h-4 w-4" />
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const renderSecurityTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Change Password</CardTitle>
          <CardDescription>
            Update your password to keep your account secure.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="currentPassword">Current Password</Label>
            <div className="relative">
              <Input
                id="currentPassword"
                type={showPassword ? "text" : "password"}
                value={formData.security.currentPassword}
                onChange={(e) => handleSecurityChange('currentPassword', e.target.value)}
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3"
                onClick={() => setShowPassword(!showPassword)}
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </Button>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="newPassword">New Password</Label>
            <Input
              id="newPassword"
              type="password"
              value={formData.security.newPassword}
              onChange={(e) => handleSecurityChange('newPassword', e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword">Confirm New Password</Label>
            <Input
              id="confirmPassword"
              type="password"
              value={formData.security.confirmPassword}
              onChange={(e) => handleSecurityChange('confirmPassword', e.target.value)}
            />
          </div>

          <div className="flex justify-end">
            <Button onClick={handleSave}>
              <Lock className="mr-2 h-4 w-4" />
              Update Password
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Two-Factor Authentication</CardTitle>
          <CardDescription>
            Add an extra layer of security to your account.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="font-medium">Two-Factor Authentication</p>
              <p className="text-sm text-muted-foreground">
                Secure your account with 2FA
              </p>
            </div>
            <Switch
              checked={formData.security.twoFactor}
              onCheckedChange={(checked) => handleSecurityChange('twoFactor', checked)}
            />
          </div>

          {formData.security.twoFactor && (
            <div className="p-4 bg-muted rounded-lg">
              <p className="text-sm font-medium mb-2">Setup Instructions:</p>
              <ol className="text-sm text-muted-foreground space-y-1">
                <li>1. Download an authenticator app (Google Authenticator, Authy, etc.)</li>
                <li>2. Scan the QR code with your authenticator app</li>
                <li>3. Enter the verification code to complete setup</li>
              </ol>
            </div>
          )}

          <div className="flex justify-end">
            <Button onClick={handleSave} disabled={isSaving}>
              <Save className="mr-2 h-4 w-4" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderBillingTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Billing Information</CardTitle>
          <CardDescription>
            Manage your billing details and subscription.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 bg-muted rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h3 className="font-medium">Current Plan</h3>
              <Badge variant="default">Pro Plan</Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              $29.99/month • Next billing date: March 1, 2025
            </p>
          </div>

          <Separator />

          <div className="space-y-4">
            <h3 className="font-medium">Payment Method</h3>
            <div className="flex items-center space-x-4 p-4 border rounded-lg">
              <CreditCard className="h-6 w-6 text-muted-foreground" />
              <div className="flex-1">
                <p className="font-medium">•••• •••• •••• 4242</p>
                <p className="text-sm text-muted-foreground">Expires 12/26</p>
              </div>
              <Button variant="outline" size="sm">
                Update
              </Button>
            </div>
          </div>

          <Separator />

          <div className="space-y-4">
            <h3 className="font-medium">Billing History</h3>
            <div className="space-y-2">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Pro Plan</p>
                  <p className="text-sm text-muted-foreground">February 1, 2025</p>
                </div>
                <p className="font-medium">$29.99</p>
              </div>
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <p className="font-medium">Pro Plan</p>
                  <p className="text-sm text-muted-foreground">January 1, 2025</p>
                </div>
                <p className="font-medium">$29.99</p>
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-2">
            <Button variant="outline">
              Cancel Subscription
            </Button>
            <Button onClick={handleSave} disabled={isSaving}>
              <Save className="mr-2 h-4 w-4" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderTeamTab = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Team Members</CardTitle>
          <CardDescription>
            Manage your team members and their permissions.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex justify-between items-center">
            <div>
              <p className="font-medium">Team Size</p>
              <p className="text-sm text-muted-foreground">5 members</p>
            </div>
            <Button>
              <Users className="mr-2 h-4 w-4" />
              Invite Member
            </Button>
          </div>

          <Separator />

          <div className="space-y-4">
            <h3 className="font-medium">Current Members</h3>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>JD</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">John Doe</p>
                    <p className="text-sm text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="default">Admin</Badge>
                  <Button variant="outline" size="sm">
                    Edit
                  </Button>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>JS</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">Jane Smith</p>
                    <p className="text-sm text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary">Editor</Badge>
                  <Button variant="outline" size="sm">
                    Edit
                  </Button>
                </div>
              </div>

              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback>MB</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">Mike Brown</p>
                    <p className="text-sm text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary">Viewer</Badge>
                  <Button variant="outline" size="sm">
                    Edit
                  </Button>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <Button onClick={handleSave} disabled={isSaving}>
              <Save className="mr-2 h-4 w-4" />
              {isSaving ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground">
            Manage your account settings and preferences.
          </p>
        </div>
      </div>

      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading user data...</p>
          </div>
        </div>
      ) : (
        <div className="grid gap-6 md:grid-cols-3">
          {/* Settings navigation */}
          <div className="space-y-2">
            <Card>
              <CardContent className="p-4">
                <nav className="space-y-4">
                  {tabSections.map((section) => (
                    <div key={section.title}>
                      <h3 className="px-3 mb-2 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
                        {section.title}
                      </h3>
                      <div className="space-y-1">
                        {section.tabs.map((tab) => (
                          <button
                            key={tab.id}
                            onClick={() => setActiveTab(tab.id)}
                            className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                              activeTab === tab.id
                                ? 'bg-primary text-primary-foreground'
                                : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
                            }`}
                          >
                            <tab.icon className="mr-3 h-4 w-4" />
                            {tab.label}
                          </button>
                        ))}
                      </div>
                    </div>
                  ))}
                </nav>
              </CardContent>
            </Card>
          </div>

          {/* Settings content */}
          <div className="md:col-span-2 space-y-6">
            {renderTabContent()}
          </div>
        </div>
      )}
    </div>
  );
};

export default SettingsPage;

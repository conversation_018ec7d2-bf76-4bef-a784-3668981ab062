import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Area, AreaChart, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts';

const VisitorsChart = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('Last 3 months');

  // Sample data for the chart
  const chartData = [
    { date: 'Apr 6', mobile: 45, desktop: 80 },
    { date: 'Apr 12', mobile: 52, desktop: 95 },
    { date: 'Apr 18', mobile: 48, desktop: 87 },
    { date: 'Apr 24', mobile: 61, desktop: 110 },
    { date: 'Apr 30', mobile: 55, desktop: 102 },
    { date: 'May 6', mobile: 67, desktop: 125 },
    { date: 'May 12', mobile: 72, desktop: 140 },
    { date: 'May 18', mobile: 69, desktop: 135 },
    { date: 'May 24', mobile: 78, desktop: 155 },
    { date: 'May 30', mobile: 82, desktop: 165 },
    { date: 'Jun 5', mobile: 88, desktop: 175 },
    { date: 'Jun 11', mobile: 92, desktop: 185 },
    { date: 'Jun 17', mobile: 95, desktop: 190 },
    { date: 'Jun 23', mobile: 98, desktop: 195 },
    { date: 'Jun 30', mobile: 105, desktop: 210 },
  ];

  const periods = ['Last 3 months', 'Last 30 days', 'Last 7 days'];

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const mobileValue = payload.find(p => p.dataKey === 'mobile')?.value || 0;
      const desktopValue = payload.find(p => p.dataKey === 'desktop')?.value || 0;
      
      return (
        <div className="bg-background border rounded-lg shadow-lg p-3">
          <p className="text-sm font-medium mb-2">{label}</p>
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-gray-400"></div>
              <span className="text-sm">Mobile: {mobileValue}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-gray-600"></div>
              <span className="text-sm">Desktop: {desktopValue}</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="col-span-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold">Total Visitors</CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              Total for the last 3 months
            </CardDescription>
          </div>
          <div className="flex gap-1">
            {periods.map((period) => (
              <Button
                key={period}
                variant={selectedPeriod === period ? "default" : "outline"}
                size="sm"
                className="text-xs h-8"
                onClick={() => setSelectedPeriod(period)}
              >
                {period}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={chartData}
              margin={{
                top: 20,
                right: 30,
                left: 0,
                bottom: 0,
              }}
            >
              <defs>
                <linearGradient id="mobileGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#9CA3AF" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#9CA3AF" stopOpacity={0.1}/>
                </linearGradient>
                <linearGradient id="desktopGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#4B5563" stopOpacity={0.4}/>
                  <stop offset="95%" stopColor="#4B5563" stopOpacity={0.1}/>
                </linearGradient>
              </defs>
              <XAxis 
                dataKey="date" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6B7280' }}
                className="text-xs"
              />
              <YAxis hide />
              <Tooltip content={<CustomTooltip />} />
              <Area
                type="monotone"
                dataKey="mobile"
                stackId="1"
                stroke="#9CA3AF"
                strokeWidth={2}
                fill="url(#mobileGradient)"
              />
              <Area
                type="monotone"
                dataKey="desktop"
                stackId="1"
                stroke="#4B5563"
                strokeWidth={2}
                fill="url(#desktopGradient)"
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
        
        {/* Legend */}
        <div className="flex items-center justify-center gap-6 mt-4">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-gray-400"></div>
            <span className="text-sm text-muted-foreground">Mobile</span>
            <span className="text-sm font-medium">190</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-gray-600"></div>
            <span className="text-sm text-muted-foreground">Desktop</span>
            <span className="text-sm font-medium">247</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default VisitorsChart;

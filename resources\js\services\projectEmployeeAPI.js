import axiosInstance from '../config/api';

class ProjectEmployeeAPI {
    constructor() {
        this.baseURL = '/project-employees';
    }

    // Get all project employee assignments with filters
    async getAll(params = {}) {
        try {
            const response = await axiosInstance.get(this.baseURL, { params });
            return response.data;
        } catch (error) {
            console.error('Error fetching project employee assignments:', error);
            throw error;
        }
    }

    // Get project employee assignment by ID
    async getById(id) {
        try {
            const response = await axiosInstance.get(`${this.baseURL}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching project employee assignment:', error);
            throw error;
        }
    }

    // Create new project employee assignment
    async create(data) {
        try {
            const formData = new FormData();
            
            // Append basic data
            Object.keys(data).forEach(key => {
                if (key !== 'documents' && data[key] !== null && data[key] !== undefined) {
                    formData.append(key, data[key]);
                }
            });

            // Append documents if they exist
            if (data.documents && data.documents.length > 0) {
                data.documents.forEach((file, index) => {
                    formData.append(`documents[${index}]`, file);
                });
            }

            const response = await axiosInstance.post(this.baseURL, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response.data;
        } catch (error) {
            console.error('Error creating project employee assignment:', error);
            throw error;
        }
    }

    // Update project employee assignment
    async update(id, data) {
        try {
            const formData = new FormData();
            formData.append('_method', 'PUT');
            
            // Append basic data
            Object.keys(data).forEach(key => {
                if (key !== 'documents' && data[key] !== null && data[key] !== undefined) {
                    formData.append(key, data[key]);
                }
            });

            // Append documents if they exist
            if (data.documents && data.documents.length > 0) {
                data.documents.forEach((file, index) => {
                    formData.append(`documents[${index}]`, file);
                });
            }

            const response = await axiosInstance.post(`${this.baseURL}/${id}`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                },
            });
            return response.data;
        } catch (error) {
            console.error('Error updating project employee assignment:', error);
            throw error;
        }
    }

    // Delete project employee assignment
    async delete(id) {
        try {
            const response = await axiosInstance.delete(`${this.baseURL}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting project employee assignment:', error);
            throw error;
        }
    }

    // Get statistics
    async getStatistics() {
        try {
            const response = await axiosInstance.get(`${this.baseURL}-statistics`);
            return response.data;
        } catch (error) {
            console.error('Error fetching project employee assignment statistics:', error);
            throw error;
        }
    }

    // Get projects for dropdown
    async getProjects() {
        try {
            const response = await axiosInstance.get(`${this.baseURL}-projects`);
            return response.data;
        } catch (error) {
            console.error('Error fetching projects for dropdown:', error);
            throw error;
        }
    }

    // Get employees for dropdown
    async getEmployees() {
        try {
            const response = await axiosInstance.get(`${this.baseURL}-employees`);
            return response.data;
        } catch (error) {
            console.error('Error fetching employees for dropdown:', error);
            throw error;
        }
    }

    // Bulk operations
    async bulkStatusUpdate(ids, status) {
        try {
            const response = await axiosInstance.post(`${this.baseURL}/bulk-status-update`, {
                ids,
                status
            });
            return response.data;
        } catch (error) {
            console.error('Error updating bulk status:', error);
            throw error;
        }
    }

    // Export data
    async export(params = {}) {
        try {
            const response = await axiosInstance.get(`${this.baseURL}/export`, { 
                params,
                responseType: 'blob'
            });
            return response.data;
        } catch (error) {
            console.error('Error exporting project employee assignments:', error);
            throw error;
        }
    }
}

const projectEmployeeAPI = new ProjectEmployeeAPI();
export default projectEmployeeAPI;

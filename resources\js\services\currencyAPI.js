import { API_BASE_URL } from '../config/api';

class CurrencyAPI {
  async getAuthHeaders() {
    const token = localStorage.getItem('auth_token');
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
      'Accept': 'application/json',
    };
  }

  async index(params = {}) {
    try {
      const queryParams = new URLSearchParams();
      
      if (params.search) queryParams.append('search', params.search);
      if (params.active !== undefined) queryParams.append('active', params.active);
      if (params.sort_by) queryParams.append('sort_by', params.sort_by);
      if (params.sort_order) queryParams.append('sort_order', params.sort_order);
      if (params.per_page) queryParams.append('per_page', params.per_page);
      if (params.page) queryParams.append('page', params.page);

      const response = await fetch(`${API_BASE_URL}/currencies?${queryParams}`, {
        method: 'GET',
        headers: await this.getAuthHeaders(),
      });

      return await response.json();
    } catch (error) {
      console.error('Currency API - Index error:', error);
      throw error;
    }
  }

  async show(id) {
    try {
      const response = await fetch(`${API_BASE_URL}/currencies/${id}`, {
        method: 'GET',
        headers: await this.getAuthHeaders(),
      });

      return await response.json();
    } catch (error) {
      console.error('Currency API - Show error:', error);
      throw error;
    }
  }

  async store(currencyData) {
    try {
      const response = await fetch(`${API_BASE_URL}/currencies`, {
        method: 'POST',
        headers: await this.getAuthHeaders(),
        body: JSON.stringify(currencyData),
      });

      return await response.json();
    } catch (error) {
      console.error('Currency API - Store error:', error);
      throw error;
    }
  }

  async update(id, currencyData) {
    try {
      const response = await fetch(`${API_BASE_URL}/currencies/${id}`, {
        method: 'PUT',
        headers: await this.getAuthHeaders(),
        body: JSON.stringify(currencyData),
      });

      return await response.json();
    } catch (error) {
      console.error('Currency API - Update error:', error);
      throw error;
    }
  }

  async destroy(id) {
    try {
      const response = await fetch(`${API_BASE_URL}/currencies/${id}`, {
        method: 'DELETE',
        headers: await this.getAuthHeaders(),
      });

      return await response.json();
    } catch (error) {
      console.error('Currency API - Delete error:', error);
      throw error;
    }
  }

  async getStatistics() {
    try {
      const response = await fetch(`${API_BASE_URL}/currencies-statistics`, {
        method: 'GET',
        headers: await this.getAuthHeaders(),
      });

      return await response.json();
    } catch (error) {
      console.error('Currency API - Statistics error:', error);
      throw error;
    }
  }

  async toggleStatus(id) {
    try {
      const response = await fetch(`${API_BASE_URL}/currencies/${id}/toggle-status`, {
        method: 'PATCH',
        headers: await this.getAuthHeaders(),
      });

      return await response.json();
    } catch (error) {
      console.error('Currency API - Toggle status error:', error);
      throw error;
    }
  }
}

export const currencyAPI = new CurrencyAPI();

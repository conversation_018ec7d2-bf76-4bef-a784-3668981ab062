import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from '../../hooks/useTranslation';
import { cn } from '../../lib/utils';
import { Button } from '../ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import { Input } from '../ui/input';
import { Label } from '../ui/label';

const LoginForm = ({ className, ...props }) => {
  const { login, loading } = useAuth();
  const { t } = useTranslation();
  const [credentials, setCredentials] = useState({
    email: '<EMAIL>',
    password: 'password123'
  });
  const [error, setError] = useState('');
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSubmitting(true);
    setError('');

    try {
      const response = await login(credentials);
      if (!response.success) {
        setError(response.message || safeT('auth.loginFailed', 'Login failed'));
      }
    } catch (err) {
      setError(safeT('auth.loginFailedCredentials', 'Login failed. Please check your credentials.'));
      console.error('Login error:', err);
    } finally {
      setSubmitting(false);
    }
  };

  const handleChange = (e) => {
    setCredentials({
      ...credentials,
      [e.target.name]: e.target.value
    });
  };

  const handleSocialLogin = (provider) => {
    console.log(`Login with ${provider}`);
    // TODO: Implement social login functionality
  };

  const handleDemoLogin = (account) => {
    setCredentials({
      email: account.email,
      password: account.password
    });
    setError('');
  };

  // Safe translation function to prevent React errors
  const safeT = (key, fallback = key) => {
    try {
      const result = t(key);
      // Ensure we always return a string
      if (typeof result === 'string' && result.trim() !== '') {
        return result;
      }
      return fallback;
    } catch (error) {
      console.warn(`Translation error for key: ${key}`, error);
      return fallback;
    }
  };

  // Demo accounts for quick access - using React.useMemo to prevent recreation
  const demoAccounts = React.useMemo(() => {
    return [
      {
        role: safeT('Supper Admin Access', 'Super Admin'),
        email: '<EMAIL>',
        password: 'password123',
        description: safeT('Super Admin Access', 'Full system access')
      },
      {
        role: safeT('Admin Access', 'Admin'),
        email: '<EMAIL>',
        password: 'password123',
        description: safeT('Admin Access', 'Administrative access')
      },
      {
        role: safeT('Manager Access', 'Manager'),
        email: '<EMAIL>',
        password: 'password123',
        description: safeT('Manager Access', 'Management access')
      },
      {
        role: safeT('Editor Access', 'Editor'),
        email: '<EMAIL>',
        password: 'password123',
        description: safeT('Editor Access', 'Editor access')
      }
    ];
  }, [t]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full">
        <div className={cn("flex flex-col gap-6", className)} {...props}>
          <Card>
            <CardHeader className="text-center">
              <CardTitle className="text-xl">{safeT('Oikkko || Real Estate Management', 'Welcome back')}</CardTitle>
              {/* <CardDescription>
                {safeT('auth.loginCredentials', 'Login with your credentials')}
              </CardDescription> */}
            </CardHeader>
            <CardContent>
              {error && (
                <div className="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md text-sm">
                  {error}
                </div>
              )}
              <form onSubmit={handleSubmit}>
                <div className="grid gap-6">
                  <div className="grid gap-6">
                    <div className="grid gap-3">
                      <Label htmlFor="email">{safeT('Email', 'Email')}</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={credentials.email}
                        onChange={handleChange}
                        required
                      />
                    </div>
                    <div className="grid gap-3">
                      <div className="flex items-center">
                        <Label htmlFor="password">{safeT('Password', 'Password')}</Label>
                        <a
                          href="#"
                          className="ml-auto text-sm underline-offset-4 hover:underline"
                        >
                          {safeT('Forget Password', 'Forgot your password?')}
                        </a>
                      </div>
                      <Input 
                        id="password" 
                        name="password"
                        type="password" 
                        value={credentials.password}
                        onChange={handleChange}
                        required 
                      />
                    </div>
                    <Button 
                      type="submit" 
                      className="w-full"
                      disabled={submitting || loading}
                    >
                      {submitting || loading ? safeT('auth.signingIn', 'Signing in...') : safeT('Login', 'Login')}
                    </Button>
                  </div>
                  
                  <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
                    <span className="bg-card text-muted-foreground relative z-10 px-2">
                      {safeT('Quick Access', 'Or use quick access')}
                    </span>
                  </div>
                  
                  {/* Demo Account Access */}
                  <div className="grid gap-3">
                    <Label className="text-sm font-medium">{safeT('Quick Access (Demo Accounts)', 'Quick Access (Demo Accounts)')}</Label>
                    <div className="grid grid-cols-2 gap-2">
                      {demoAccounts.map((account, index) => (
                        <Button
                          key={index}
                          type="button"
                          variant="outline"
                          className="w-full justify-start text-left h-auto p-3"
                          onClick={() => handleDemoLogin(account)}
                        >
                          <div className="flex flex-col items-start">
                            <span className="font-medium text-xs">{account.role}</span>
                            <span className="text-[10px] text-muted-foreground">{account.description}</span>
                            <span className="text-[10px] text-muted-foreground truncate">{account.email}</span>
                          </div>
                        </Button>
                      ))}
                    </div>
                  </div>
                  
                
                </div>
              </form>
            </CardContent>
          </Card>
      
          
         
        
        </div>
      </div>
    </div>
  );
};

export default LoginForm;

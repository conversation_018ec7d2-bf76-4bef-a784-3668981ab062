import React, { useMemo, useEffect, useRef } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import '../../../css/rich-text-editor.css';

const RichTextEditor = ({ 
  value, 
  onChange, 
  placeholder = "Enter text...", 
  disabled = false,
  height = "150px"
}) => {
  const quillRef = useRef(null);

  // Comprehensive suppression of React Quill deprecation warnings
  useEffect(() => {
    const originalConsoleError = console.error;
    const originalConsoleWarn = console.warn;
    
    console.error = (...args) => {
      const message = args[0];
      if (typeof message === 'string') {
        // Suppress findDOMNode warnings
        if (message.includes('findDOMNode') || 
            message.includes('DOMNodeInserted') ||
            message.includes('ReactQuill') ||
            message.includes('Deprecation')) {
          return;
        }
      }
      originalConsoleError.apply(console, args);
    };
    
    console.warn = (...args) => {
      const message = args[0];
      if (typeof message === 'string') {
        // Suppress findDOMNode warnings
        if (message.includes('findDOMNode') || 
            message.includes('DOMNodeInserted') ||
            message.includes('ReactQuill') ||
            message.includes('Deprecation')) {
          return;
        }
      }
      originalConsoleWarn.apply(console, args);
    };

    return () => {
      console.error = originalConsoleError;
      console.warn = originalConsoleWarn;
    };
  }, []);

  const modules = useMemo(() => ({
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'align': [] }],
      ['link'],
      ['clean']
    ],
  }), []);

  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'indent',
    'color', 'background',
    'align',
    'link'
  ];

  return (
    <div className="rich-text-editor">
      <ReactQuill
        ref={quillRef}
        theme="snow"
        value={value || ''}
        onChange={onChange}
        modules={modules}
        formats={formats}
        placeholder={placeholder}
        readOnly={disabled}
        style={{
          height: height,
          marginBottom: disabled ? '0px' : '42px' // Account for toolbar height when not disabled
        }}
        preserveWhitespace
        bounds={'.rich-text-editor'}
      />
    </div>
  );
};

export default RichTextEditor;

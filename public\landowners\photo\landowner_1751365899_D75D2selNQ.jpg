<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real Estate Management Dashboard</title>
    <script type="module" >
    import RefreshRuntime from 'http://[::1]:5173/@react-refresh'
    RefreshRuntime.injectIntoGlobalHook(window)
    window.$RefreshReg$ = () => {}
    window.$RefreshSig$ = () => (type) => type
    window.__vite_plugin_react_preamble_installed__ = true
</script>    <script type="module" src="http://[::1]:5173/@vite/client"></script><script type="module" src="http://[::1]:5173/resources/js/main.jsx"></script><link rel="stylesheet" href="http://[::1]:5173/resources/css/app.css" /></head>
<body>
    <div id="app"></div>
</body>
</html>

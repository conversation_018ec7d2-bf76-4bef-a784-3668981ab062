import * as React from "react"
import * as DialogPrimitive from "@radix-ui/react-dialog"
import { X } from "lucide-react"
import { createPortal } from "react-dom"

import { cn } from "@/lib/utils"

const ContentModal = DialogPrimitive.Root

const ContentModalTrigger = DialogPrimitive.Trigger

const ContentModalClose = DialogPrimitive.Close

// Custom portal that renders within the main content area
const ContentModalPortal = ({ children, container }) => {
  const [mounted, setMounted] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) return null

  // Find the main content container or fall back to body
  const targetContainer = container || document.querySelector('main') || document.body

  return createPortal(children, targetContainer)
}

const ContentModalOverlay = React.forwardRef(({ className, ...props }, ref) => (
  <ContentModalPortal>
    <DialogPrimitive.Overlay
      ref={ref}
      className={cn(
        "absolute inset-0 z-40 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
        className
      )}
      {...props}
    />
  </ContentModalPortal>
))
ContentModalOverlay.displayName = DialogPrimitive.Overlay.displayName

const ContentModalContent = React.forwardRef(({ className, children, ...props }, ref) => (
  <ContentModalPortal>
    <DialogPrimitive.Content
      ref={ref}
      className={cn(
        "absolute left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",
        className
      )}
      {...props}
    >
      {children}
      <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
        <X className="h-4 w-4" />
        <span className="sr-only">Close</span>
      </DialogPrimitive.Close>
    </DialogPrimitive.Content>
  </ContentModalPortal>
))
ContentModalContent.displayName = "ContentModalContent"

const ContentModalHeader = ({
  className,
  ...props
}) => (
  <div
    className={cn(
      "flex flex-col space-y-1.5 text-center sm:text-left",
      className
    )}
    {...props}
  />
)
ContentModalHeader.displayName = "ContentModalHeader"

const ContentModalFooter = ({
  className,
  ...props
}) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    )}
    {...props}
  />
)
ContentModalFooter.displayName = "ContentModalFooter"

const ContentModalTitle = React.forwardRef(({ className, ...props }, ref) => (
  <DialogPrimitive.Title
    ref={ref}
    className={cn(
      "text-lg font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
))
ContentModalTitle.displayName = "ContentModalTitle"

const ContentModalDescription = React.forwardRef(({ className, ...props }, ref) => (
  <DialogPrimitive.Description
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
))
ContentModalDescription.displayName = "ContentModalDescription"

export {
  ContentModal,
  ContentModalPortal,
  ContentModalOverlay,
  ContentModalClose,
  ContentModalTrigger,
  ContentModalContent,
  ContentModalHeader,
  ContentModalFooter,
  ContentModalTitle,
  ContentModalDescription,
}

// Centralized API configuration with automatic environment detection
import axios from 'axios';

export const getApiBaseUrl = () => {
  const currentHost = window.location.hostname;
  const currentPort = window.location.port;
  const currentProtocol = window.location.protocol;
  

  // Local Development Detection
  const isLocalDevelopment = (
    currentHost === 'localhost' || 
    currentHost === '127.0.0.1' || 
    currentHost.startsWith('192.168.') ||
    currentHost.endsWith('.local') ||
    import.meta.env.DEV
  );

  if (isLocalDevelopment) {
    
    
    // XAMPP Setup Detection
    if (window.location.pathname.includes('real-estate-management')) {
      const xamppUrl = `${currentProtocol}//${currentHost}${currentPort ? ':' + currentPort : ''}/real-estate-management/public/api`;
    
      return xamppUrl;
    }
    
    // Laravel Development Server Detection (php artisan serve)
    if (currentPort === '8000' || currentPort === '3000') {
      const devServerUrl = `${currentProtocol}//${currentHost}:${currentPort}/api`;
    
      return devServerUrl;
    }
    
    // Default local development
    const defaultLocalUrl = 'http://127.0.0.1:8000/api';
  
    return defaultLocalUrl;
  }

  // Production Environment

  const productionUrl = `${currentProtocol}//${currentHost}/api`;

  return productionUrl;
};

// Get the API base URL
export const API_BASE_URL = getApiBaseUrl();



// Function to test API connectivity
export const testApiConnectivity = async (url) => {
  try {
    const response = await fetch(`${url}/info`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
      },
    });
    
    if (response.ok) {
      const data = await response.json();
   
      return { success: true, data };
    }
    
    throw new Error(`HTTP ${response.status}`);
  } catch (error) {
  
    return { success: false, error: error.message };
  }
};

// Auto-detect and validate the best API URL
export const getValidatedApiUrl = async () => {
  const primaryUrl = getApiBaseUrl();
  
  // Test primary URL first
  const primaryTest = await testApiConnectivity(primaryUrl);
  if (primaryTest.success) {
    return primaryUrl;
  }
  
  
  
  // Fallback URLs to try
  const fallbackUrls = [
    'http://127.0.0.1:8000/api',
    'http://localhost:8000/api',
    `${window.location.protocol}//${window.location.host}/api`,
  ];
  
  // Remove duplicates and primary URL from fallbacks
  const uniqueFallbacks = [...new Set(fallbackUrls)].filter(url => url !== primaryUrl);
  
  for (const fallbackUrl of uniqueFallbacks) {
    const test = await testApiConnectivity(fallbackUrl);
    if (test.success) {
     
      return fallbackUrl;
    }
  }
  

  return primaryUrl;
};

// Environment detection utilities
export const getEnvironmentInfo = () => {
  return {
    isDevelopment: window.location.hostname === 'localhost' || 
             window.location.hostname === '127.0.0.1' ||
             window.location.hostname.startsWith('192.168.') ||
             import.meta.env.DEV,
    apiUrl: API_BASE_URL
  };
};

// Create axios instance with base configuration
const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor to include auth token
axiosInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

export default axiosInstance;

<?php

namespace Database\Seeders;

use App\Models\Employee;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class EmployeeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first user to assign as creator
        $user = User::first();
        
        $employees = [
            [
                'name' => '<PERSON>',
                'designation' => 'Senior Software Engineer',
                'phone' => '+1234567890',
                'email' => '<EMAIL>',
                'status' => 'active',
                'date_of_joining' => Carbon::now()->subMonths(6),
                'address' => '123 Tech Street, San Francisco, CA 94102',
                'salary' => 85000.00,
                'created_by' => $user?->id,
                'updated_by' => $user?->id,
            ],
            [
                'name' => '<PERSON>',
                'designation' => 'Product Manager',
                'phone' => '+1234567891',
                'email' => '<EMAIL>',
                'status' => 'active',
                'date_of_joining' => Carbon::now()->subYears(1),
                'address' => '456 Business Ave, New York, NY 10001',
                'salary' => 95000.00,
                'created_by' => $user?->id,
                'updated_by' => $user?->id,
            ],
            [
                'name' => '<PERSON>',
                'designation' => 'DevOps Engineer',
                'phone' => '+1234567892',
                'email' => '<EMAIL>',
                'status' => 'active',
                'date_of_joining' => Carbon::now()->subMonths(3),
                'address' => '789 Cloud Lane, Seattle, WA 98101',
                'salary' => 80000.00,
                'created_by' => $user?->id,
                'updated_by' => $user?->id,
            ],
            [
                'name' => 'Emily Davis',
                'designation' => 'UI/UX Designer',
                'phone' => '+1234567893',
                'email' => '<EMAIL>',
                'status' => 'active',
                'date_of_joining' => Carbon::now()->subMonths(8),
                'address' => '321 Design Blvd, Los Angeles, CA 90210',
                'salary' => 70000.00,
                'created_by' => $user?->id,
                'updated_by' => $user?->id,
            ],
            [
                'name' => 'David Wilson',
                'designation' => 'Marketing Specialist',
                'phone' => '+1234567894',
                'email' => '<EMAIL>',
                'status' => 'inactive',
                'date_of_joining' => Carbon::now()->subYears(2),
                'address' => '654 Marketing Way, Chicago, IL 60601',
                'salary' => 55000.00,
                'created_by' => $user?->id,
                'updated_by' => $user?->id,
            ],
            [
                'name' => 'Lisa Anderson',
                'designation' => 'Data Analyst',
                'phone' => '+1234567895',
                'email' => '<EMAIL>',
                'status' => 'active',
                'date_of_joining' => Carbon::now()->subMonths(4),
                'address' => '987 Analytics Dr, Austin, TX 78701',
                'salary' => 68000.00,
                'created_by' => $user?->id,
                'updated_by' => $user?->id,
            ],
            [
                'name' => 'Robert Taylor',
                'designation' => 'Quality Assurance Engineer',
                'phone' => '+1234567896',
                'email' => '<EMAIL>',
                'status' => 'active',
                'date_of_joining' => Carbon::now()->subMonths(10),
                'address' => '147 Testing St, Denver, CO 80202',
                'salary' => 65000.00,
                'created_by' => $user?->id,
                'updated_by' => $user?->id,
            ],
            [
                'name' => 'Jennifer Martinez',
                'designation' => 'HR Coordinator',
                'phone' => '+1234567897',
                'email' => '<EMAIL>',
                'status' => 'active',
                'date_of_joining' => Carbon::now()->subYears(1)->subMonths(3),
                'address' => '258 People Rd, Miami, FL 33101',
                'salary' => 50000.00,
                'created_by' => $user?->id,
                'updated_by' => $user?->id,
            ]
        ];

        foreach ($employees as $employeeData) {
            Employee::create($employeeData);
        }
    }
}

import React, { useState, useEffect } from 'react';
import vendorTypeAPI from '../../services/vendorTypeAPI';
import Swal from 'sweetalert2';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '../ui/dialog';
import StandardModal from '@/components/ui/StandardModal';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { Search, Plus, Edit2, Trash2, Package, Check, X, Filter, ChevronLeft, ChevronRight } from 'lucide-react';

const VendorTypePage = () => {
  const [vendorTypes, setVendorTypes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState({});
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingVendorType, setEditingVendorType] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    status: 'active'
  });

  // Pagination and filtering state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');

  // Fetch vendor types
  const fetchVendorTypes = async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        per_page: perPage,
        search: searchTerm,
        status: statusFilter,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      const response = await vendorTypeAPI.getVendorTypes(params);
      setVendorTypes(response.data.data.data);
      setCurrentPage(response.data.data.current_page);
      setTotalPages(response.data.data.last_page);
    } catch (error) {
      console.error('Error fetching vendor types:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to fetch vendor types',
        timer: 3000,
        showConfirmButton: false
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const response = await vendorTypeAPI.getVendorTypeStatistics();
      setStatistics(response.data.data);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  useEffect(() => {
    fetchVendorTypes(1);
    fetchStatistics();
  }, [perPage, searchTerm, statusFilter, sortBy, sortOrder]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    fetchVendorTypes(1);
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      status: 'active'
    });
  };

  // Handle add vendor type
  const handleAddVendorType = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        text: 'Name is required',
        timer: 3000,
        showConfirmButton: false
      });
      return;
    }
    
    try {
      await vendorTypeAPI.createVendorType(formData);
      setShowAddModal(false);
      resetForm();
      fetchVendorTypes();
      fetchStatistics();
      
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: 'Vendor type created successfully',
        timer: 3000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error creating vendor type:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: error.response?.data?.message || 'Failed to create vendor type',
        timer: 3000,
        showConfirmButton: false
      });
    }
  };

  // Handle edit vendor type
  const handleEditVendorType = (vendorType) => {
    setEditingVendorType(vendorType);
    setFormData({
      name: vendorType.name || '',
      status: vendorType.status || 'active'
    });
    setShowEditModal(true);
  };

  // Handle update vendor type
  const handleUpdateVendorType = async (e) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        text: 'Name is required',
        timer: 3000,
        showConfirmButton: false
      });
      return;
    }
    
    try {
      await vendorTypeAPI.updateVendorType(editingVendorType.id, formData);
      setShowEditModal(false);
      setEditingVendorType(null);
      resetForm();
      fetchVendorTypes();
      fetchStatistics();
      
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: 'Vendor type updated successfully',
        timer: 3000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error updating vendor type:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: error.response?.data?.message || 'Failed to update vendor type',
        timer: 3000,
        showConfirmButton: false
      });
    }
  };

  // Handle delete vendor type
  const handleDeleteVendorType = async (vendorType) => {
    const result = await Swal.fire({
      title: 'Delete Vendor Type',
      text: `Are you sure you want to delete "${vendorType.name}"? This action cannot be undone.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel'
    });

    if (result.isConfirmed) {
      try {
        await vendorTypeAPI.deleteVendorType(vendorType.id);
        fetchVendorTypes();
        fetchStatistics();
        
        Swal.fire({
          icon: 'success',
          title: 'Deleted!',
          text: 'Vendor type deleted successfully',
          timer: 3000,
          showConfirmButton: false
        });
      } catch (error) {
        console.error('Error deleting vendor type:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to delete vendor type',
          timer: 3000,
          showConfirmButton: false
        });
      }
    }
  };

  // Handle status toggle
  const handleStatusToggle = async (vendorType) => {
    const newStatus = vendorType.status === 'active' ? 'inactive' : 'active';
    
    try {
      await vendorTypeAPI.updateVendorType(vendorType.id, {
        name: vendorType.name,
        status: newStatus
      });
      fetchVendorTypes();
      fetchStatistics();
      
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: `Vendor type ${newStatus === 'active' ? 'activated' : 'deactivated'} successfully`,
        timer: 3000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error toggling status:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to update vendor type status',
        timer: 3000,
        showConfirmButton: false
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center">
            <Package className="w-8 h-8 mr-3 text-blue-600" />
            Vendor Type Management
          </h1>
          <p className="text-gray-600 mt-1">Manage vendor types and categories</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700" onClick={() => setShowAddModal(true)}>
          <Plus className="w-4 h-4 mr-2" />
          Add Vendor Type
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-800">Total Vendor Types</CardTitle>
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <Package className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900">{statistics.total_vendor_types || 0}</div>
            <p className="text-xs text-blue-600 mt-1">All vendor type categories</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-800">Active Types</CardTitle>
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
              <Check className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">{statistics.active_vendor_types || 0}</div>
            <p className="text-xs text-green-600 mt-1">Currently available</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-800">Inactive Types</CardTitle>
            <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
              <X className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-900">{statistics.inactive_vendor_types || 0}</div>
            <p className="text-xs text-red-600 mt-1">Not currently active</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="border-gray-200 shadow-sm">
        <CardHeader className="bg-gray-50 border-b">
          <CardTitle className="flex items-center text-lg">
            <Filter className="w-5 h-5 mr-2 text-blue-600" />
            Search & Filter Vendor Types
          </CardTitle>
          <CardDescription>
            Find vendor types by name or status
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <Label htmlFor="search" className="text-sm font-medium text-gray-700">Search</Label>
                <div className="relative mt-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="search"
                    type="text"
                    placeholder="Search vendor types..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="status_filter" className="text-sm font-medium text-gray-700">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="sort_by" className="text-sm font-medium text-gray-700">Sort By</Label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name</SelectItem>
                    <SelectItem value="status">Status</SelectItem>
                    <SelectItem value="created_at">Date Created</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="sort_order" className="text-sm font-medium text-gray-700">Order</Label>
                <Select value={sortOrder} onValueChange={setSortOrder}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="asc">Ascending</SelectItem>
                    <SelectItem value="desc">Descending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                <Search className="w-4 h-4 mr-2" />
                Apply Filters
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  setSortBy('name');
                  setSortOrder('asc');
                }}
              >
                Clear Filters
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Vendor Types Table */}
      <Card>
        <CardHeader>
          <CardTitle>Vendor Types</CardTitle>
          <CardDescription>Manage your vendor type categories</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full table-auto">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Name</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Created By</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {vendorTypes.map((vendorType) => (
                      <tr key={vendorType.id} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <div className="flex-shrink-0">
                              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <Package className="w-5 h-5 text-blue-600" />
                              </div>
                            </div>
                            <div className="ml-3">
                              <div className="font-medium text-gray-900">{vendorType.name}</div>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <Badge 
                            variant={vendorType.status === 'active' ? 'default' : 'secondary'}
                            className={vendorType.status === 'active' 
                              ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                              : 'bg-red-100 text-red-800 hover:bg-red-200'
                            }
                          >
                            {vendorType.status === 'active' ? (
                              <Check className="w-3 h-3 mr-1" />
                            ) : (
                              <X className="w-3 h-3 mr-1" />
                            )}
                            {vendorType.status}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">
                          <div className="text-sm text-gray-700">
                            {vendorType.creator?.name || 'N/A'}
                          </div>
                          <div className="text-xs text-gray-500">
                            {vendorType.created_at ? new Date(vendorType.created_at).toLocaleDateString() : ''}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditVendorType(vendorType)}
                              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            >
                              <Edit2 className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleStatusToggle(vendorType)}
                              className={vendorType.status === 'active' 
                                ? 'text-orange-600 hover:text-orange-700 hover:bg-orange-50' 
                                : 'text-green-600 hover:text-green-700 hover:bg-green-50'
                              }
                            >
                              {vendorType.status === 'active' ? 'Deactivate' : 'Activate'}
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteVendorType(vendorType)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-gray-700">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchVendorTypes(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="w-4 h-4" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchVendorTypes(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Vendor Type Modal */}
      <StandardModal 
        showModal={showAddModal} 
        closeModal={() => setShowAddModal(false)}
        modalMode="create"
        title="Add New Vendor Type"
        icon={Plus}
        maxWidth="max-w-md"
      >
        <div className="text-center pb-4 border-b border-gray-200 dark:border-gray-700">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
            <Package className="w-8 h-8 text-white" />
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Create a new vendor type category.
          </p>
        </div>

        <form onSubmit={handleAddVendorType} className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                placeholder="Enter vendor type name"
                required
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
              />
            </div>
            
            <div>
              <Label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</Label>
              <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                <SelectTrigger className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => setShowAddModal(false)}
              className="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-xl font-medium transition-all duration-200 shadow-sm hover:shadow-md"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              <Plus className="mr-2 h-4 w-4 inline" />
              Add Vendor Type
            </button>
          </div>
        </form>
      </StandardModal>

      {/* Edit Vendor Type Modal */}
      <StandardModal 
        showModal={showEditModal} 
        closeModal={() => setShowEditModal(false)}
        modalMode="edit"
        title="Edit Vendor Type"
        icon={Edit2}
        maxWidth="max-w-md"
      >
        <div className="text-center pb-4 border-b border-gray-200 dark:border-gray-700">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
            <Package className="w-8 h-8 text-white" />
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Update vendor type information.
          </p>
        </div>

        <form onSubmit={handleUpdateVendorType} className="space-y-6">
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit_name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Name *</Label>
              <Input
                id="edit_name"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                placeholder="Enter vendor type name"
                required
                className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
              />
            </div>
            
            <div>
              <Label htmlFor="edit_status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</Label>
              <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                <SelectTrigger className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => setShowEditModal(false)}
              className="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-xl font-medium transition-all duration-200 shadow-sm hover:shadow-md"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              <Edit2 className="mr-2 h-4 w-4 inline" />
              Update Vendor Type
            </button>
          </div>
        </form>
      </StandardModal>
    </div>
  );
};

export default VendorTypePage;

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckPermission
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, string $module, string $permission = 'read'): Response
    {
        $user = $request->user();

        // Check if user is authenticated
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized'
            ], 401);
        }

        // Ensure role relationship is loaded
        if (!$user->relationLoaded('role')) {
            $user->load('role');
        }

        // Check if user has access to the module
        if (!$user->hasModuleAccess($module)) {
            return response()->json([
                'success' => false,
                'message' => 'You do not have access to this module'
            ], 403);
        }

        // Check if user has the required permission
        if (!$user->hasPermission($module, $permission)) {
            return response()->json([
                'success' => false,
                'message' => "You do not have permission to {$permission} in {$module} module"
            ], 403);
        }

        return $next($request);
    }
}

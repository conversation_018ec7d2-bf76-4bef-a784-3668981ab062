import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import Sidebar from './Sidebar';
import Header from './Header';
import ProtectedRoute from '../auth/ProtectedRoute';
import ModuleExtensionsSidebar from '../ui/ModuleExtensionsSidebar';

const DashboardLayout = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [modulesSidebarOpen, setModulesSidebarOpen] = useState(false);
  const location = useLocation();
  
  // Get current page from URL path
  const currentPage = location.pathname.replace('/', '') || 'dashboard';

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-background flex">
        {/* Mobile sidebar overlay */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Sidebar */}
        <div className={cn(
          "fixed inset-y-0 left-0 z-50 w-64 transform bg-card border-r transition-transform duration-300 ease-in-out lg:relative lg:translate-x-0",
          sidebarOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
        )}>
          <Sidebar
            onClose={() => setSidebarOpen(false)}
          />
        </div>

        {/* Main content */}
        <div className="flex-1 flex flex-col min-w-0">
          {/* Header */}
          <Header 
            onMenuClick={() => setSidebarOpen(true)}
            onModulesClick={() => setModulesSidebarOpen(true)}
          />

          {/* Page content */}
          <main className="flex-1 p-6 overflow-auto bg-background relative">
            <div className="w-full h-full">
              {children}
            </div>
          </main>
        </div>

        {/* Module Extensions Sidebar */}
        <ModuleExtensionsSidebar 
          isOpen={modulesSidebarOpen}
          onClose={() => setModulesSidebarOpen(false)}
        />
      </div>
    </ProtectedRoute>
  );
};

export default DashboardLayout;

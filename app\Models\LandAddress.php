<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LandAddress extends Model
{
    protected $fillable = [
        'land_acquisition_id',
        'country_id',
        'state_id', 
        'city_text', // Updated to match migration
        'specific_address',
        // Keep legacy fields for backward compatibility
        'plot_no',
        'road',
        'area',
        'upazila',
        'thana',
        'city',
        'district',
        'country',
        'zip_code'
    ];

    /**
     * Get the land acquisition that owns this address
     */
    public function landAcquisition(): BelongsTo
    {
        return $this->belongsTo(LandAcquisition::class);
    }

    /**
     * Get the country for this address
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Get the state for this address
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }
    
    /**
     * City accessor - returns city_text or falls back to legacy city field
     */
    public function getCityAttribute()
    {
        return $this->city_text ?? $this->attributes['city'] ?? null;
    }
}

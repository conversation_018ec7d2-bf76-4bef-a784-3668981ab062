<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            // Drop the old country column index first
            $table->dropIndex(['city', 'state', 'country']);
            
            // Change country column to be a foreign key
            $table->unsignedBigInteger('country_id')->nullable()->after('state');
            
            // Add foreign key constraint
            $table->foreign('country_id')->references('id')->on('countries')->onDelete('set null');
            
            // Add new index
            $table->index(['city', 'state', 'country_id']);
            
            // Drop the old country column
            $table->dropColumn('country');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            // Drop the new foreign key and index
            $table->dropForeign(['country_id']);
            $table->dropIndex(['city', 'state', 'country_id']);
            $table->dropColumn('country_id');
            
            // Restore the old country column
            $table->string('country', 100)->after('state');
            
            // Restore the old index
            $table->index(['city', 'state', 'country']);
        });
    }
};

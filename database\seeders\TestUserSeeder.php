<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

class TestUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the Super Admin role (assuming it exists)
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        $adminRole = Role::where('name', 'Admin')->first();

        if (!$superAdminRole) {
            $this->command->error('Super Admin role not found. Please run role seeder first.');
            return;
        }

        // Create test users if they don't exist
        if (!User::where('email', '<EMAIL>')->exists()) {
            User::create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role_id' => $adminRole ? $adminRole->id : $superAdminRole->id,
                'email_verified_at' => now(),
            ]);
            $this->command->info('Created test user: <EMAIL> / password');
        }

        if (!User::where('email', '<EMAIL>')->exists()) {
            User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('admin123'),
                'role_id' => $superAdminRole->id,
                'email_verified_at' => now(),
            ]);
            $this->command->info('Created admin user: <EMAIL> / admin123');
        }

        $this->command->info('Test users created successfully!');
    }
}

import axios from 'axios';
import { API_BASE_URL } from '../config/api.js';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor for authentication if needed
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      // Redirect to login if needed
    }
    return Promise.reject(error);
  }
);

// Role API functions

// Get all roles with search and pagination
export const getRoles = async (params = {}) => {
  const response = await api.get('/roles', { params });
  return response.data;
};

// Get role statistics
export const getStatistics = async () => {
  const response = await api.get('/roles-statistics');
  return response.data;
};

// Get available modules and permissions
export const getAvailableModules = async () => {
  const response = await api.get('/roles-modules');
  return response.data;
};

// Create a new role
export const createRole = async (roleData) => {
  const response = await api.post('/roles', roleData);
  return response.data;
};

// Get a specific role
export const getRole = async (id) => {
  const response = await api.get(`/roles/${id}`);
  return response.data;
};

// Update a role
export const updateRole = async (id, roleData) => {
  const response = await api.put(`/roles/${id}`, roleData);
  return response.data;
};

// Delete a role
export const deleteRole = async (id) => {
  const response = await api.delete(`/roles/${id}`);
  return response.data;
};

// Bulk update role status
export const bulkUpdateStatus = async (roleIds, status) => {
  const response = await api.patch('/roles-bulk-status', {
    role_ids: roleIds,
    status: status,
  });
  return response.data;
};



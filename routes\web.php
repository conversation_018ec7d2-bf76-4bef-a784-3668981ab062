<?php

use Illuminate\Support\Facades\Route;

// Login route for authentication redirects
Route::get('/login', function () {
    return response()->json([
        'message' => 'Please use the API endpoint /api/auth/login for authentication',
        'login_endpoint' => '/api/auth/login'
    ], 401);
})->name('login');


// Catch-all route for React Router - this should handle all dashboard routes
Route::get('/{any}', function () {
    return view('react');
})->where('any', '^(?!api).*$'); // Exclude API routes
import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from '../TranslationProvider';
import { showAlertMethods as showAlert } from '@/utils/sweetAlert';
import LanguageSwitcher from '@/components/ui/LanguageSwitcher';
import {
  Home,
  BarChart3,
  Users,
  Settings,
  FileText,
  X,
  Shield,
  ShoppingCart,
  UserCheck,
  Layers,
  Bot,
  RotateCcw,
  Building,
  Building2,
  Map,
  MapPin,
  DollarSign,
  LogOut,
  IdCard,
  HardHat,
  Package,
  Truck,
  Puzzle,
  ChevronDown,
  ChevronRight,
  UserCheck as UserCheckIcon   
} from 'lucide-react';

const Sidebar = ({ onClose }) => {
  const { canAccessModule, hasModuleAccess, loading, user, role, logout, moduleDetails, refreshUserData } = useAuth();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  
  // Module management state
  const [showModules, setShowModules] = useState(false);
  const [moduleStates, setModuleStates] = useState({});
  const [availableModules, setAvailableModules] = useState([]);
  const [loadingModules, setLoadingModules] = useState(false);
  
  // Get current page from URL path
  const currentPage = location.pathname.replace('/', '') || 'dashboard';



  // Load available modules from the Modules folder
  const loadAvailableModules = async () => {
    setLoadingModules(true);
    try {
      // Try to fetch from API first, fallback to predefined list
      const response = await fetch('/api/modules/available');
      if (response.ok) {
        const data = await response.json();
        if (data.success && data.data.modules) {
          const moduleList = data.data.modules.map(module => ({
            name: module.name,
            key: module.key,
            icon: getModuleIcon(module.key),
            defaultActive: module.available || false,
            isLaravelModule: module.is_laravel_module || false,
            controllerExists: module.controller_exists || false,
            modelExists: module.model_exists || false
          }));
          setAvailableModules(moduleList);
          return;
        }
      }
    } catch (error) {
      // API not available, using fallback modules
    }
    
    // Fallback to predefined modules
    const fallbackModules = [
      { name: 'Land Owners', key: 'landowners', icon: Users, defaultActive: true, isLaravelModule: true },
      { name: 'Projects', key: 'projects', icon: Building2, defaultActive: true, isLaravelModule: false },
      { name: 'Employees', key: 'employees', icon: IdCard, defaultActive: true, isLaravelModule: false },
      { name: 'Contractors', key: 'contractors', icon: HardHat, defaultActive: true, isLaravelModule: false },
      { name: 'Vendors', key: 'vendors', icon: Truck, defaultActive: true, isLaravelModule: false },
      { name: 'Vendor Types', key: 'vendor-types', icon: Package, defaultActive: true, isLaravelModule: false },
      { name: 'Reports', key: 'reports', icon: FileText, defaultActive: true, isLaravelModule: false },
      { name: 'Analytics', key: 'analytics', icon: BarChart3, defaultActive: false, isLaravelModule: false },
    ];
    setAvailableModules(fallbackModules);
    setLoadingModules(false);
  };

  // Get icon for module based on key
  const getModuleIcon = (moduleKey) => {
    const iconMap = {
      'land-owners': Users,
      'landowners': Users,
      'projects': Building2,
      'employees': IdCard,
      'contractors': HardHat,
      'vendors': Truck,
      'vendor-types': Package,
      'vendortypes': Package,
      'property-amenity': Package,
      'property-amenities': Package,
      'property-type': Building,
      'property-types': Building,
      'property-status': Settings,
      'property-statuses': Settings,
      'reports': FileText,
      'analytics': BarChart3,
      'settings': Settings,
      'roles': Shield,
      'users': UserCheck,
      'dashboard': Home,
      'orders': ShoppingCart,
      'components': Layers
    };
    return iconMap[moduleKey.toLowerCase()] || Package; // Default to Package icon
  };

  // Initialize module states and load available modules
  useEffect(() => {
    loadAvailableModules();
  }, []);

  // Update module states when available modules change
  useEffect(() => {
    if (availableModules.length > 0) {
      const initialStates = {};
      availableModules.forEach(module => {
        // Load from localStorage or use default
        const savedState = localStorage.getItem(`module_${module.key}_active`);
        initialStates[module.key] = savedState !== null ? savedState === 'true' : module.defaultActive;
      });
      setModuleStates(initialStates);
    }
  }, [availableModules]);

  // Toggle module active state
  const toggleModule = async (moduleKey, currentState) => {
    const newState = !currentState;
    
    try {
      // Update state
      setModuleStates(prev => ({
        ...prev,
        [moduleKey]: newState
      }));
      
      // Save to localStorage
      localStorage.setItem(`module_${moduleKey}_active`, newState.toString());
      
      // Show success message
      const moduleName = availableModules.find(m => m.key === moduleKey)?.name || moduleKey;
      showAlert.success(
        'Module Updated',
        `${moduleName} has been ${newState ? 'activated' : 'deactivated'}`,
        2000
      );
      
    } catch (error) {
      showAlert.error('Error', 'Failed to update module status');
      
      // Revert state on error
      setModuleStates(prev => ({
        ...prev,
        [moduleKey]: currentState
      }));
    }
  };

  // Handle navigation with confirmation for sensitive pages
  const handleNavigation = async (item, event) => {
    // Pages that require confirmation
    const sensitivePages = ['role', 'settings'];
    
    if (sensitivePages.includes(item.page)) {
      event.preventDefault();
      
      try {
        const result = await showAlert.confirm(
          'Access Administrative Area',
          `You are about to access ${t(item.nameKey)}. This area contains sensitive system settings. Are you sure you want to continue?`,
          'Yes, continue',
          'Cancel'
        );

        if (result.isConfirmed) {
          navigate(`/${item.page}`);
          onClose && onClose();
        }
      } catch (error) {
        // Fallback to basic confirm
        if (window.confirm(`Access ${item.page}? This area contains sensitive system settings.`)) {
          navigate(`/${item.page}`);
          onClose && onClose();
        }
      }
    } else {
      // Normal navigation, just close sidebar if needed
      onClose && onClose();
    }
  };

  // Handle logout with confirmation
  const handleLogout = async () => {
    const result = await showAlert.confirm(
      'Sign Out',
      'Are you sure you want to sign out of your account?',
      'Yes, sign out',
      'Cancel'
    );

    if (result.isConfirmed) {
      showAlert.loading('Signing out...', 'Please wait while we sign you out');
      
      try {
        await logout();
        showAlert.close();
        showAlert.success('Signed Out', 'You have been successfully signed out', 1500);
      } catch (error) {
        showAlert.close();
        showAlert.error('Sign Out Failed', 'There was an error signing you out. Please try again.');
      }
    }
  };

  // All available navigation items with their module keys and translation keys
  // Only include items that have corresponding controllers and models
  const allNavigationItems = [
    { nameKey: 'common.navigation.dashboard', page: 'dashboard', icon: Home, moduleKey: 'dashboard' },
     { nameKey: 'common.navigation.customers', page: 'customer', icon: Users, moduleKey: 'customer' },
    { nameKey: 'common.navigation.landOwners', page: 'land-owners', icon: Users, moduleKey: 'landowners' },
    { nameKey: 'common.navigation.landAcquisition', page: 'land-acquisition', icon: Building, moduleKey: 'land-acquisition' },
    { nameKey: 'common.navigation.projects', page: 'projects', icon: Building2, moduleKey: 'project' },
    { nameKey: 'common.navigation.employees', page: 'employees', icon: IdCard, moduleKey: 'employees' },
    { nameKey: 'common.navigation.contractors', page: 'contractors', icon: HardHat, moduleKey: 'contractors' },
    { nameKey: 'common.navigation.vendorTypes', page: 'vendor-types', icon: Package, moduleKey: 'vendor-type' },
    { nameKey: 'common.navigation.vendors', page: 'vendors', icon: Truck, moduleKey: 'vendor' },
    { nameKey: 'common.navigation.assignVendor', page: 'assign-vendor', icon: UserCheckIcon, moduleKey: 'assign-vendor' },
    { nameKey: 'common.navigation.assignContractor', page: 'assign-contractor', icon: UserCheckIcon, moduleKey: 'assign-contractor' },
    { nameKey: 'common.navigation.assignEmployee', page: 'assign-employee', icon: UserCheckIcon, moduleKey: 'assign-employee' },
    { nameKey: 'common.navigation.propertyAmenities', page: 'property-amenities', icon: Package, moduleKey: 'property-amenity' },
    { nameKey: 'common.navigation.propertyTypes', page: 'property-type', icon: Building, moduleKey: 'property-type' },
    { nameKey: 'common.navigation.propertyStatus', page: 'property-status', icon: Building, moduleKey: 'property-status' }, 
    { nameKey: 'common.navigation.propertyStage', page: 'property-stage', icon: Building, moduleKey: 'property-stage' },
    { nameKey: 'common.navigation.roleManagement', page: 'role', icon: Shield, moduleKey: 'role' },
    { nameKey: 'common.navigation.states', page: 'states', icon: Map, moduleKey: 'state' },
    { nameKey: 'common.navigation.cities', page: 'cities', icon: MapPin, moduleKey: 'city' },
    { nameKey: 'common.navigation.currencies', page: 'currencies', icon: DollarSign, moduleKey: 'currency' },
    { nameKey: 'common.navigation.settings', page: 'settings', icon: Settings, moduleKey: 'settings' },
  ];

  if (loading) {
    return (
      <div className="flex h-full flex-col bg-background border-r">
        <div className="flex h-16 items-center px-6 border-b">
          <div className="animate-pulse bg-gray-200 h-6 w-32 rounded"></div>
        </div>
        <div className="flex-1 p-4 space-y-2">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-gray-200 h-10 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  // Filter navigation items based on user's accessible modules AND module availability
  const accessibleNavItems = allNavigationItems.filter(item => 
    canAccessModule(item.moduleKey) // Re-enabled proper module access checking
  );

  // Group items into main and secondary navigation
  const mainNavigation = accessibleNavItems.filter(item => 
    ['dashboard'].includes(item.moduleKey)
  );

   
  React.useEffect(() => {
   
    allNavigationItems.forEach(item => {
      const hasAccess = canAccessModule(item.moduleKey);
     
    });
  
    
  }, [allNavigationItems, accessibleNavItems, mainNavigation, loading, user, role]);

  const documentsNavigation = accessibleNavItems.filter(item => 
    ['role', 'settings', 'employees', 'customer'].includes(item.moduleKey)
  );

  const landDevelopmentNavigation = accessibleNavItems.filter(item => 
    ['landowners', 'land-acquisition', 'contractors', 'vendor-type', 'vendor', 'assign-vendor', 'assign-contractor', 'assign-employee', 'state', 'city'].includes(item.moduleKey)
  );

  const propertyManagementNavigation = accessibleNavItems.filter(item => 
    ['project', 'property-amenity', 'property-type' ,'property-status' ].includes(item.moduleKey)
  );

  return (
    <div className="flex h-full flex-col bg-background border-r">
      {/* Company Logo */}
      <div className="flex h-16 items-center px-6 border-b">
        <div className="flex items-center">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-foreground">
            <span className="text-xs font-bold text-background">O</span>
          </div>
          <span className="ml-2 text-sm font-semibold">Oikko||Real Estate Management</span>
        </div>
        
        {/* Close button for mobile */}
        <Button
          variant="ghost"
          size="icon"
          className="ml-auto lg:hidden"
          onClick={onClose}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* User Info */}
      <div className="px-6 py-4 border-b bg-muted/50">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
            <span className="text-xs font-medium text-white">
              {user?.name?.charAt(0) || 'U'}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{user?.name || 'User'}</p>
            <p className="text-xs text-muted-foreground truncate">{role?.name || 'No Role'}</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-1">
        {/* Main Navigation */}
        {mainNavigation.map((item) => {
          const Icon = item.icon;
          const isActive = currentPage === item.page;
          return (
            <Link
              key={item.nameKey}
              to={`/${item.page}`}
              onClick={(event) => handleNavigation(item, event)}
              className={cn(
                "w-full flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors text-left",
                isActive
                  ? "bg-accent text-accent-foreground"
                  : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
              )}
            >
              <Icon className="h-4 w-4 flex-shrink-0" />
              {item.nameKey.startsWith('common.navigation.') ? t(item.nameKey) : item.nameKey}
              {item.moduleKey === 'landowners' && (
                <span className="text-xs bg-green-500 text-white px-1 rounded ml-auto">●</span>
              )}
            </Link>
          );
        })}

        {/* Land & Development Section */}
        {landDevelopmentNavigation.length > 0 && (
          <div className="pt-6">
            <div className="px-3 mb-2">
              <h3 className="text-xs font-medium text-muted-foreground">
                {t('common.navigation.landDevelopment')}
              </h3>
            </div>
            {landDevelopmentNavigation.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.page;
              return (
                <Link
                  key={item.nameKey}
                  to={`/${item.page}`}
                  onClick={(event) => handleNavigation(item, event)}
                  className={cn(
                    "w-full flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors text-left",
                    isActive
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  <Icon className="h-4 w-4 flex-shrink-0" />
                  {t(item.nameKey)}
                  {item.moduleKey === 'landowners' && (
                    <span className="text-xs bg-green-500 text-white px-1 rounded ml-auto">●</span>
                  )}
                </Link>
              );
            })}
          </div>
        )}

        {/* Property Management Section */}
        {propertyManagementNavigation.length > 0 && (
          <div className="pt-6">
            <div className="px-3 mb-2">
              <h3 className="text-xs font-medium text-muted-foreground">
                Property Management
              </h3>
            </div>
            {propertyManagementNavigation.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.page;
              return (
                <Link
                  key={item.nameKey}
                  to={`/${item.page}`}
                  onClick={(event) => handleNavigation(item, event)}
                  className={cn(
                    "w-full flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors text-left",
                    isActive
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  <Icon className="h-4 w-4 flex-shrink-0" />
                  {t(item.nameKey)}
                </Link>
              );
            })}
          </div>
        )}

        {/* Documents & Admin Section */}
        {documentsNavigation.length > 0 && (
          <div className="pt-6">
            <div className="px-3 mb-2">
              <h3 className="text-xs font-medium text-muted-foreground">
                {t('common.navigation.documentsAdmin')}
              </h3>
            </div>
            {documentsNavigation.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.page;
              return (
                <Link
                  key={item.nameKey}
                  to={`/${item.page}`}
                  onClick={(event) => handleNavigation(item, event)}
                  className={cn(
                    "w-full flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors text-left",
                    isActive
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  <Icon className="h-4 w-4 flex-shrink-0" />
                  {t(item.nameKey)}
                </Link>
              );
            })}
          </div>
        )}

      
      </nav>
      
      {/* Language Switcher and Logout at bottom */}
      <div className="px-4 py-4 border-t space-y-3">
        <LanguageSwitcher variant="dropdown" size="sm" />
        
        {/* Refresh User Data Button (temporary for debugging) */}
        <Button
          variant="outline"
          size="sm"
          onClick={async () => {
            try {
              await refreshUserData();
              showAlert.success('Success', 'User permissions refreshed successfully');
            } catch (error) {
              showAlert.error('Error', 'Failed to refresh user permissions');
            }
          }}
          className="w-full justify-start text-blue-600 hover:text-blue-700 hover:bg-blue-50 border-blue-200 hover:border-blue-300"
        >
          <RotateCcw className="h-4 w-4 mr-2" />
          Refresh Permissions
        </Button>
        
        {/* Logout Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleLogout}
          className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300"
        >
          <LogOut className="h-4 w-4 mr-2" />
          {t('Logout', 'Sign Out')}
        </Button>
      </div>
    </div>
  );
};

export default Sidebar;

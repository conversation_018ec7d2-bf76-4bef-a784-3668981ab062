<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            // Drop the old index first
            $table->dropIndex(['city', 'state', 'country_id']);
            
            // Change state column to be a foreign key
            $table->unsignedBigInteger('state_id')->nullable()->after('city');
            
            // Add foreign key constraint
            $table->foreign('state_id')->references('id')->on('states')->onDelete('set null');
            
            // Add new index
            $table->index(['city', 'state_id', 'country_id']);
            
            // Drop the old state column
            $table->dropColumn('state');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            // Drop the new foreign key and index
            $table->dropForeign(['state_id']);
            $table->dropIndex(['city', 'state_id', 'country_id']);
            $table->dropColumn('state_id');
            
            // Restore the old state column
            $table->string('state', 100)->after('city');
            
            // Restore the old index
            $table->index(['city', 'state', 'country_id']);
        });
    }
};

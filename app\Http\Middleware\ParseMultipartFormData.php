<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ParseMultipartFormData
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Only process PUT, PATCH requests with multipart content
        if (in_array($request->getMethod(), ['PUT', 'PATCH']) && 
            str_contains($request->header('Content-Type', ''), 'multipart/form-data')) {
            
            $this->parseMultipartFormData($request);
        }

        return $next($request);
    }

    /**
     * Parse multipart form data for PUT/PATCH requests
     */
    private function parseMultipartFormData(Request $request)
    {
        $content = $request->getContent();
        $boundary = $this->getBoundary($request->header('Content-Type'));
        
        if (!$boundary) {
            return;
        }

        $fields = [];
        $files = [];
        
        // Split content by boundary
        $parts = explode('--' . $boundary, $content);
        
        foreach ($parts as $part) {
            if (empty(trim($part)) || $part === '--') {
                continue;
            }
            
            $this->parsePart($part, $fields, $files);
        }
        
        // Merge parsed data into the request
        $request->merge($fields);
        
        // Add files to request if any
        if (!empty($files)) {
            foreach ($files as $key => $file) {
                $request->files->set($key, $file);
            }
        }
    }

    /**
     * Extract boundary from content type header
     */
    private function getBoundary($contentType)
    {
        if (preg_match('/boundary=([^;]+)/', $contentType, $matches)) {
            return trim($matches[1], '"');
        }
        return null;
    }

    /**
     * Parse individual part of multipart data
     */
    private function parsePart($part, &$fields, &$files)
    {
        // Split headers and content
        $sections = explode("\r\n\r\n", $part, 2);
        if (count($sections) !== 2) {
            return;
        }
        
        $headers = $sections[0];
        $content = rtrim($sections[1], "\r\n");
        
        // Parse Content-Disposition header
        if (preg_match('/name="([^"]+)"/', $headers, $nameMatches)) {
            $name = $nameMatches[1];
            
            // Check if it's a file upload
            if (preg_match('/filename="([^"]*)"/', $headers, $filenameMatches)) {
                // Handle file upload
                $filename = $filenameMatches[1];
                
                // Create temporary file
                $tempFile = tempnam(sys_get_temp_dir(), 'laravel_upload');
                file_put_contents($tempFile, $content);
                
                $files[$name] = new \Illuminate\Http\UploadedFile(
                    $tempFile,
                    $filename,
                    $this->getMimeType($headers),
                    null,
                    true
                );
            } else {
                // Handle regular field
                $fields[$name] = $content;
            }
        }
    }

    /**
     * Extract MIME type from headers
     */
    private function getMimeType($headers)
    {
        if (preg_match('/Content-Type:\s*([^\r\n]+)/', $headers, $matches)) {
            return trim($matches[1]);
        }
        return 'application/octet-stream';
    }
}

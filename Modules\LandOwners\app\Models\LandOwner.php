<?php

namespace Modules\LandOwners\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\AuditableTrait;
use App\Models\LandAcquisition;
use Modules\LandOwners\Models\LandOwnerAudit;
use App\Models\User;

class LandOwner extends Model
{
    use HasFactory, AuditableTrait;

    protected $fillable = [
        'first_name',
        'last_name',
        'father_name',
        'mother_name',
        'address',
        'phone',
        'nid_number',
        'email',
        'photo',
        'document_type',
        'nid_front',
        'nid_back',
        'passport_photo',
        'created_by',
        'updated_by',
        'status',
    ];

    /**
     * Relationship with LandAcquisition
     */
    public function landAcquisitions()
    {
        return $this->hasMany(LandAcquisition::class, 'landOwners_id');
    }

    /**
     * Relationship with User who created this land owner
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Relationship with User who last updated this land owner
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Relationship with audit records
     */
    public function audits()
    {
        return $this->hasMany(LandOwnerAudit::class)->orderBy('created_at', 'desc');
    }

    /**
     * Get full name with father's name
     */
    public function getFullNameAttribute(): string
    {
        $fullName = trim($this->first_name . ' ' . $this->last_name);
        return $this->father_name ? "{$fullName} S/O {$this->father_name}" : $fullName;
    }

    /**
     * Get display name (first name + last name)
     */
    public function getNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get the latest audit record
     */
    public function getLatestAuditAttribute()
    {
        return $this->audits()->first();
    }

    /**
     * Get creation info with user details
     */
    public function getCreationInfoAttribute(): array
    {
        return [
            'created_at' => $this->created_at,
            'created_by' => $this->creator ? [
                'id' => $this->creator->id,
                'name' => $this->creator->name,
                'email' => $this->creator->email,
            ] : null,
        ];
    }

    /**
     * Get last update info with user details
     */
    public function getUpdateInfoAttribute(): array
    {
        return [
            'updated_at' => $this->updated_at,
            'updated_by' => $this->updater ? [
                'id' => $this->updater->id,
                'name' => $this->updater->name,
                'email' => $this->updater->email,
            ] : null,
        ];
    }

    /**
     * Create an audit record for this land owner
     */
    public function createAudit(string $action, array $oldValues = null, array $newValues = null, $userId = null, string $notes = null): LandOwnerAudit
    {
        $changedFields = [];
        
        if ($oldValues && $newValues) {
            foreach ($newValues as $key => $newValue) {
                $oldValue = $oldValues[$key] ?? null;
                if ($oldValue != $newValue) {
                    $changedFields[] = $key;
                }
            }
        } elseif ($action === 'created' && $newValues) {
            // For creation, all fields are "changed"
            $changedFields = array_keys($newValues);
        } elseif ($action === 'deleted' && $oldValues) {
            // For deletion, all fields are "changed"
            $changedFields = array_keys($oldValues);
        }

        // Use provided userId or try to get from auth, or use null
        $auditUserId = $userId ?? (auth()->check() ? auth()->id() : null);
        
        // Get user details if available
        $user = $auditUserId ? User::find($auditUserId) : null;

        // Prepare audit data
        $auditData = [
            'land_owner_id' => $this->id,
            'user_id' => $auditUserId,
            'action' => $action,
            'event_type' => 'automatic', // Automatic from model events
            'user_name' => $user ? $user->name : null,
            'user_email' => $user ? $user->email : null,
            'old_values' => $oldValues ? json_encode($oldValues) : null,
            'new_values' => $newValues ? json_encode($newValues) : null,
            'changed_fields' => json_encode($changedFields),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'source' => 'api', // Since this is coming from API calls
            'description' => $notes ?: $this->generateAuditDescription($action),
            'metadata' => json_encode([
                'model_class' => get_class($this),
                'model_id' => $this->id,
                'timestamp' => now()->toDateTimeString(),
                'changes_count' => count($changedFields)
            ]),
        ];

        return LandOwnerAudit::create($auditData);
    }

    /**
     * Generate automatic audit description
     */
    private function generateAuditDescription(string $action): string
    {
        $name = $this->name ?: 'Land Owner';
        
        switch ($action) {
            case 'created':
                return "Created new land owner: {$name}";
            case 'updated':
                return "Updated land owner: {$name}";
            case 'deleted':
                return "Deleted land owner: {$name}";
            default:
                return "Performed {$action} on land owner: {$name}";
        }
    }
}

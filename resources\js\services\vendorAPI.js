import api from '../config/api';

const vendorAPI = {
  // Get all vendors with pagination and filters
  getVendors: (params = {}) => {
    return api.get('/vendors', { params });
  },

  // Get single vendor by ID
  getVendor: (id) => {
    return api.get(`/vendors/${id}`);
  },

  // Create new vendor
  createVendor: (data) => {
    return api.post('/vendors', data);
  },

  // Update vendor
  updateVendor: (id, data) => {
    return api.put(`/vendors/${id}`, data);
  },

  // Delete vendor
  deleteVendor: (id) => {
    return api.delete(`/vendors/${id}`);
  },

  // Get vendors dropdown
  getVendorsDropdown: () => {
    return api.get('/vendors-dropdown');
  },

  // Get vendor statistics
  getVendorsStatistics: () => {
    return api.get('/vendors-statistics');
  },

  // Bulk status update
  bulkStatusUpdate: (vendorIds, status) => {
    return api.post('/vendors/bulk-status-update', {
      vendor_ids: vendorIds,
      status: status
    });
  }
};

export default vendorAPI;

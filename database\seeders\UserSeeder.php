<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\Role;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get roles
        $superAdminRole = Role::where('name', 'Super Admin')->first();
        $adminRole = Role::where('name', 'Admin')->first();
        $managerRole = Role::where('name', 'Manager')->first();
        $editorRole = Role::where('name', 'Editor')->first();
        $viewerRole = Role::where('name', 'Viewer')->first();

        if (!$superAdminRole) {
            $this->command->warn('Roles not found. Please run RoleSeeder first.');
            return;
        }

        $users = [
            [
                'first_name' => 'Super',
                'last_name' => 'Admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $superAdminRole?->id,
                'email_verified_at' => now(),
                'phone' => '******-0001',
                'company' => 'Real Estate Management System',
                'bio' => 'System super administrator with full access.',
                'timezone' => 'America/New_York',
                'language' => 'en',
            ],
            [
                'first_name' => 'Admin',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $adminRole?->id,
                'email_verified_at' => now(),
                'phone' => '******-0002',
                'company' => 'Real Estate Management System',
                'bio' => 'System administrator for daily operations.',
                'timezone' => 'America/New_York',
                'language' => 'en',
            ],
            [
                'first_name' => 'Manager',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $managerRole?->id,
                'email_verified_at' => now(),
                'phone' => '******-0003',
                'company' => 'Real Estate Group',
                'bio' => 'Land acquisition manager.',
                'timezone' => 'America/Los_Angeles',
                'language' => 'en',
            ],
            [
                'first_name' => 'Editor',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $editorRole?->id,
                'email_verified_at' => now(),
                'phone' => '******-0004',
                'company' => 'Real Estate Group',
                'bio' => 'Content editor and documentation specialist.',
                'timezone' => 'America/Chicago',
                'language' => 'en',
            ],
            [
                'first_name' => 'Viewer',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $viewerRole?->id,
                'email_verified_at' => now(),
                'phone' => '******-0005',
                'company' => 'Real Estate Group',
                'bio' => 'Read-only access user for reports and viewing.',
                'timezone' => 'America/Denver',
                'language' => 'en',
            ],
            [
                'first_name' => 'Demo',
                'last_name' => 'User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'role_id' => $managerRole?->id,
                'email_verified_at' => now(),
                'phone' => '******-0006',
                'company' => 'Demo Real Estate',
                'bio' => 'Demo account for testing and presentations.',
                'timezone' => 'America/Los_Angeles',
                'language' => 'en',
            ],
        ];

        foreach ($users as $userData) {
            // Check if user already exists
            $existingUser = User::where('email', $userData['email'])->first();
            
            if (!$existingUser) {
                User::create($userData);
                $this->command->info("Created user: {$userData['first_name']} {$userData['last_name']} ({$userData['email']})");
            } else {
                $this->command->info("User already exists: {$userData['email']}");
            }
        }

        $this->command->info('User seeding completed!');
        $this->command->info('Default password for all users: password123');
    }
}

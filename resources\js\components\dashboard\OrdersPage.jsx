import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  MoreHorizontal, 
  Plus, 
  Filter,
  Download,
  Search
} from 'lucide-react';
import { Input } from '@/components/ui/input';

const OrdersPage = () => {
  const orders = [
    {
      id: "#12345",
      customer: "<PERSON>",
      email: "<EMAIL>",
      status: "Fulfilled",
      date: "2024-06-23",
      total: "$329.00",
      items: 3
    },
    {
      id: "#12346",
      customer: "<PERSON>",
      email: "<EMAIL>",
      status: "Pending",
      date: "2024-06-23",
      total: "$129.00",
      items: 1
    },
    {
      id: "#12347",
      customer: "<PERSON>",
      email: "<EMAIL>",
      status: "Processing",
      date: "2024-06-22",
      total: "$89.00",
      items: 2
    },
    {
      id: "#12348",
      customer: "<PERSON>",
      email: "<EMAIL>",
      status: "Fulfilled",
      date: "2024-06-22",
      total: "$459.00",
      items: 5
    },
    {
      id: "#12349",
      customer: "Oliver Davis",
      email: "<EMAIL>",
      status: "Cancelled",
      date: "2024-06-21",
      total: "$199.00",
      items: 2
    },
    {
      id: "#12350",
      customer: "Sofia Davis",
      email: "<EMAIL>",
      status: "Fulfilled",
      date: "2024-06-21",
      total: "$79.00",
      items: 1
    }
  ];

  const getStatusBadge = (status) => {
    switch (status) {
      case 'Fulfilled':
        return <Badge variant="default">Fulfilled</Badge>;
      case 'Processing':
        return <Badge variant="secondary">Processing</Badge>;
      case 'Pending':
        return <Badge variant="outline">Pending</Badge>;
      case 'Cancelled':
        return <Badge variant="destructive">Cancelled</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  const stats = [
    {
      title: "Total Orders",
      value: "1,234",
      change: "+12%",
      description: "from last month"
    },
    {
      title: "Pending Orders",
      value: "23",
      change: "-4%",
      description: "from yesterday"
    },
    {
      title: "Average Order Value",
      value: "$156.50",
      change: "+8%",
      description: "from last month"
    },
    {
      title: "Order Fulfillment Rate",
      value: "94.2%",
      change: "****%",
      description: "from last month"
    }
  ];

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Orders</h1>
          <p className="text-muted-foreground">
            Manage and track all customer orders.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            New Order
          </Button>
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                {stat.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stat.value}</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-500">{stat.change}</span> {stat.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Orders table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Recent Orders</CardTitle>
              <CardDescription>
                A list of recent orders from your customers.
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                <Input
                  placeholder="Search orders..."
                  className="pl-10 w-[250px]"
                />
              </div>
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order ID</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Items</TableHead>
                <TableHead>Total</TableHead>
                <TableHead className="w-[100px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {orders.map((order) => (
                <TableRow key={order.id}>
                  <TableCell className="font-medium">{order.id}</TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{order.customer}</div>
                      <div className="text-sm text-muted-foreground">{order.email}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(order.status)}
                  </TableCell>
                  <TableCell>{order.date}</TableCell>
                  <TableCell>{order.items}</TableCell>
                  <TableCell className="font-medium">{order.total}</TableCell>
                  <TableCell>
                    <Button variant="ghost" size="icon">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default OrdersPage;

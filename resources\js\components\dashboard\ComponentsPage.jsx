import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Pa<PERSON>, 
  MousePointer, 
  Type, 
  Layout, 
  Grid, 
  User,
  ChevronDown,
  Co<PERSON>,
  Check
} from 'lucide-react';

const ComponentsPage = () => {
  const [copiedCode, setCopiedCode] = useState('');

  const copyToClipboard = (code, componentName) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(componentName);
    setTimeout(() => setCopiedCode(''), 2000);
  };

  const ComponentSection = ({ title, description, icon: Icon, children }) => (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center gap-2">
          <Icon className="h-5 w-5 text-primary" />
          <CardTitle className="text-lg">{title}</CardTitle>
        </div>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {children}
      </CardContent>
    </Card>
  );

  const CodeBlock = ({ code, componentName }) => (
    <div className="relative">
      <pre className="bg-muted p-4 rounded-md text-sm overflow-x-auto">
        <code>{code}</code>
      </pre>
      <Button
        size="sm"
        variant="outline"
        className="absolute top-2 right-2"
        onClick={() => copyToClipboard(code, componentName)}
      >
        {copiedCode === componentName ? (
          <Check className="h-4 w-4" />
        ) : (
          <Copy className="h-4 w-4" />
        )}
      </Button>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">UI Components</h1>
          <p className="text-muted-foreground">
            Explore and test all available shadcn/ui components in your dashboard
          </p>
        </div>
        <Badge variant="secondary" className="text-sm">
          {10} shadcn/ui Components
        </Badge>
      </div>

      {/* Buttons Section */}
      <ComponentSection
        title="Buttons"
        description="Interactive button components with different variants and sizes"
        icon={MousePointer}
      >
        <div className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button>Default</Button>
            <Button variant="secondary">Secondary</Button>
            <Button variant="outline">Outline</Button>
            <Button variant="ghost">Ghost</Button>
            <Button variant="link">Link</Button>
            <Button variant="destructive">Destructive</Button>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button size="sm">Small</Button>
            <Button size="default">Default</Button>
            <Button size="lg">Large</Button>
            <Button size="icon">
              <User className="h-4 w-4" />
            </Button>
          </div>
          <CodeBlock
            componentName="button"
            code={`<Button variant="default">Click me</Button>
<Button variant="outline" size="sm">Small Button</Button>`}
          />
        </div>
      </ComponentSection>

      {/* Cards Section */}
      <ComponentSection
        title="Cards"
        description="Flexible container components for grouping content"
        icon={Layout}
      >
        <div className="grid gap-4 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Card Title</CardTitle>
              <CardDescription>Card description goes here</CardDescription>
            </CardHeader>
            <CardContent>
              <p>This is the card content area.</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader>
              <CardTitle>Another Card</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Cards are great for organizing content.</p>
            </CardContent>
          </Card>
        </div>
        <CodeBlock
          componentName="card"
          code={`<Card>
  <CardHeader>
    <CardTitle>Card Title</CardTitle>
    <CardDescription>Description</CardDescription>
  </CardHeader>
  <CardContent>
    <p>Content goes here</p>
  </CardContent>
</Card>`}
        />
      </ComponentSection>

      {/* Form Elements Section */}
      <ComponentSection
        title="Form Elements"
        description="Input fields, labels, and form controls"
        icon={Type}
      >
        <div className="space-y-4 max-w-md">
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input id="email" type="email" placeholder="Enter your email" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="message">Message</Label>
            <Textarea id="message" placeholder="Type your message here" />
          </div>
        </div>
        <CodeBlock
          componentName="input"
          code={`<div className="space-y-2">
  <Label htmlFor="email">Email</Label>
  <Input id="email" type="email" placeholder="Enter email" />
</div>`}
        />
      </ComponentSection>

      {/* Badges Section */}
      <ComponentSection
        title="Badges"
        description="Small status indicators and labels"
        icon={Palette}
      >
        <div className="flex flex-wrap gap-2">
          <Badge>Default</Badge>
          <Badge variant="secondary">Secondary</Badge>
          <Badge variant="outline">Outline</Badge>
          <Badge variant="destructive">Destructive</Badge>
        </div>
        <CodeBlock
          componentName="badge"
          code={`<Badge>Default</Badge>
<Badge variant="secondary">Secondary</Badge>
<Badge variant="outline">Outline</Badge>`}
        />
      </ComponentSection>

      {/* Avatar Section */}
      <ComponentSection
        title="Avatars"
        description="User profile pictures and initials"
        icon={User}
      >
        <div className="flex gap-4 items-center">
          <Avatar>
            <AvatarImage src="https://github.com/shadcn.png" alt="Avatar" />
            <AvatarFallback>CN</AvatarFallback>
          </Avatar>
          <Avatar>
            <AvatarFallback>JD</AvatarFallback>
          </Avatar>
        </div>
        <CodeBlock
          componentName="avatar"
          code={`<Avatar>
  <AvatarImage src="https://github.com/shadcn.png" alt="Avatar" />
  <AvatarFallback>CN</AvatarFallback>
</Avatar>`}
        />
      </ComponentSection>

      {/* Dropdown Menu Section */}
      <ComponentSection
        title="Dropdown Menu"
        description="Contextual menus and action lists"
        icon={ChevronDown}
      >
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              Open Menu <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuLabel>My Account</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem>Profile</DropdownMenuItem>
            <DropdownMenuItem>Settings</DropdownMenuItem>
            <DropdownMenuItem>Logout</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        <CodeBlock
          componentName="dropdown"
          code={`<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button variant="outline">Open Menu</Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent>
    <DropdownMenuItem>Profile</DropdownMenuItem>
    <DropdownMenuItem>Settings</DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>`}
        />
      </ComponentSection>

      {/* Table Section */}
      <ComponentSection
        title="Tables"
        description="Data tables for displaying structured information"
        icon={Grid}
      >
        <Table>
          <TableCaption>A list of your recent invoices.</TableCaption>
          <TableHeader>
            <TableRow>
              <TableHead>Invoice</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Method</TableHead>
              <TableHead className="text-right">Amount</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell className="font-medium">INV001</TableCell>
              <TableCell>
                <Badge variant="outline">Paid</Badge>
              </TableCell>
              <TableCell>Credit Card</TableCell>
              <TableCell className="text-right">$250.00</TableCell>
            </TableRow>
            <TableRow>
              <TableCell className="font-medium">INV002</TableCell>
              <TableCell>
                <Badge variant="secondary">Pending</Badge>
              </TableCell>
              <TableCell>PayPal</TableCell>
              <TableCell className="text-right">$150.00</TableCell>
            </TableRow>
          </TableBody>
        </Table>
        <CodeBlock
          componentName="table"
          code={`<Table>
  <TableHeader>
    <TableRow>
      <TableHead>Name</TableHead>
      <TableHead>Status</TableHead>
    </TableRow>
  </TableHeader>
  <TableBody>
    <TableRow>
      <TableCell>John Doe</TableCell>
      <TableCell>Active</TableCell>
    </TableRow>
  </TableBody>
</Table>`}
        />
      </ComponentSection>

      {/* Separator Section */}
      <ComponentSection
        title="Separators"
        description="Visual dividers for content sections"
        icon={Layout}
      >
        <div className="space-y-4">
          <div>
            <p>Content above separator</p>
            <Separator className="my-4" />
            <p>Content below separator</p>
          </div>
        </div>
        <CodeBlock
          componentName="separator"
          code={`<div>
  <p>Content above</p>
  <Separator className="my-4" />
  <p>Content below</p>
</div>`}
        />
      </ComponentSection>

      {/* Available Components to Add */}
      <Card className="border-dashed">
        <CardHeader>
          <div className="flex items-center gap-2">
            <Palette className="h-5 w-5 text-muted-foreground" />
            <CardTitle className="text-lg">Additional shadcn/ui Components</CardTitle>
          </div>
          <CardDescription>
            Popular shadcn/ui components you can add to expand your UI library
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {[
              { name: 'Dialog', description: 'Modal dialogs and overlays' },
              { name: 'Alert', description: 'Alert messages and notifications' },
              { name: 'Checkbox', description: 'Checkbox input controls' },
              { name: 'Radio Group', description: 'Radio button groups' },
              { name: 'Select', description: 'Dropdown select menus' },
              { name: 'Switch', description: 'Toggle switch controls' },
              { name: 'Tabs', description: 'Tabbed content sections' },
              { name: 'Toast', description: 'Notification toasts' },
              { name: 'Tooltip', description: 'Hover tooltips' },
              { name: 'Progress', description: 'Progress bars' },
              { name: 'Slider', description: 'Range slider inputs' },
              { name: 'Calendar', description: 'Date picker calendar' },
            ].map((component) => (
              <div key={component.name} className="p-3 border rounded-lg">
                <h4 className="font-medium text-sm">{component.name}</h4>
                <p className="text-xs text-muted-foreground mt-1">{component.description}</p>
              </div>
            ))}
          </div>
          <div className="mt-4 p-4 bg-muted rounded-lg">
            <p className="text-sm text-muted-foreground">
              💡 <strong>Tip:</strong> You can add these components using the shadcn/ui CLI:
            </p>
            <code className="text-xs bg-background px-2 py-1 rounded mt-2 inline-block">
              npx shadcn-ui@latest add [component-name]
            </code>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ComponentsPage;

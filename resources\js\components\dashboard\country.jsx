import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Globe,
  MapPin,
  Flag,
  Users,
  ChevronLeft,
  ChevronRight,
  Eye,
  EyeOff
} from 'lucide-react';
import Swal from 'sweetalert2';
import DebugUserPermissions from '../debug/DebugUserPermissions';

const CountryPage = () => {
  // State management
  const [countries, setCountries] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingCountry, setEditingCountry] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [statistics, setStatistics] = useState({
    total_countries: 0,
    active_countries: 0,
    inactive_countries: 0,
  });

  // Form data for new country
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    iso_code: '',
    capital: '',
    currency: '',
    phone_code: '',
    status: 'active'
  });

  // Form data for editing country
  const [editFormData, setEditFormData] = useState({
    name: '',
    code: '',
    iso_code: '',
    capital: '',
    currency: '',
    phone_code: '',
    status: 'active'
  });

  // Mock data for demonstration
  const mockCountries = [
    {
      id: 1,
      name: 'Bangladesh',
      code: 'BD',
      iso_code: 'BGD',
      capital: 'Dhaka',
      currency: 'BDT',
      phone_code: '+880',
      status: 'active',
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:30:00Z'
    },
    {
      id: 2,
      name: 'India',
      code: 'IN',
      iso_code: 'IND',
      capital: 'New Delhi',
      currency: 'INR',
      phone_code: '+91',
      status: 'active',
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:30:00Z'
    },
    {
      id: 3,
      name: 'Pakistan',
      code: 'PK',
      iso_code: 'PAK',
      capital: 'Islamabad',
      currency: 'PKR',
      phone_code: '+92',
      status: 'active',
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:30:00Z'
    },
    {
      id: 4,
      name: 'United States',
      code: 'US',
      iso_code: 'USA',
      capital: 'Washington, D.C.',
      currency: 'USD',
      phone_code: '+1',
      status: 'active',
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:30:00Z'
    },
    {
      id: 5,
      name: 'United Kingdom',
      code: 'GB',
      iso_code: 'GBR',
      capital: 'London',
      currency: 'GBP',
      phone_code: '+44',
      status: 'inactive',
      created_at: '2024-01-15T10:30:00Z',
      updated_at: '2024-01-15T10:30:00Z'
    }
  ];

  // Simulate API calls
  useEffect(() => {
    fetchCountries();
  }, [searchTerm, currentPage, perPage]);

  useEffect(() => {
    fetchStatistics();
  }, [countries]);

  const fetchCountries = async () => {
    setLoading(true);
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Filter countries based on search term
    const filteredCountries = mockCountries.filter(country =>
      country.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      country.code.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    setCountries(filteredCountries);
    setTotalRecords(filteredCountries.length);
    setTotalPages(Math.ceil(filteredCountries.length / perPage));
    setLoading(false);
  };

  const fetchStatistics = async () => {
    const stats = {
      total_countries: mockCountries.length,
      active_countries: mockCountries.filter(c => c.status === 'active').length,
      inactive_countries: mockCountries.filter(c => c.status === 'inactive').length,
    };
    setStatistics(stats);
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEditInputChange = (field, value) => {
    setEditFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: 'Country added successfully',
        confirmButtonColor: '#10b981'
      });

      setShowAddModal(false);
      setFormData({
        name: '',
        code: '',
        iso_code: '',
        capital: '',
        currency: '',
        phone_code: '',
        status: 'active'
      });
      fetchCountries();
    } catch (error) {
      Swal.fire({
        icon: 'error',
        title: 'Error!',
        text: 'Failed to add country',
        confirmButtonColor: '#ef4444'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (country) => {
    setEditingCountry(country);
    setEditFormData({
      name: country.name,
      code: country.code,
      iso_code: country.iso_code,
      capital: country.capital,
      currency: country.currency,
      phone_code: country.phone_code,
      status: country.status
    });
    setShowEditModal(true);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchCountries();
  };

  const getStatusColor = (status) => {
    return status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
  };

  const getStatusBadge = (status) => {
    const Icon = status === 'active' ? Eye : EyeOff;
    return (
      <Badge className={`${getStatusColor(status)} border-0`}>
        <Icon className="mr-1 h-3 w-3" />
        {status}
      </Badge>
    );
  };

  return (
    <div className="space-y-6">
      {/* Debug Component - Remove this in production */}
      <DebugUserPermissions />
      
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Country Management</h1>
          <p className="text-muted-foreground">
            Manage countries and their information in the system
          </p>
        </div>
        <Button onClick={() => setShowAddModal(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add New Country
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search Countries
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by country name or code..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button type="submit">
              <Search className="mr-2 h-4 w-4" />
              Search
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Country Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Countries</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.total_countries}</div>
            <p className="text-xs text-muted-foreground">
              {statistics.active_countries} active countries
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Countries</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {statistics.active_countries}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive Countries</CardTitle>
            <EyeOff className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {statistics.inactive_countries}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently inactive
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Countries Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            Countries ({totalRecords})
          </CardTitle>
          <CardDescription>
            Manage countries and their information
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : countries.length === 0 ? (
            <div className="text-center py-8">
              <Globe className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No countries found</h3>
              <p className="mt-1 text-sm text-gray-500">Get started by adding a new country.</p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Code</TableHead>
                    <TableHead>ISO Code</TableHead>
                    <TableHead>Capital</TableHead>
                    <TableHead>Currency</TableHead>
                    <TableHead>Phone Code</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Updated</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {countries.map((country) => (
                    <TableRow key={country.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Flag className="h-4 w-4 text-gray-500" />
                          {country.name}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{country.code}</Badge>
                      </TableCell>
                      <TableCell>
                        <Badge variant="secondary">{country.iso_code}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <MapPin className="h-3 w-3 text-gray-500" />
                          {country.capital}
                        </div>
                      </TableCell>
                      <TableCell>{country.currency}</TableCell>
                      <TableCell>{country.phone_code}</TableCell>
                      <TableCell>
                        {getStatusBadge(country.status)}
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-gray-500">
                          {new Date(country.updated_at).toLocaleDateString()}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEdit(country)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between px-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * perPage) + 1} to {Math.min(currentPage * perPage, totalRecords)} of {totalRecords} entries
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage <= 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage >= totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Country Modal */}
      <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Country</DialogTitle>
            <DialogDescription>
              Fill in the details below to add a new country to the system.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Country Name *</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder="Bangladesh"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="code">Country Code *</Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => handleInputChange('code', e.target.value)}
                    placeholder="BD"
                    maxLength="2"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="iso_code">ISO Code *</Label>
                  <Input
                    id="iso_code"
                    value={formData.iso_code}
                    onChange={(e) => handleInputChange('iso_code', e.target.value)}
                    placeholder="BGD"
                    maxLength="3"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="capital">Capital City</Label>
                  <Input
                    id="capital"
                    value={formData.capital}
                    onChange={(e) => handleInputChange('capital', e.target.value)}
                    placeholder="Dhaka"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="currency">Currency</Label>
                  <Input
                    id="currency"
                    value={formData.currency}
                    onChange={(e) => handleInputChange('currency', e.target.value)}
                    placeholder="BDT"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="phone_code">Phone Code</Label>
                  <Input
                    id="phone_code"
                    value={formData.phone_code}
                    onChange={(e) => handleInputChange('phone_code', e.target.value)}
                    placeholder="+880"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <select
                  id="status"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowAddModal(false)}>
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? 'Adding...' : 'Add Country'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Country Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Edit Country</DialogTitle>
            <DialogDescription>
              Update the country information below.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_name">Country Name *</Label>
                <Input
                  id="edit_name"
                  value={editFormData.name}
                  onChange={(e) => handleEditInputChange('name', e.target.value)}
                  placeholder="Bangladesh"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit_code">Country Code *</Label>
                <Input
                  id="edit_code"
                  value={editFormData.code}
                  onChange={(e) => handleEditInputChange('code', e.target.value)}
                  placeholder="BD"
                  maxLength="2"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_iso_code">ISO Code *</Label>
                <Input
                  id="edit_iso_code"
                  value={editFormData.iso_code}
                  onChange={(e) => handleEditInputChange('iso_code', e.target.value)}
                  placeholder="BGD"
                  maxLength="3"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit_capital">Capital City</Label>
                <Input
                  id="edit_capital"
                  value={editFormData.capital}
                  onChange={(e) => handleEditInputChange('capital', e.target.value)}
                  placeholder="Dhaka"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit_currency">Currency</Label>
                <Input
                  id="edit_currency"
                  value={editFormData.currency}
                  onChange={(e) => handleEditInputChange('currency', e.target.value)}
                  placeholder="BDT"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit_phone_code">Phone Code</Label>
                <Input
                  id="edit_phone_code"
                  value={editFormData.phone_code}
                  onChange={(e) => handleEditInputChange('phone_code', e.target.value)}
                  placeholder="+880"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="edit_status">Status</Label>
              <select
                id="edit_status"
                value={editFormData.status}
                onChange={(e) => handleEditInputChange('status', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setShowEditModal(false)}>
              Cancel
            </Button>
            <Button type="button">
              Update Country
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CountryPage;
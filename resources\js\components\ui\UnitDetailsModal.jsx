import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import { showAlert } from '../../utils/sweetAlert';
import UnitTypePreviewModal from './UnitTypePreviewModal';
import {
    ContentModal,
    ContentModalContent,
    ContentModalHeader,
    ContentModalTitle,
    ContentModalOverlay,
} from './content-modal';
import { 
    Plus, 
    Edit, 
    Trash2, 
    X,
    Save,
    Building,
    Home,
    Tag,
    Eye
} from 'lucide-react';

const UnitDetailsModal = ({ isOpen, onClose, project, onOpenUnitTypes }) => {
    const [unitDetails, setUnitDetails] = useState([]);
    const [unitTypes, setUnitTypes] = useState([]); // Add state for unit types
    const [loading, setLoading] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [deleting, setDeleting] = useState(null); // Track which unit is being deleted
    const [optimisticUpdating, setOptimisticUpdating] = useState(false); // Prevent auto-refresh during optimistic updates
    const [showForm, setShowForm] = useState(false);
    const [editingUnit, setEditingUnit] = useState(null);
    const [showPreviewModal, setShowPreviewModal] = useState(false); // Preview modal state
    const [selectedUnitType, setSelectedUnitType] = useState(null); // Selected unit type for preview
    const [formData, setFormData] = useState({
        project_id: project?.id || '',
        unit_name: '',
        unit_type: '',
        status: 'available'
    });

    useEffect(() => {
        if (isOpen && project && !optimisticUpdating) {
            fetchUnitDetails();
            fetchUnitTypes(); // Fetch unit types when modal opens
            setFormData(prev => ({ ...prev, project_id: project.id }));
        }
    }, [isOpen, project, optimisticUpdating]);

    const fetchUnitDetails = async () => {
        try {
            setLoading(true);
            const response = await fetch(`/api/unit-details?project_id=${project.id}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                    'Accept': 'application/json',
                }
            });

            const data = await response.json();
            if (data.status === 'success') {
                setUnitDetails(data.data);
            } else {
                showAlert('error', 'Error', 'Failed to fetch unit details');
            }
        } catch (error) {
            // Debug statement removed
            showAlert('error', 'Error', 'Failed to fetch unit details');
        } finally {
            setLoading(false);
        }
    };

    const fetchUnitTypes = async () => {
        try {
            const response = await fetch(`/api/unit-types?project_id=${project.id}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                    'Accept': 'application/json',
                }
            });

            const data = await response.json();
            if (data.status === 'success') {
                setUnitTypes(data.data);
            } else {
                // Debug statement removed
            }
        } catch (error) {
            // Debug statement removed
        }
    };

    // Function to refresh both unit details and unit types
    const refreshData = () => {
        if (project) {
            fetchUnitDetails();
            fetchUnitTypes();
        }
    };

    // Function to handle unit type preview
    const handlePreviewUnitType = async (unit) => {
        try {
            // Find the unit type from our existing data first
            const unitType = unitTypes.find(type => type.type_name === unit.unit_type);
            
            if (unitType) {
                setSelectedUnitType(unitType);
                setShowPreviewModal(true);
            } else {
                // If not found, fetch from API
                const response = await fetch(`/api/unit-types?project_id=${project.id}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                        'Accept': 'application/json',
                    }
                });

                const data = await response.json();
                if (data.status === 'success') {
                    const foundType = data.data.find(type => type.type_name === unit.unit_type);
                    if (foundType) {
                        setSelectedUnitType(foundType);
                        setShowPreviewModal(true);
                    } else {
                        showAlert('error', 'Not Found', 'Unit type details not found');
                    }
                }
            }
        } catch (error) {
            // Debug statement removed
            showAlert('error', 'Error', 'Failed to fetch unit type details');
        }
    };

    const closePreviewModal = () => {
        setShowPreviewModal(false);
        setSelectedUnitType(null);
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setSubmitting(true);
        
        // Store original state for potential rollback
        const originalUnits = [...unitDetails];
        
        try {
            const url = editingUnit 
                ? `/api/unit-details/${editingUnit.id}`
                : '/api/unit-details';
            
            const method = editingUnit ? 'PUT' : 'POST';

            // Optimistic update for editing
            if (editingUnit) {
                // Update the unit in the local state immediately
                const updatedUnit = { ...editingUnit, ...formData };
                setUnitDetails(prevUnits => 
                    prevUnits.map(unit => 
                        unit.id === editingUnit.id ? updatedUnit : unit
                    )
                );
            }

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                    'Accept': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();
            
            if (response.ok && data.status === 'success') {
                showAlert('success', 'Success', data.message);
                
                // For new units, add to the list immediately
                if (!editingUnit && data.data) {
                    setUnitDetails(prevUnits => [data.data, ...prevUnits]);
                } else if (editingUnit) {
                    // For updates, update the specific item in the state instead of refetching
                    setUnitDetails(prevUnits => 
                        prevUnits.map(unit => 
                            unit.id === editingUnit.id ? { ...unit, ...data.data } : unit
                        )
                    );
                }
                
                resetForm();
                setShowForm(false);
                setEditingUnit(null);
            } else {
                // Revert optimistic update if the operation failed
                if (editingUnit) {
                    setUnitDetails(originalUnits);
                }
                showAlert('error', 'Error', data.message || 'Failed to save unit details');
            }
        } catch (error) {
            // Revert optimistic update if there was an error
            if (editingUnit) {
                setUnitDetails(originalUnits);
            }
            // Debug statement removed
            showAlert('error', 'Error', 'Failed to save unit details');
        } finally {
            setSubmitting(false);
        }
    };

    const handleEdit = (unit) => {
        setEditingUnit(unit);
        setFormData({
            project_id: unit.project_id,
            unit_name: unit.unit_name || '',
            unit_type: unit.unit_type || '',
            status: unit.status || 'available'
        });
        setShowForm(true);
    };

    const handleDelete = async (unit) => {
        // Prevent multiple deletes of the same unit
        if (deleting === unit.id) return;
        
        const result = await Swal.fire({
            title: 'Are you sure?',
            text: `Delete unit "${unit.unit_name || unit.unit_number || 'Unnamed unit'}"?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!',
            customClass: {
                popup: 'swal2-popup-custom',
                title: 'swal2-title-custom',
                content: 'swal2-content-custom',
                confirmButton: 'swal2-confirm-custom',
                cancelButton: 'swal2-cancel-custom'
            }
        });

        if (result.isConfirmed) {
            // Store original units for potential rollback
            const originalUnits = [...unitDetails];
            
            try {
                setDeleting(unit.id); // Mark as deleting
                setOptimisticUpdating(true); // Prevent auto-refresh
                // Debug statement removed // Debug log
                
                // Optimistically remove the unit from UI immediately
                setUnitDetails(prevUnits => prevUnits.filter(u => u.id !== unit.id));
                
                const response = await fetch(`/api/unit-details/${unit.id}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                // Debug statement removed // Debug log
                
                const data = await response.json();
                // Debug statement removed // Debug log
                
                if (response.ok && data.status === 'success') {
                    showAlert('success', 'Deleted!', data.message || 'Unit deleted successfully');
                    // Debug statement removed
                    
                    // Unit has already been removed from the UI optimistically
                    // Do NOT refetch data to avoid bringing back the deleted item
                } else {
                    // Revert the optimistic update if the delete failed
                    setUnitDetails(originalUnits);
                    // Debug statement removed
                    showAlert('error', 'Error', data.message || 'Failed to delete unit');
                }
            } catch (error) {
                // Revert the optimistic update if there was an error
                setUnitDetails(originalUnits);
                // Debug statement removed
                showAlert('error', 'Error', 'Failed to delete unit: ' + error.message);
            } finally {
                setDeleting(null); // Clear deleting state
                // Re-enable auto-refresh after a delay
                setTimeout(() => {
                    setOptimisticUpdating(false);
                }, 2000);
            }
        }
    };

    const resetForm = () => {
        setFormData({
            project_id: project?.id || '',
            unit_name: '',
            unit_type: '',
            status: 'available'
        });
        setEditingUnit(null);
        setShowForm(false);
    };

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    const getStatusColor = (status) => {
        switch (status) {
            case 'available': return 'bg-green-100 text-green-800';
            case 'sold': return 'bg-red-100 text-red-800';
            case 'reserved': return 'bg-yellow-100 text-yellow-800';
            case 'under_construction': return 'bg-blue-100 text-blue-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    if (!isOpen) return null;

    return (
        <>
            <ContentModal open={isOpen} onOpenChange={(open) => !open && onClose()}>
                <ContentModalOverlay />
                <ContentModalContent className="w-full max-w-6xl max-h-[90vh] overflow-hidden">
                    {/* Header */}
                    <ContentModalHeader className="p-6 border-b">
                        <div className="flex items-center gap-3">
                            <Building className="w-6 h-6 text-blue-600" />
                            <div>
                                <ContentModalTitle className="text-xl font-semibold text-gray-900">
                                    Unit Details - {project?.project_name}
                                </ContentModalTitle>
                                <p className="text-sm text-gray-500">
                                    Manage unit details for this project
                                </p>
                            </div>
                        </div>
                    </ContentModalHeader>

                    <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                        {/* Action Buttons */}
                        <div className="flex justify-between items-center mb-6">
                            <h3 className="text-lg font-medium text-gray-900">
                                Units ({unitDetails.length})
                        </h3>
                        <div className="flex gap-2">
                            <button
                                onClick={refreshData}
                                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
                                title="Refresh data"
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                </svg>
                                Refresh
                            </button>
                            <button
                                onClick={onOpenUnitTypes}
                                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
                            >
                                <Tag className="w-4 h-4" />
                                Add Unit Type
                            </button>
                            <button
                                onClick={() => {
                                    resetForm();
                                    setShowForm(!showForm);
                                }}
                                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
                            >
                                <Plus className="w-4 h-4" />
                                Add New Unit
                            </button>
                        </div>
                    </div>

                    {/* Add/Edit Form */}
                    {showForm && (
                        <div className="bg-gray-50 rounded-lg p-6 mb-6">
                            <h4 className="text-lg font-medium text-gray-900 mb-4">
                                {editingUnit ? 'Edit Unit' : 'Add New Unit'}
                            </h4>
                            <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Unit Name
                                    </label>
                                    <input
                                        type="text"
                                        name="unit_name"
                                        value={formData.unit_name}
                                        onChange={handleInputChange}
                                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="e.g., A1, Penthouse 1"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Unit Type
                                    </label>
                                    <select
                                        name="unit_type"
                                        value={formData.unit_type}
                                        onChange={handleInputChange}
                                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        required
                                    >
                                        <option value="">Select Unit Type</option>
                                        {unitTypes.map((type) => (
                                            <option key={type.id} value={type.type_name}>
                                                {type.type_name}
                                            </option>
                                        ))}
                                    </select>
                                    {unitTypes.length === 0 && (
                                        <p className="text-sm text-gray-500 mt-1">
                                            No unit types available. 
                                            <button
                                                type="button"
                                                onClick={onOpenUnitTypes}
                                                className="text-blue-600 hover:text-blue-800 ml-1"
                                            >
                                                Create one first.
                                            </button>
                                        </p>
                                    )}
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Status
                                    </label>
                                    <select
                                        name="status"
                                        value={formData.status}
                                        onChange={handleInputChange}
                                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                    >
                                        <option value="available">Available</option>
                                        <option value="sold">Sold</option>
                                        <option value="reserved">Reserved</option>
                                        <option value="under_construction">Under Construction</option>
                                    </select>
                                </div>

                                <div className="col-span-full flex gap-3 pt-4">
                                    <button
                                        type="submit"
                                        disabled={submitting}
                                        className={`px-6 py-2 rounded-lg flex items-center gap-2 transition-colors ${
                                            submitting 
                                                ? 'bg-blue-400 cursor-not-allowed' 
                                                : 'bg-blue-600 hover:bg-blue-700'
                                        } text-white`}
                                    >
                                        {submitting ? (
                                            <>
                                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                                {editingUnit ? 'Updating...' : 'Adding...'}
                                            </>
                                        ) : (
                                            <>
                                                <Save className="w-4 h-4" />
                                                {editingUnit ? 'Update Unit' : 'Add Unit'}
                                            </>
                                        )}
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setShowForm(false);
                                            resetForm();
                                        }}
                                        disabled={submitting}
                                        className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors disabled:opacity-50"
                                    >
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>
                    )}

                    {/* Units List */}
                    {loading ? (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                            <p className="text-gray-500 mt-2">Loading units...</p>
                        </div>
                    ) : unitDetails.length === 0 ? (
                        <div className="text-center py-8">
                            <Home className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-500">No units found for this project</p>
                            <p className="text-sm text-gray-400">Click "Add New Unit" to get started</p>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <table className="min-w-full divide-y divide-gray-200">
                                <thead className="bg-gray-50">
                                    <tr>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Unit Name
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Unit Type
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Floor #
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Unit #
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-white divide-y divide-gray-200">
                                    {unitDetails.map((unit) => (
                                        <tr key={unit.id} className="hover:bg-gray-50">
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <div className="text-sm font-medium text-gray-900">
                                                    {unit.unit_name || 'Unnamed Unit'}
                                                </div>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className="text-sm text-gray-900 capitalize">
                                                    {unit.unit_type || '-'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className="text-sm text-gray-900">
                                                    {unit.floor_number || '-'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className="text-sm text-gray-900 font-mono">
                                                    {unit.unit_number || '-'}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap">
                                                <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(unit.status)}`}>
                                                    {unit.status}
                                                </span>
                                            </td>
                                            <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                                <div className="flex items-center gap-2">
                                                    <button
                                                        onClick={() => handleEdit(unit)}
                                                        className="text-blue-600 hover:text-blue-900"
                                                        title="Edit Unit"
                                                    >
                                                        <Edit className="w-4 h-4" />
                                                    </button>
                                                    <button
                                                        onClick={() => handlePreviewUnitType(unit)}
                                                        className="text-green-600 hover:text-green-900"
                                                        title="Preview Unit Type Details"
                                                    >
                                                        <Eye className="w-4 h-4" />
                                                    </button>
                                                    <button
                                                        onClick={() => handleDelete(unit)}
                                                        className="text-red-600 hover:text-red-900"
                                                        title="Delete Unit"
                                                    >
                                                        <Trash2 className="w-4 h-4" />
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}
                </div>
            </ContentModalContent>
        </ContentModal>

        {/* Unit Type Preview Modal */}
        <UnitTypePreviewModal
            isOpen={showPreviewModal}
            onClose={closePreviewModal}
            unitType={selectedUnitType}
        />
        </>
    );
};

export default UnitDetailsModal;

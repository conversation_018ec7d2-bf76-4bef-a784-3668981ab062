export default {
  // Elementos comunes de la interfaz
  common: {
    buttons: {
      add: 'Agregar',
      edit: 'Editar',
      delete: 'Eliminar',
      save: '<PERSON><PERSON>',
      cancel: 'Cancelar',
      confirm: 'Confirmar',
      close: 'Cerrar',
      search: '<PERSON><PERSON>',
      filter: 'Filtrar',
      export: 'Exportar',
      import: 'Importar',
      refresh: 'Actualizar',
      loading: 'Cargando...',
      submit: 'Enviar',
      reset: 'Restablecer',
      clear: 'Limpiar',
      view: 'Ver',
      download: 'Descargar',
      upload: 'Subir'
    },
    status: {
      active: 'Activo',
      inactive: 'Inactivo',
      pending: 'Pendiente',
      approved: 'Aprobado',
      rejected: 'Re<PERSON>zado',
      completed: 'Completado',
      draft: 'Borrador',
      published: 'Publicado'
    },
    messages: {
      success: 'Operación completada exitosamente',
      error: 'Ocurrió un error',
      warning: 'Advertencia',
      info: 'Información',
      loading: 'Cargando, por favor espere...',
      noData: 'No hay datos disponibles',
      confirmDelete: '¿Está seguro de que desea eliminar este elemento?',
      confirmAction: '¿Está seguro de que desea realizar esta acción?',
      saved: 'Cambios guardados exitosamente',
      deleted: 'Elemento eliminado exitosamente',
      updated: 'Elemento actualizado exitosamente',
      created: 'Elemento creado exitosamente'
    },
    navigation: {
      dashboard: 'Panel de Control',
      analytics: 'Analíticas',
      landOwners: 'Propietarios de Tierras',
      landAcquisition: 'Adquisición de Tierras',
      projects: 'Proyectos',
      lifecycle: 'Ciclo de Vida',
      employees: 'Gestión de Empleados',
      contractors: 'Gestión de Contratistas',
      assignContractor: 'Asignar Contratista',
      vendorTypes: 'Tipos de Proveedores',
      vendors: 'Proveedores',
      country: 'País',
      language: 'Idioma',
      currency: 'Moneda',
      customers: 'Clientes',
      orders: 'Órdenes',
      components: 'Componentes',
      reports: 'Reportes',
      roleManagement: 'Gestión de Roles',
      wordAssistant: 'Asistente de Palabras',
      settings: 'Configuración',
      documentsAdmin: 'Documentos y Administración',
      landDevelopment: 'Tierras y Desarrollo',
      hrManagement: 'Gestión de Recursos Humanos',
      developmentCosting: 'Costos de Desarrollo Principal'
    },
    forms: {
      required: 'Este campo es obligatorio',
      invalidEmail: 'Por favor ingrese un email válido',
      invalidPhone: 'Por favor ingrese un teléfono válido',
      passwordMismatch: 'Las contraseñas no coinciden',
      minLength: 'La longitud mínima es {min} caracteres',
      maxLength: 'La longitud máxima es {max} caracteres',
      selectOption: 'Por favor seleccione una opción',
      invalidDate: 'Por favor ingrese una fecha válida',
      invalidNumber: 'Por favor ingrese un número válido'
    },
    table: {
      name: 'Nombre',
      code: 'Código',
      status: 'Estado',
      actions: 'Acciones',
      createdAt: 'Creado en',
      updatedAt: 'Actualizado en',
      noRecords: 'No se encontraron registros',
      showing: 'Mostrando {start} a {end} de {total} entradas',
      previous: 'Anterior',
      next: 'Siguiente',
      rowsPerPage: 'Filas por página'
    },
    auditLog: 'Registro de Auditoría',
    loading: 'Cargando...',
    noData: 'No hay datos disponibles'
  },

  // Panel de control específico
  dashboard: {
    title: 'Panel de Control',
    description: 'Aquí está lo que está sucediendo con tu negocio hoy.',
    welcome: '¡Bienvenido de nuevo, {name}!',
    overview: 'Resumen',
    statistics: 'Estadísticas',
    recentActivity: 'Actividad Reciente',
    quickActions: 'Acciones Rápidas',
    viewReport: 'Ver Reporte',
    statistics: {
      totalRevenue: 'Ingresos Totales',
      subscriptions: 'Suscripciones',
      sales: 'Ventas',
      activeNow: 'Activos Ahora',
      fromLastMonth: 'del mes pasado',
      fromLastHour: 'de la última hora'
    }
  },

  // Gestión de Idiomas
  language: {
    title: 'Gestión de Idiomas',
    addLanguage: 'Agregar Idioma',
    editLanguage: 'Editar Idioma',
    default: 'Predeterminado',
    current: 'Actual',
    select: 'Seleccionar',
    fields: {
      name: 'Nombre',
      code: 'Código',
      nativeName: 'Nombre Nativo',
      flag: 'Bandera (emoji)',
      direction: 'Dirección',
      status: 'Estado',
      isDefault: 'Establecer como idioma predeterminado'
    },
    direction: {
      ltr: 'Izquierda a Derecha (LTR)',
      rtl: 'Derecha a Izquierda (RTL)'
    },
    statistics: {
      totalLanguages: 'Total de Idiomas',
      activeLanguages: 'Idiomas Activos',
      defaultLanguage: 'Idioma Predeterminado',
      ltrLanguages: 'Idiomas LTR',
      rtlLanguages: 'Idiomas RTL'
    },
    messages: {
      setDefault: '¿Establecer como Idioma Predeterminado?',
      setDefaultText: 'Esto hará que este idioma sea el predeterminado para el sistema',
      setDefaultConfirm: '¡Sí, establecer como predeterminado!',
      defaultUpdated: 'Idioma predeterminado actualizado exitosamente',
      onlyActiveCanBeDefault: 'Solo los idiomas activos pueden ser predeterminados'
    }
  },

  // Gestión de Monedas
  currency: {
    title: 'Gestión de Monedas',
    addCurrency: 'Agregar Moneda',
    editCurrency: 'Editar Moneda',
    fields: {
      name: 'Nombre',
      code: 'Código',
      symbol: 'Símbolo',
      exchangeRate: 'Tipo de Cambio',
      isDefault: 'Establecer como moneda predeterminada',
      isActive: 'Activo'
    },
    statistics: {
      totalCurrencies: 'Total de Monedas',
      activeCurrencies: 'Monedas Activas',
      defaultCurrency: 'Moneda Predeterminada',
      averageRate: 'Tipo Promedio'
    },
    messages: {
      setDefault: '¿Establecer como Moneda Predeterminada?',
      setDefaultText: 'Esto hará que esta moneda sea la predeterminada para el sistema',
      setDefaultConfirm: '¡Sí, establecer como predeterminado!',
      defaultUpdated: 'Moneda predeterminada actualizada exitosamente'
    }
  },

  // Propietarios de Tierras
  landOwners: {
    title: 'Propietarios de Tierras',
    description: 'Una lista de todos los propietarios de tierras en el sistema',
    searchTitle: 'Buscar Propietarios de Tierras',
    searchPlaceholder: 'Buscar por nombre, nombre del padre, teléfono, número de NID, o email...',
    addOwner: 'Agregar Nuevo Propietario',
    editOwner: 'Editar Propietario',
    fields: {
      fullName: 'Nombre Completo',
      firstName: 'Nombre',
      lastName: 'Apellido',
      fatherName: 'Nombre del Padre',
      motherName: 'Nombre de la Madre',
      email: 'Email',
      phone: 'Teléfono',
      address: 'Dirección',
      city: 'Ciudad',
      state: 'Estado',
      zipCode: 'Código Postal',
      country: 'País',
      dateOfBirth: 'Fecha de Nacimiento',
      nationalId: 'ID Nacional',
      nidNumber: 'Número de NID',
      status: 'Estado',
      photo: 'Foto',
      documentType: 'Tipo de Documento',
      nidFront: 'NID Frontal',
      nidBack: 'NID Trasero',
      passportPhoto: 'Foto de Pasaporte'
    },
    statistics: {
      totalOwners: 'Total de Propietarios',
      activeOwners: 'Propietarios Activos',
      newThisMonth: 'Nuevos Este Mes',
      totalLands: 'Total de Tierras'
    },
    messages: {
      confirmDelete: '¿Está seguro de que desea eliminar este propietario de tierras?',
      deleteSuccess: 'Propietario de tierras eliminado exitosamente',
      createSuccess: 'Propietario de tierras creado exitosamente',
      updateSuccess: 'Propietario de tierras actualizado exitosamente',
      loadError: 'Error al cargar propietarios de tierras. Inténtelo de nuevo.',
      deleteError: 'Error al eliminar propietario de tierras. Inténtelo de nuevo.',
      createError: 'Error al crear propietario de tierras. Inténtelo de nuevo.',
      updateError: 'Error al actualizar propietario de tierras. Inténtelo de nuevo.'
    }
  },

  // Adquisición de Tierras
  landAcquisition: {
    title: 'Adquisición de Tierras',
    addAcquisition: 'Agregar Adquisición',
    editAcquisition: 'Editar Adquisición',
    fields: {
      landSize: 'Tamaño de Tierra',
      location: 'Ubicación',
      price: 'Precio',
      acquisitionDate: 'Fecha de Adquisición',
      status: 'Estado',
      description: 'Descripción',
      documents: 'Documentos'
    },
    statistics: {
      totalAcquisitions: 'Total de Adquisiciones',
      completedDeals: 'Tratos Completados',
      totalValue: 'Valor Total',
      averageSize: 'Tamaño Promedio'
    }
  },

  // Gestión de Usuarios
  users: {
    title: 'Gestión de Usuarios',
    profile: 'Perfil',
    fields: {
      firstName: 'Nombre',
      lastName: 'Apellido',
      email: 'Email',
      phone: 'Teléfono',
      role: 'Rol',
      company: 'Empresa',
      bio: 'Biografía',
      timezone: 'Zona Horaria',
      language: 'Idioma'
    },
    messages: {
      profileUpdated: 'Perfil actualizado exitosamente',
      passwordChanged: 'Contraseña cambiada exitosamente'
    }
  },

  // Gestión de Roles
  roles: {
    title: 'Gestión de Roles',
    addRole: 'Agregar Rol',
    editRole: 'Editar Rol',
    fields: {
      name: 'Nombre',
      description: 'Descripción',
      permissions: 'Permisos',
      modules: 'Módulos Accesibles'
    },
    permissions: {
      create: 'Crear',
      read: 'Leer',
      update: 'Actualizar',
      delete: 'Eliminar',
      export: 'Exportar',
      manage: 'Gestionar',
      use: 'Usar'
    }
  },

  // Configuración
  settings: {
    title: 'Configuración',
    general: 'Configuración General',
    profile: 'Configuración de Perfil',
    security: 'Configuración de Seguridad',
    notifications: 'Configuración de Notificaciones',
    language: 'Idioma y Localización',
    languageDescription: 'Elige tu idioma preferido',
    appearance: 'Apariencia',
    system: 'Configuración del Sistema'
  },

  // Autenticación
  auth: {
    login: 'Iniciar Sesión',
    logout: 'Cerrar Sesión',
    register: 'Registrarse',
    forgotPassword: 'Olvidé mi Contraseña',
    resetPassword: 'Restablecer Contraseña',
    changePassword: 'Cambiar Contraseña',
    currentPassword: 'Contraseña Actual',
    newPassword: 'Nueva Contraseña',
    confirmPassword: 'Confirmar Contraseña',
    rememberMe: 'Recordarme',
    loginSuccess: 'Inicio de sesión exitoso',
    logoutSuccess: 'Cierre de sesión exitoso',
    invalidCredentials: 'Email o contraseña inválidos',
    sessionExpired: 'Su sesión ha expirado. Por favor inicie sesión nuevamente.'
  },

  // Fecha y Hora
  date: {
    today: 'Hoy',
    yesterday: 'Ayer',
    tomorrow: 'Mañana',
    thisWeek: 'Esta Semana',
    thisMonth: 'Este Mes',
    thisYear: 'Este Año',
    lastWeek: 'Semana Pasada',
    lastMonth: 'Mes Pasado',
    lastYear: 'Año Pasado',
    formats: {
      short: 'DD/MM/YYYY',
      medium: 'DD MMM YYYY',
      long: 'DD MMMM YYYY',
      full: 'dddd, DD MMMM YYYY'
    }
  },

  // Subida de archivos
  upload: {
    selectFile: 'Seleccionar Archivo',
    dropFile: 'Soltar archivo aquí',
    uploading: 'Subiendo...',
    uploadSuccess: 'Archivo subido exitosamente',
    uploadError: 'Error en la subida',
    invalidFileType: 'Tipo de archivo inválido',
    fileTooLarge: 'Archivo demasiado grande',
    maxSize: 'Tamaño máximo de archivo: {size}MB'
  },

  // Gestión de Países
  country: {
    title: 'Gestión de Países',
    description: 'Gestionar países y su información en el sistema',
    searchTitle: 'Buscar Países',
    searchPlaceholder: 'Buscar por nombre, código o continente...',
    addCountry: 'Agregar País',
    editCountry: 'Editar País',
    fields: {
      name: 'Nombre',
      code: 'Código',
      isoCode: 'Código ISO',
      capital: 'Capital',
      currency: 'Moneda',
      phoneCode: 'Código de Teléfono',
      continent: 'Continente',
      population: 'Población',
      status: 'Estado'
    },
    statistics: {
      totalCountries: 'Total de Países',
      activeCountries: 'Países Activos',
      inactiveCountries: 'Países Inactivos'
    },
    messages: {
      confirmDelete: '¿Está seguro de que desea eliminar este país?',
      deleteSuccess: 'País eliminado exitosamente',
      createSuccess: 'País creado exitosamente',
      updateSuccess: 'País actualizado exitosamente',
      loadError: 'Error al cargar países. Inténtelo de nuevo.',
      deleteError: 'Error al eliminar país. Inténtelo de nuevo.',
      createError: 'Error al crear país. Inténtelo de nuevo.',
      updateError: 'Error al actualizar país. Inténtelo de nuevo.'
    }
  }
};

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\AuditableTrait;
use App\Traits\EnvironmentAwareTrait;

class Contractor extends Model
{
    use HasFactory, AuditableTrait, EnvironmentAwareTrait;

    protected $fillable = [
        'name',
        'phone',
        'trade_license',
        'email',
        'address',
        'files',
        'status',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'status' => 'string',
        'files' => 'array'
    ];

    /**
     * Get the user who created this contractor
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this contractor
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope a query to only include active contractors
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include inactive contractors
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }
}

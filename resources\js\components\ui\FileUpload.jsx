import React, { useState, useRef } from 'react';
import { Upload, X, Eye, FileText, Image, File } from 'lucide-react';

// Helper function to safely check if something is a File object
const isFileObject = (obj) => {
  return obj && obj.constructor && obj.constructor.name === 'File';
};

const FileUpload = ({ 
  value, 
  onChange, 
  label = "File", 
  accept = "*/*",
  maxSize = 10 * 1024 * 1024, // 10MB default
  placeholder = "Upload a file",
  className = "",
  disabled = false,
  allowPreview = true
}) => {
  const [preview, setPreview] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef(null);

  // Initialize state when value changes (for editing existing records)
  React.useEffect(() => {
    // Debug statement removed
    if (value && typeof value === 'string') {
      // For existing files (string URLs), don't set selectedFile
      setSelectedFile(null);
      setPreview(null);
    } else if (isFileObject(value)) {
      // For newly selected files
      setSelectedFile(value);
      if (isImageFile(value)) {
        const reader = new FileReader();
        reader.onload = (e) => setPreview(e.target.result);
        reader.readAsDataURL(value);
      }
    } else {
      // No file
      setSelectedFile(null);
      setPreview(null);
    }
  }, [value]);

  // Helper function to get full file URL
  const getFileUrl = (filePath) => {
    if (!filePath) return null;
    
    // If it's already a full URL, return as is
    if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
      return filePath;
    }
    
    // If it starts with /landowners, use the base URL
    if (filePath.startsWith('/landowners/')) {
      return `${window.location.protocol}//${window.location.host}${filePath}`;
    }
    
    // If it starts with /project-docs, use the base URL
    if (filePath.startsWith('/project-docs/')) {
      return `${window.location.protocol}//${window.location.host}${filePath}`;
    }
    
    // If it starts with /storage, use the base URL
    if (filePath.startsWith('/storage/')) {
      return `${window.location.protocol}//${window.location.host}${filePath}`;
    }
    
    // For project documents, assume they are in projects directory
    if (filePath.includes('project-docs/')) {
      return `${window.location.protocol}//${window.location.host}/storage/${filePath}`;
    }
    
    // For land owner documents, assume they are in landowners/documents directory
    return `${window.location.protocol}//${window.location.host}/landowners/documents/${filePath}`;
  };

  // Check if file is an image
  const isImageFile = (file) => {
    if (file && file.constructor && file.constructor.name === 'File') {
      return file.type && file.type.startsWith('image/');
    }
    if (typeof file === 'string') {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];
      return imageExtensions.some(ext => file.toLowerCase().endsWith(ext));
    }
    return false;
  };

  // Check if value is a File object
  const isFileObject = (file) => {
    return file && file.constructor && file.constructor.name === 'File';
  };

  // Get file icon based on type
  const getFileIcon = (file) => {
    if (isImageFile(file)) {
      return <Image className="h-8 w-8 text-blue-500" />;
    }
    
    if (isFileObject(file)) {
      if (file.type && file.type.includes('pdf')) {
        return <FileText className="h-8 w-8 text-red-500" />;
      }
      if (file.type && (file.type.includes('word') || file.type.includes('document'))) {
        return <FileText className="h-8 w-8 text-blue-600" />;
      }
    }
    
    if (typeof file === 'string') {
      if (file.toLowerCase().includes('.pdf')) {
        return <FileText className="h-8 w-8 text-red-500" />;
      }
      if (file.toLowerCase().includes('.doc')) {
        return <FileText className="h-8 w-8 text-blue-600" />;
      }
    }
    
    return <File className="h-8 w-8 text-gray-500" />;
  };

  const handleFileSelect = (file) => {
    if (!file) return;

    console.log('📁 FileUpload: File selected', {
      name: file.name,
      size: file.size,
      type: file.type,
      lastModified: file.lastModified
    });

    // Validate file size
    if (file.size > maxSize) {
      // Debug statement removed
      alert(`File size must be less than ${Math.round(maxSize / (1024 * 1024))}MB`);
      return;
    }

    // Debug statement removed

    // Create preview for images only
    if (isImageFile(file)) {
      const reader = new FileReader();
      reader.onload = (e) => {
        // Debug statement removed
        setPreview(e.target.result);
      };
      reader.onerror = (e) => {
        // Debug statement removed
      };
      reader.readAsDataURL(file);
    } else {
      setPreview(null); // No preview for non-image files
    }

    // Store the file for form submission
    setSelectedFile(file);
    console.log('📤 FileUpload: Calling onChange with file object:', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      isFile: isFileObject(file)
    });
    onChange(file); // Pass the file object to parent
  };

  const handleFileChange = (event) => {
    // Debug statement removed
    const file = event.target.files[0];
    if (file) {
      // Debug statement removed
      handleFileSelect(file);
    }
    // Clear the input value to allow selecting the same file again if needed
    event.target.value = '';
  };

  const handleButtonClick = () => {
    if (disabled) return;
    // Debug statement removed
    fileInputRef.current?.click();
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
    
    if (disabled) return;

    // Debug statement removed
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      // Debug statement removed
      handleFileSelect(files[0]);
    }
  };

  const handleRemove = () => {
    // Debug statement removed
    setPreview(null);
    setSelectedFile(null);
    onChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleViewFile = () => {
    if (selectedFile && isImageFile(selectedFile)) {
      // For image files, show preview
      if (preview) {
        window.open(preview, '_blank');
      }
    } else if (typeof value === 'string') {
      // For existing files, open the URL
      const fileUrl = getFileUrl(value);
      if (fileUrl) {
        window.open(fileUrl, '_blank');
      }
    }
  };

  // Determine what to display
  const hasFile = selectedFile || (value && typeof value === 'string');
  const displayFile = selectedFile || value;
  
  console.log('📁 FileUpload: Render state:', {
    hasFile,
    selectedFile: selectedFile?.name,
    value: typeof value === 'string' ? value : (value && value.constructor && value.constructor.name === 'File') ? value.name : value,
    displayFile: (displayFile && displayFile.constructor && displayFile.constructor.name === 'File') ? displayFile.name : displayFile
  });
  
  return (
    <div className={`space-y-2 ${className}`}>
      {label && <label className="block text-sm font-medium text-gray-700">{label}</label>}
      
      {!hasFile ? (
        // Upload area
        <div
          className={`
            relative border-2 border-dashed rounded-lg p-6 text-center transition-colors
            ${dragOver ? 'border-primary bg-primary/5' : 'border-gray-300'}
            ${disabled ? 'opacity-50' : 'hover:border-gray-400'}
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            ref={fileInputRef}
            type="file"
            onChange={handleFileChange}
            accept={accept}
            className="hidden"
            disabled={disabled}
          />
          
          <Upload className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-600">{placeholder}</p>
          <p className="text-xs text-gray-500 mt-1">
            Max size: {Math.round(maxSize / (1024 * 1024))}MB
          </p>
          
          <button
            type="button"
            className="mt-4 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            onClick={handleButtonClick}
            disabled={disabled}
          >
            <Upload className="mr-2 h-4 w-4" />
            Choose File
          </button>
        </div>
      ) : (
        // File preview/info
        <div className="border rounded-lg p-4 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getFileIcon(displayFile)}
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {selectedFile ? selectedFile.name : (typeof value === 'string' ? value.split('/').pop() : 'File')}
                </p>
                {selectedFile && (
                  <p className="text-xs text-gray-500">
                    {Math.round(selectedFile.size / 1024)} KB • {selectedFile.type || 'Unknown type'}
                  </p>
                )}
              </div>
            </div>
            
            <div className="flex space-x-2">
              {allowPreview && (isImageFile(displayFile) || typeof value === 'string') && (
                <button
                  type="button"
                  className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  onClick={handleViewFile}
                  disabled={disabled}
                >
                  <Eye className="h-4 w-4" />
                </button>
              )}
              <button
                type="button"
                className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                onClick={handleRemove}
                disabled={disabled}
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
          
          {/* Image preview */}
          {preview && isImageFile(displayFile) && (
            <div className="mt-3">
              <img
                src={preview}
                alt="Preview"
                className="max-w-full h-32 object-cover rounded border"
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FileUpload;

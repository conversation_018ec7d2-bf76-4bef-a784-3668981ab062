<?php
// Simple API test script
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once __DIR__ . '/../vendor/autoload.php';

// Bootstrap Laravel
$app = require_once __DIR__ . '/../bootstrap/app.php';
$app->make(\Illuminate\Contracts\Console\Kernel::class)->bootstrap();

use Modules\LandOwners\Models\LandOwner;

try {
    $landOwners = LandOwner::select('id', 'first_name', 'last_name', 'father_name')
        ->where('status', 'active')
        ->orderBy('first_name')
        ->get();

    $response = [
        'success' => true,
        'data' => $landOwners->toArray(),
        'message' => 'Land owners fetched successfully',
        'count' => $landOwners->count()
    ];

    echo json_encode($response, JSON_PRETTY_PRINT);
    
} catch (Exception $e) {
    $response = [
        'success' => false,
        'message' => 'Error: ' . $e->getMessage(),
        'data' => []
    ];
    
    echo json_encode($response, JSON_PRETTY_PRINT);
}

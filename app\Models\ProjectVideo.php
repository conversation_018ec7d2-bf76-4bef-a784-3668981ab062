<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectVideo extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'project_id',
        'video_type',
        'video_path',
        'video_url',
        'youtube_url',
        'title',
        'description',
        'duration',
        'thumbnail',
        'sort_order',
        'is_featured',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'duration' => 'integer',
        'sort_order' => 'integer',
        'is_featured' => 'boolean'
    ];

    protected $dates = ['deleted_at'];

    // Relationships
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Scopes
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'asc');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('video_type', $type);
    }

    // Accessors
    public function getVideoUrlAttribute()
    {
        if ($this->video_type === 'upload' && $this->video_path) {
            return asset('storage/' . $this->video_path);
        }
        
        if ($this->video_type === 'youtube' && $this->youtube_url) {
            return $this->youtube_url;
        }
        
        return $this->attributes['video_url'] ?? null;
    }

    public function getThumbnailUrlAttribute()
    {
        if ($this->thumbnail) {
            return asset('storage/' . $this->thumbnail);
        }
        
        if ($this->video_type === 'youtube' && $this->youtube_url) {
            $videoId = $this->extractYouTubeId($this->youtube_url);
            return "https://img.youtube.com/vi/{$videoId}/maxresdefault.jpg";
        }
        
        return null;
    }

    public function getEmbedUrlAttribute()
    {
        if ($this->video_type === 'youtube' && $this->youtube_url) {
            $videoId = $this->extractYouTubeId($this->youtube_url);
            return "https://www.youtube.com/embed/{$videoId}";
        }
        
        return null;
    }

    // Helper methods
    private function extractYouTubeId($url)
    {
        preg_match('/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/', $url, $matches);
        return $matches[1] ?? null;
    }
}

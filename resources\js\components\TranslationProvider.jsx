import React, { createContext, useContext, useState, useEffect } from 'react';
import { translator } from '../services/translationService.js';

const TranslationContext = createContext({
  isLoaded: false,
  currentLanguage: 'en',
  t: (key) => key,
  changeLanguage: () => {},
  formatDate: (date) => date,
  formatNumber: (number) => number,
  formatCurrency: (amount) => amount
});

export const useTranslation = () => {
  const context = useContext(TranslationContext);
  if (!context) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  return context;
};

export const TranslationProvider = ({ children }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    const initializeTranslations = async () => {
      try {
        setIsInitializing(true);
       
        await translator.init();
 
        setIsLoaded(true);
        setCurrentLanguage(translator.getCurrentLanguage());
      } catch (error) {
        
        // Set defaults in case of error
        setIsLoaded(true);
        setCurrentLanguage('en');
      } finally {
        setIsInitializing(false);
      }
    };

    initializeTranslations();

    // Listen for language changes
    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail.language);
    };

    const handleTranslationsLoaded = (event) => {
      setIsLoaded(true);
      setCurrentLanguage(event.detail.language);
    };

    window.addEventListener('languageChanged', handleLanguageChange);
    window.addEventListener('translationsLoaded', handleTranslationsLoaded);

    return () => {
      window.removeEventListener('languageChanged', handleLanguageChange);
      window.removeEventListener('translationsLoaded', handleTranslationsLoaded);
    };
  }, []);

  const t = (key, params = {}) => {
    if (!isLoaded) {
     
      return key; // Return key if not loaded yet
    }
    const translated = translator.t(key, params);
   
    
    return translated;
  };

  const changeLanguage = async (languageCode) => {
    try {
      await translator.changeLanguage(languageCode);
      setCurrentLanguage(languageCode);
    } catch (error) {
      console.error('Failed to change language:', error);
    }
  };

  const formatDate = (date, options = {}) => {
    return translator.formatDate(date, options);
  };

  const formatNumber = (number, options = {}) => {
    return translator.formatNumber(number, options);
  };

  const formatCurrency = (amount, currency = 'USD') => {
    return translator.formatCurrency(amount, currency);
  };

  const getAvailableLanguages = async () => {
    return await translator.getAvailableLanguages();
  };

  // Show loading while initializing translations
  if (isInitializing) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const value = {
    isLoaded,
    currentLanguage,
    t,
    changeLanguage,
    formatDate,
    formatNumber,
    formatCurrency,
    getAvailableLanguages
  };

  return (
    <TranslationContext.Provider value={value}>
      {children}
    </TranslationContext.Provider>
  );
};

export default TranslationProvider;

import axiosInstance from '../config/api';

const employeeAPI = {
  // Get all employees with filters and pagination
  getEmployees: async (params = {}) => {
    const response = await axiosInstance.get('/employees', { params });
    return response.data;
  },

  // Get employee by ID
  getEmployee: async (id) => {
    const response = await axiosInstance.get(`/employees/${id}`);
    return response.data;
  },

  // Create new employee
  createEmployee: async (employeeData) => {
    const response = await axiosInstance.post('/employees', employeeData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Update employee
  updateEmployee: async (id, employeeData) => {
    // Add the _method field for Laravel PUT method spoofing
    employeeData.append('_method', 'PUT');
    
    const response = await axiosInstance.post(`/employees/${id}`, employeeData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  // Delete employee
  deleteEmployee: async (id) => {
    const response = await axiosInstance.delete(`/employees/${id}`);
    return response.data;
  },

  // Get employees for dropdown
  getEmployeesDropdown: async () => {
    const response = await axiosInstance.get('/employees-dropdown');
    return response.data;
  },

  // Get employee statistics
  getStatistics: async () => {
    const response = await axiosInstance.get('/employees-statistics');
    return response.data;
  },

  // Bulk status update
  bulkStatusUpdate: async (data) => {
    const response = await axiosInstance.post('/employees/bulk-status-update', data);
    return response.data;
  },

  // Export employees
  exportEmployees: async (params = {}) => {
    const response = await axiosInstance.get('/employees/export', {
      params,
      responseType: 'blob',
    });
    return response.data;
  },
};

export default employeeAPI;

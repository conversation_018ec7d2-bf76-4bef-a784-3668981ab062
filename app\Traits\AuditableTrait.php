<?php

namespace App\Traits;

use Illuminate\Support\Facades\Auth;
use Modules\LandOwners\Models\LandOwnerAudit;

trait AuditableTrait
{
    /**
     * Store original values before updating
     */
    private $originalAuditValues = [];

    /**
     * Boot the auditable trait for a model.
     */
    protected static function bootAuditableTrait()
    {
        // Set created_by when creating and capture data for audit
        static::creating(function ($model) {
            if (Auth::check()) {
                $model->created_by = Auth::id();
                $model->updated_by = Auth::id();
            } else {
                // For testing purposes, allow creation without auth but log it
                $model->created_by = null;
                $model->updated_by = null;
            }
        });

        // After created - create audit record
        static::created(function ($model) {
            if (method_exists($model, 'createAudit')) {
                $newValues = $model->getAuditableAttributes();
                $model->createAudit('created', null, $newValues);
            }
        });

        // Before updating - capture original values
        static::updating(function ($model) {
            if (Auth::check()) {
                $model->updated_by = Auth::id();
            } else {
                // For testing purposes, allow updates without auth
                $model->updated_by = null;
            }

            // Store original values for audit
            if (method_exists($model, 'createAudit')) {
                $model->originalAuditValues = $model->getOriginal();
            }
        });

        // After updated - create audit record
        static::updated(function ($model) {
            if (method_exists($model, 'createAudit')) {
                $oldValues = $model->originalAuditValues ?? [];
                $newValues = $model->getAuditableAttributes();
                
                // Only create audit if there are actual changes
                $changes = $model->getChanges();
                if (!empty($changes)) {
                    $model->createAudit('updated', $oldValues, $newValues);
                }
            }
        });

        // Before deleted - create audit record (before actual deletion to avoid FK constraint issues)
        static::deleting(function ($model) {
            if (method_exists($model, 'createAudit')) {
                $oldValues = $model->getAuditableAttributes();
                $model->createAudit('deleted', $oldValues, null);
            }
        });
    }

    /**
     * Get attributes that should be audited
     */
    public function getAuditableAttributes(): array
    {
        // Get fillable attributes excluding sensitive fields
        $fillable = $this->getFillable();
        $attributes = [];
        
        foreach ($fillable as $field) {
            // Skip certain fields from audit
            if (!in_array($field, ['password', 'remember_token', 'created_by', 'updated_by'])) {
                $attributes[$field] = $this->getAttribute($field);
            }
        }
        
        // Add timestamps
        $attributes['created_at'] = $this->created_at?->toDateTimeString();
        $attributes['updated_at'] = $this->updated_at?->toDateTimeString();
        
        return $attributes;
    }
}

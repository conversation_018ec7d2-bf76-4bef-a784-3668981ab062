<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ProductionCorsMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $response = $next($request);

        // Handle preflight OPTIONS requests
        if ($request->getMethod() === "OPTIONS") {
            $response->headers->set('Access-Control-Allow-Origin', $this->getAllowedOrigin($request));
            $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
            $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
            $response->headers->set('Access-Control-Allow-Credentials', 'true');
            $response->headers->set('Access-Control-Max-Age', '86400');
            return $response;
        }

        // Set CORS headers for all responses
        $response->headers->set('Access-Control-Allow-Origin', $this->getAllowedOrigin($request));
        $response->headers->set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS, PATCH');
        $response->headers->set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, Accept, Origin');
        $response->headers->set('Access-Control-Allow-Credentials', 'true');

        return $response;
    }

    /**
     * Get the allowed origin based on the request
     */
    private function getAllowedOrigin(Request $request): string
    {
        $origin = $request->headers->get('origin');
        
        // Define allowed origins
        $allowedOrigins = [
            'http://localhost',
            'http://localhost:3000',
            'http://127.0.0.1',
            'http://127.0.0.1:3000',
            'https://yourdomain.com',
            'https://www.yourdomain.com',
        ];

        // Check if origin is in allowed list
        if (in_array($origin, $allowedOrigins)) {
            return $origin;
        }

        // For production, check patterns
        if (app()->environment('production')) {
            if (preg_match('#^https?://.*\.yourdomain\.com$#', $origin) || 
                preg_match('#^https?://yourdomain\.com$#', $origin)) {
                return $origin;
            }
        }

        // Default to app URL
        return config('app.url');
    }
}

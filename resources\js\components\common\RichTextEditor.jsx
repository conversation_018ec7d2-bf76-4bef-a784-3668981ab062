import React, { useMemo } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';

const RichTextEditor = ({ 
    value, 
    onChange, 
    placeholder = "Enter description...",
    className = "",
    height = "150px",
    readOnly = false,
    theme = "snow"
}) => {
    // Quill modules configuration
    const modules = useMemo(() => ({
        toolbar: [
            [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
            ['bold', 'italic', 'underline', 'strike'],
            [{ 'color': [] }, { 'background': [] }],
            [{ 'list': 'ordered'}, { 'list': 'bullet' }],
            [{ 'indent': '-1'}, { 'indent': '+1' }],
            [{ 'align': [] }],
            ['link', 'image'],
            ['clean']
        ],
        clipboard: {
            matchVisual: false,
        }
    }), []);

    // Quill formats
    const formats = [
        'header', 'font', 'size',
        'bold', 'italic', 'underline', 'strike', 'blockquote',
        'list', 'bullet', 'indent',
        'link', 'image', 'color', 'background',
        'align'
    ];

    const editorStyle = {
        height: height,
        backgroundColor: 'white'
    };

    return (
        <div className={`rich-text-editor ${className}`}>
            <style>
                {`
                    .rich-text-editor .ql-editor {
                        min-height: ${height};
                        font-family: inherit;
                        font-size: 14px;
                        line-height: 1.5;
                    }
                    .rich-text-editor .ql-toolbar {
                        border-top: 1px solid #d1d5db;
                        border-left: 1px solid #d1d5db;
                        border-right: 1px solid #d1d5db;
                        border-radius: 6px 6px 0 0;
                        background: #f9fafb;
                    }
                    .rich-text-editor .ql-container {
                        border-bottom: 1px solid #d1d5db;
                        border-left: 1px solid #d1d5db;
                        border-right: 1px solid #d1d5db;
                        border-radius: 0 0 6px 6px;
                        font-family: inherit;
                    }
                    .rich-text-editor .ql-editor:focus {
                        outline: none;
                    }
                    .rich-text-editor .ql-editor.ql-blank::before {
                        color: #9ca3af;
                        font-style: normal;
                    }
                `}
            </style>
            <ReactQuill
                theme={theme}
                value={value || ''}
                onChange={onChange}
                modules={modules}
                formats={formats}
                placeholder={placeholder}
                readOnly={readOnly}
                style={editorStyle}
            />
        </div>
    );
};

export default RichTextEditor;

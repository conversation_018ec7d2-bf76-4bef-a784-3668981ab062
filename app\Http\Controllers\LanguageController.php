<?php

namespace App\Http\Controllers;

use App\Models\Language;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class LanguageController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Language::query();

            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('code', 'LIKE', "%{$search}%")
                      ->orWhere('native_name', 'LIKE', "%{$search}%");
                });
            }

            if ($request->has('status') && $request->status) {
                $query->where('status', $request->status);
            }

            $sortBy = $request->get('sort_by', 'name');
            $sortOrder = $request->get('sort_order', 'asc');
            $query->orderBy($sortBy, $sortOrder);

            $perPage = $request->get('per_page', 10);
            $languages = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $languages->items(),
                'pagination' => [
                    'current_page' => $languages->currentPage(),
                    'last_page' => $languages->lastPage(),
                    'per_page' => $languages->perPage(),
                    'total' => $languages->total()
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function store(Request $request): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'name' => 'required|string|max:255|unique:languages,name',
                'code' => 'required|string|max:10|unique:languages,code',
                'native_name' => 'nullable|string|max:255',
                'flag' => 'nullable|string|max:255',
                'direction' => 'required|in:ltr,rtl',
                'status' => 'required|in:active,inactive'
            ]);

            $language = Language::create($validatedData);
            return response()->json(['success' => true, 'data' => $language], 201);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function show(Language $language): JsonResponse
    {
        return response()->json(['success' => true, 'data' => $language]);
    }

    public function update(Request $request, Language $language): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'name' => ['required', 'string', 'max:255', Rule::unique('languages')->ignore($language->id)],
                'code' => ['required', 'string', 'max:10', Rule::unique('languages')->ignore($language->id)],
                'native_name' => 'nullable|string|max:255',
                'flag' => 'nullable|string|max:255',
                'direction' => 'required|in:ltr,rtl',
                'status' => 'required|in:active,inactive'
            ]);

            $language->update($validatedData);
            return response()->json(['success' => true, 'data' => $language->fresh()]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function destroy(Language $language): JsonResponse
    {
        try {
            $language->delete();
            return response()->json(['success' => true, 'message' => 'Language deleted successfully']);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }

    public function setDefault($id): JsonResponse
    {
        try {
            $language = Language::findOrFail($id);
            
            // Ensure language is active before setting as default
            if ($language->status !== 'active') {
                return response()->json([
                    'success' => false,
                    'message' => 'Only active languages can be set as default'
                ], 400);
            }

            // Use the model method to set default
            Language::setDefault($id);

            return response()->json([
                'success' => true,
                'message' => 'Default language updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function getStatistics(): JsonResponse
    {
        try {
            $defaultLanguage = Language::getDefault();
            $stats = [
                'total_languages' => Language::count(),
                'active_languages' => Language::where('status', 'active')->count(),
                'inactive_languages' => Language::where('status', 'inactive')->count(),
                'ltr_languages' => Language::where('direction', 'ltr')->count(),
                'rtl_languages' => Language::where('direction', 'rtl')->count(),
                'default_language' => $defaultLanguage ? $defaultLanguage->name : 'None',
            ];
            return response()->json(['success' => true, 'data' => $stats]);
        } catch (\Exception $e) {
            return response()->json(['success' => false, 'message' => $e->getMessage()], 500);
        }
    }
}

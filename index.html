<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real Estate Management System - Project Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 50px;
        }

        .header h1 {
            font-size: 3.5em;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.9;
            margin-bottom: 30px;
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 50px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-10px);
        }

        .stat-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #4299e1;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #718096;
            font-weight: 500;
        }

        .documentation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }

        .doc-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .doc-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #4299e1, #48bb78);
        }

        .doc-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0, 0, 0, 0.3);
        }

        .doc-icon {
            font-size: 3.5em;
            margin-bottom: 20px;
            display: block;
        }

        .doc-title {
            font-size: 1.5em;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 15px;
        }

        .doc-description {
            color: #718096;
            line-height: 1.6;
            margin-bottom: 25px;
        }

        .doc-actions {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9em;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(66, 153, 225, 0.4);
        }

        .btn-secondary {
            background: rgba(113, 128, 150, 0.1);
            color: #4a5568;
            border: 2px solid rgba(113, 128, 150, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(113, 128, 150, 0.2);
            transform: translateY(-2px);
        }

        .features {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        .features h2 {
            color: #2d3748;
            margin-bottom: 30px;
            font-size: 2em;
            text-align: center;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
        }

        .feature-item {
            background: #f7fafc;
            padding: 25px;
            border-radius: 15px;
            border-left: 5px solid #4299e1;
        }

        .feature-title {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .feature-description {
            color: #718096;
            font-size: 0.95em;
            line-height: 1.5;
        }

        .tech-stack {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
        }

        .tech-stack h2 {
            color: #2d3748;
            margin-bottom: 30px;
            font-size: 2em;
            text-align: center;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
        }

        .tech-item {
            background: #f7fafc;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
        }

        .tech-item:hover {
            border-color: #4299e1;
            transform: translateY(-5px);
        }

        .tech-icon {
            font-size: 2.5em;
            margin-bottom: 10px;
            display: block;
        }

        .tech-name {
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .tech-version {
            color: #718096;
            font-size: 0.9em;
        }

        @media (max-width: 768px) {
            .container {
                padding: 20px 15px;
            }

            .header h1 {
                font-size: 2.5em;
            }

            .header p {
                font-size: 1.1em;
            }

            .stats {
                grid-template-columns: repeat(2, 1fr);
            }

            .documentation-grid {
                grid-template-columns: 1fr;
            }

            .doc-card {
                padding: 30px 25px;
            }

            .features,
            .tech-stack {
                padding: 30px 25px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏢 Real Estate Management System</h1>
            <p>Complete Project Documentation & Resources</p>
            <p style="font-size: 1em; opacity: 0.8;">Laravel 12 • React • Tailwind CSS • MySQL</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-icon">📊</div>
                <div class="stat-number">11</div>
                <div class="stat-label">Database Tables</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🎮</div>
                <div class="stat-number">10</div>
                <div class="stat-label">API Controllers</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🏗️</div>
                <div class="stat-number">11</div>
                <div class="stat-label">Eloquent Models</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🔄</div>
                <div class="stat-number">23</div>
                <div class="stat-label">Migrations</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🛣️</div>
                <div class="stat-number">50+</div>
                <div class="stat-label">API Endpoints</div>
            </div>
            <div class="stat-card">
                <div class="stat-icon">🔐</div>
                <div class="stat-number">5</div>
                <div class="stat-label">User Roles</div>
            </div>
        </div>

        <div class="documentation-grid">
            <div class="doc-card">
                <div class="doc-icon">📋</div>
                <div class="doc-title">Project Index Report</div>
                <div class="doc-description">
                    Comprehensive markdown report containing detailed analysis of all models, controllers, routes, migrations, and system architecture.
                </div>
                <div class="doc-actions">
                    <a href="PROJECT_INDEX.md" class="btn btn-primary" target="_blank">
                        📄 View Report
                    </a>
                    <a href="project-index.json" class="btn btn-secondary" target="_blank">
                        📊 JSON Data
                    </a>
                </div>
            </div>

            <div class="doc-card">
                <div class="doc-icon">🗃️</div>
                <div class="doc-title">Database Schema</div>
                <div class="doc-description">
                    Interactive visualization of the database structure, relationships, tables, columns, and indexes with detailed documentation.
                </div>
                <div class="doc-actions">
                    <a href="database-schema.html" class="btn btn-primary" target="_blank">
                        🎯 View Schema
                    </a>
                </div>
            </div>

            <div class="doc-card">
                <div class="doc-icon">🔌</div>
                <div class="doc-title">API Documentation</div>
                <div class="doc-description">
                    Complete REST API reference with endpoints, parameters, request/response examples, and authentication details.
                </div>
                <div class="doc-actions">
                    <a href="api-documentation.html" class="btn btn-primary" target="_blank">
                        📡 API Docs
                    </a>
                </div>
            </div>

            <div class="doc-card">
                <div class="doc-icon">⚙️</div>
                <div class="doc-title">Project Indexer</div>
                <div class="doc-description">
                    PHP script that automatically scans and indexes your entire Laravel project, generating comprehensive documentation.
                </div>
                <div class="doc-actions">
                    <a href="index-project.php" class="btn btn-secondary" target="_blank">
                        🔍 View Script
                    </a>
                </div>
            </div>
        </div>

        <div class="features">
            <h2>✨ System Features</h2>
            <div class="features-grid">
                <div class="feature-item">
                    <div class="feature-title">👥 Land Owner Management</div>
                    <div class="feature-description">Complete CRUD operations for land owners with document uploads, photo management, and audit trails.</div>
                </div>
                <div class="feature-item">
                    <div class="feature-title">🏗️ Land Acquisition Tracking</div>
                    <div class="feature-description">Detailed land acquisition records with pricing, measurements, and geographic information.</div>
                </div>
                <div class="feature-item">
                    <div class="feature-title">🔐 Role-Based Access Control</div>
                    <div class="feature-description">Comprehensive permission system with 5 predefined roles and modular access control.</div>
                </div>
                <div class="feature-item">
                    <div class="feature-title">📄 Document Management</div>
                    <div class="feature-description">File upload and management system for land documents, NID cards, and passport photos.</div>
                </div>
                <div class="feature-item">
                    <div class="feature-title">📊 Audit Trail System</div>
                    <div class="feature-description">Complete audit logging for all land owner changes with detailed history tracking.</div>
                </div>
                <div class="feature-item">
                    <div class="feature-title">🌍 Multi-Regional Support</div>
                    <div class="feature-description">Country, language, and currency management for international operations.</div>
                </div>
                <div class="feature-item">
                    <div class="feature-title">🔍 Advanced Search & Filtering</div>
                    <div class="feature-description">Powerful search capabilities across all entities with multiple filter options.</div>
                </div>
                <div class="feature-item">
                    <div class="feature-title">📱 RESTful API</div>
                    <div class="feature-description">Well-structured REST API with Sanctum authentication and comprehensive endpoints.</div>
                </div>
            </div>
        </div>

        <div class="tech-stack">
            <h2>🛠️ Technology Stack</h2>
            <div class="tech-grid">
                <div class="tech-item">
                    <div class="tech-icon">🐘</div>
                    <div class="tech-name">Laravel</div>
                    <div class="tech-version">v12.0</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">🐘</div>
                    <div class="tech-name">PHP</div>
                    <div class="tech-version">v8.2+</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">⚛️</div>
                    <div class="tech-name">React</div>
                    <div class="tech-version">v18.2</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">⚡</div>
                    <div class="tech-name">Vite</div>
                    <div class="tech-version">Latest</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">🎨</div>
                    <div class="tech-name">Tailwind CSS</div>
                    <div class="tech-version">Latest</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">🗄️</div>
                    <div class="tech-name">MySQL</div>
                    <div class="tech-version">8.0+</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">🔐</div>
                    <div class="tech-name">Sanctum</div>
                    <div class="tech-version">v4.1</div>
                </div>
                <div class="tech-item">
                    <div class="tech-icon">📦</div>
                    <div class="tech-name">Composer</div>
                    <div class="tech-version">Latest</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add some interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Animate stats on load
            const statNumbers = document.querySelectorAll('.stat-number');
            statNumbers.forEach(stat => {
                const finalValue = stat.textContent;
                stat.textContent = '0';
                
                let current = 0;
                const increment = finalValue.includes('+') ? 
                    parseInt(finalValue) / 50 : 
                    parseInt(finalValue) / 30;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= parseInt(finalValue)) {
                        stat.textContent = finalValue;
                        clearInterval(timer);
                    } else {
                        stat.textContent = Math.floor(current).toString();
                    }
                }, 50);
            });

            // Add click effects to cards
            const cards = document.querySelectorAll('.doc-card, .stat-card');
            cards.forEach(card => {
                card.addEventListener('click', function(e) {
                    if (!e.target.closest('.btn')) {
                        const links = this.querySelectorAll('.btn-primary');
                        if (links.length > 0) {
                            links[0].click();
                        }
                    }
                });
            });
        });
    </script>
</body>
</html>

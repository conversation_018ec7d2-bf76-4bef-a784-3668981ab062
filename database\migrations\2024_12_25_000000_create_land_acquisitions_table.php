<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('land_acquisitions', function (Blueprint $table) {
            $table->id();
            $table->string('record_dag')->unique(); // Record DAG number
            $table->string('khatian'); // Khatian number
            $table->string('mauza'); // Mauza name/area
            $table->decimal('land_size', 10, 2); // Land size in decimal/acres
            $table->decimal('acquisition_price', 15, 2); // Acquisition price
            $table->unsignedBigInteger('landOwners_id'); // Foreign key to land_owners table
            $table->timestamps();

            // Indexes
            $table->index(['record_dag']);
            $table->index(['khatian']);
            $table->index(['mauza']);
            $table->index(['landOwners_id']);

            // Foreign key constraint
            $table->foreign('landOwners_id')->references('id')->on('land_owners')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('land_acquisitions');
    }
};

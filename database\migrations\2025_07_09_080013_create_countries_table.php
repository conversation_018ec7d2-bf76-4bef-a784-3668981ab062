<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('countries', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('code', 2)->unique(); // ISO 3166-1 alpha-2
            $table->string('iso_code', 3)->unique(); // ISO 3166-1 alpha-3
            $table->string('capital')->nullable();
            $table->string('currency', 3)->nullable();
            $table->string('phone_code', 10)->nullable();
            $table->string('continent')->nullable();
            $table->string('population')->nullable();
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->timestamps();
            
            // Add indexes for better performance
            $table->index(['status']);
            $table->index(['continent']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('countries');
    }
};

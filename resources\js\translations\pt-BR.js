// Brazilian Portuguese translations - inherits from Portuguese with regional differences
import ptTranslations from './pt.js';

export default {
  ...ptTranslations,
  
  // Override specific translations for Brazilian Portuguese
  common: {
    ...ptTranslations.common,
    buttons: {
      ...ptTranslations.common.buttons,
      delete: 'Excluir', // Brazilian preference
      cancel: 'Cancelar',
      search: 'Pesquisar', // Brazilian preference
    },
    messages: {
      ...ptTranslations.common.messages,
      confirmDelete: 'Tem certeza que deseja excluir este item?',
      deleted: 'Item excluído com sucesso',
    },
    navigation: {
      ...ptTranslations.common.navigation,
      landOwners: 'Proprietários de Terra',
      landAcquisition: 'Aquisição de Terras',
      customers: 'Clientes',
      orders: 'Pedidos',
    },
    forms: {
      ...ptTranslations.common.forms,
      zipCode: 'CEP',
      state: 'Estado',
    }
  },

  // Brazilian specific overrides
  landOwners: {
    ...ptTranslations.landOwners,
    fields: {
      ...ptTranslations.landOwners.fields,
      zipCode: 'CEP',
      state: 'Estado',
      nationalId: 'CPF',
      nidNumber: 'Número do CPF',
    },
    messages: {
      ...ptTranslations.landOwners.messages,
      confirmDelete: 'Tem certeza que deseja excluir este proprietário?',
      deleteSuccess: 'Proprietário excluído com sucesso',
      createSuccess: 'Proprietário criado com sucesso',
      updateSuccess: 'Proprietário atualizado com sucesso',
      loadError: 'Falha ao carregar proprietários. Tente novamente.',
      deleteError: 'Falha ao excluir proprietário. Tente novamente.',
      createError: 'Falha ao criar proprietário. Tente novamente.',
      updateError: 'Falha ao atualizar proprietário. Tente novamente.'
    }
  },

  // Date formats for Brazil
  date: {
    ...ptTranslations.date,
    formats: {
      short: 'DD/MM/YYYY',
      medium: 'DD/MM/YYYY',
      long: 'DD de MMMM de YYYY',
      full: 'dddd, DD de MMMM de YYYY'
    }
  },

  // Currency specific to Brazil
  currency: {
    ...ptTranslations.currency,
    fields: {
      ...ptTranslations.currency.fields,
      symbol: 'Símbolo (R$)',
    }
  },

  // Brazilian specific terminology
  country: {
    ...ptTranslations.country,
    fields: {
      ...ptTranslations.country.fields,
      phoneCode: 'Código de Área',
      state: 'Estado',
    }
  }
};

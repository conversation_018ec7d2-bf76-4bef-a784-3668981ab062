import React, { useState } from 'react';
import { useTranslation } from './TranslationProvider';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Globe, Check, X } from 'lucide-react';

const TranslationTestPage = () => {
  const { t, isLoaded, currentLanguage, changeLanguage } = useTranslation();
  const [testResults, setTestResults] = useState({});

  const testTranslationKeys = [
    'common.navigation.dashboard',
    'common.navigation.landOwners',
    'common.navigation.landAcquisition',
    'common.navigation.documentsAdmin',
    'common.buttons.add',
    'common.buttons.edit',
    'common.buttons.delete',
    'common.buttons.save',
    'common.buttons.cancel',
    'dashboard.title',
    'dashboard.description',
    'dashboard.statistics.totalRevenue',
    'dashboard.statistics.subscriptions',
    'dashboard.viewReport',
    'language.title',
    'language.addLanguage',
    'language.fields.name',
    'language.fields.code',
    'language.direction.ltr',
    'language.direction.rtl'
  ];

  const runTranslationTest = () => {
    const results = {};
    
    testTranslationKeys.forEach(key => {
      const translation = t(key);
      results[key] = {
        key,
        translation,
        hasTranslation: translation !== key,
        isEmpty: !translation || translation.trim() === ''
      };
    });

    setTestResults(results);
  };

  const getStatusIcon = (result) => {
    if (result.hasTranslation && !result.isEmpty) {
      return <Check className="w-4 h-4 text-green-500" />;
    }
    return <X className="w-4 h-4 text-red-500" />;
  };

  const getStatusColor = (result) => {
    if (result.hasTranslation && !result.isEmpty) {
      return 'bg-green-100 text-green-800';
    }
    return 'bg-red-100 text-red-800';
  };

  const availableLanguages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'es', name: 'Spanish', flag: '🇪🇸' },
    { code: 'fr', name: 'French', flag: '🇫🇷' },
    { code: 'de', name: 'German', flag: '🇩🇪' },
    { code: 'it', name: 'Italian', flag: '🇮🇹' },
    { code: 'pt', name: 'Portuguese', flag: '🇵🇹' },
    { code: 'pt-BR', name: 'Brazilian Portuguese', flag: '🇧🇷' },
    { code: 'ar', name: 'Arabic', flag: '🇸🇦' },
    { code: 'zh-CN', name: 'Chinese (Simplified)', flag: '🇨🇳' },
    { code: 'nl', name: 'Dutch', flag: '🇳🇱' },
    { code: 'hi', name: 'Hindi', flag: '🇮🇳' },
    { code: 'ja', name: 'Japanese', flag: '🇯🇵' },
    { code: 'ko', name: 'Korean', flag: '🇰🇷' }
  ];

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Translation System Test</h1>
          <p className="text-gray-600 mt-2">Test the translation system functionality</p>
        </div>
        <Badge variant="outline" className="text-sm">
          <Globe className="w-4 h-4 mr-1" />
          {currentLanguage.toUpperCase()}
        </Badge>
      </div>

      {/* System Status */}
      <Card>
        <CardHeader>
          <CardTitle>System Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${isLoaded ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm">
                Translations {isLoaded ? 'Loaded' : 'Loading...'}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-blue-500" />
              <span className="text-sm">Current Language: {currentLanguage}</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded-full bg-purple-500" />
              <span className="text-sm">
                Test Results: {Object.keys(testResults).length} keys tested
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Language Switcher */}
      <Card>
        <CardHeader>
          <CardTitle>Language Switcher</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {availableLanguages.map(lang => (
              <Button
                key={lang.code}
                variant={currentLanguage === lang.code ? "default" : "outline"}
                size="sm"
                onClick={() => changeLanguage(lang.code)}
                className="flex items-center space-x-2"
              >
                <span>{lang.flag}</span>
                <span>{lang.name}</span>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Test Controls */}
      <Card>
        <CardHeader>
          <CardTitle>Translation Test</CardTitle>
        </CardHeader>
        <CardContent>
          <Button onClick={runTranslationTest} disabled={!isLoaded}>
            Run Translation Test
          </Button>
        </CardContent>
      </Card>

      {/* Test Results */}
      {Object.keys(testResults).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(testResults).map(([key, result]) => (
                <div key={key} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(result)}
                    <div>
                      <p className="font-mono text-sm text-gray-600">{key}</p>
                      <p className="text-sm">{result.translation}</p>
                    </div>
                  </div>
                  <Badge 
                    variant="outline" 
                    className={getStatusColor(result)}
                  >
                    {result.hasTranslation && !result.isEmpty ? 'OK' : 'MISSING'}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sample Translations */}
      <Card>
        <CardHeader>
          <CardTitle>Sample Translations (Current Language: {currentLanguage})</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="font-semibold mb-2">Navigation</h3>
              <ul className="space-y-1 text-sm">
                <li>Dashboard: <span className="font-mono">{t('common.navigation.dashboard')}</span></li>
                <li>Land Owners: <span className="font-mono">{t('common.navigation.landOwners')}</span></li>
                <li>Land Acquisition: <span className="font-mono">{t('common.navigation.landAcquisition')}</span></li>
                <li>Settings: <span className="font-mono">{t('common.navigation.settings')}</span></li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Buttons</h3>
              <ul className="space-y-1 text-sm">
                <li>Add: <span className="font-mono">{t('common.buttons.add')}</span></li>
                <li>Edit: <span className="font-mono">{t('common.buttons.edit')}</span></li>
                <li>Delete: <span className="font-mono">{t('common.buttons.delete')}</span></li>
                <li>Save: <span className="font-mono">{t('common.buttons.save')}</span></li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TranslationTestPage;

import React from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from '../TranslationProvider';
import { showAlertMethods as showAlert } from '@/utils/sweetAlert';
import LanguageSwitcher from '@/components/ui/LanguageSwitcher';
import {
  Home,
  BarChart3,
  Users,
  Settings,
  FileText,
  X,
  Shield,
  ShoppingCart,
  UserCheck,
  Layers,
  Bot,
  RotateCcw,
  Building,
  Building2,
  Globe,
  Globe2,
  Map,
  MapPin,
  Languages,
  DollarSign,
  LogOut,
  IdCard,
  HardHat,
  Package,
  Truck,
  AlertCircle,
  Lock
} from 'lucide-react';

const Sidebar = ({ onClose }) => {
  const { 
    canAccessModule, 
    hasModuleAccess, 
    isModuleAvailable,
    loading, 
    user, 
    role, 
    logout, 
    moduleDetails 
  } = useAuth();
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();
  
  // Get current page from URL path
  const currentPage = location.pathname.replace('/', '') || 'dashboard';

  // Handle navigation with confirmation for sensitive pages
  const handleNavigation = async (item, event) => {
    // Check if module is available
    if (!isModuleAvailable(item.moduleKey)) {
      event.preventDefault();
      
      const moduleDetail = moduleDetails[item.moduleKey];
      const errorMsg = moduleDetail?.errors?.join(', ') || 'Module controller or model not found';
      
      await showAlert.error(
        'Module Unavailable',
        `The ${t(item.nameKey)} module is not available. ${errorMsg}`,
        'OK'
      );
      return;
    }

    // Pages that require confirmation
    const sensitivePages = ['role', 'settings'];
    
    if (sensitivePages.includes(item.page)) {
      event.preventDefault();
      
      try {
        const result = await showAlert.confirm(
          'Access Administrative Area',
          `You are about to access ${t(item.nameKey)}. This area contains sensitive system settings. Are you sure you want to continue?`,
          'Yes, continue',
          'Cancel'
        );

        if (result.isConfirmed) {
          navigate(`/${item.page}`);
          onClose && onClose();
        }
      } catch (error) {
        console.error('Error showing confirmation dialog:', error);
        // Fallback to basic confirm
        if (window.confirm(`Access ${item.page}? This area contains sensitive system settings.`)) {
          navigate(`/${item.page}`);
          onClose && onClose();
        }
      }
    } else {
      // Normal navigation, just close sidebar if needed
      onClose && onClose();
    }
  };

  // Handle logout with confirmation
  const handleLogout = async () => {
    const result = await showAlert.confirm(
      'Sign Out',
      'Are you sure you want to sign out of your account?',
      'Yes, sign out',
      'Cancel'
    );

    if (result.isConfirmed) {
      showAlert.loading('Signing out...', 'Please wait while we sign you out');
      
      try {
        await logout();
        showAlert.close();
        showAlert.success('Signed Out', 'You have been successfully signed out', 1500);
      } catch (error) {
        showAlert.close();
        showAlert.error('Sign Out Failed', 'There was an error signing you out. Please try again.');
        console.error('Logout failed:', error);
      }
    }
  };

  // All available navigation items with their module keys and translation keys
  // Only include items that have corresponding controllers and models
  const allNavigationItems = [
    { nameKey: 'common.navigation.dashboard', page: 'dashboard', icon: Home, moduleKey: 'dashboard' },
    { nameKey: 'common.navigation.landOwners', page: 'land-owners', icon: Users, moduleKey: 'land-owners' },
    { nameKey: 'common.navigation.landAcquisition', page: 'land-acquisition', icon: Building, moduleKey: 'land-acquisition' },
    { nameKey: 'common.navigation.projects', page: 'projects', icon: Building2, moduleKey: 'project' },
    { nameKey: 'common.navigation.employees', page: 'employees', icon: IdCard, moduleKey: 'employees' },
    { nameKey: 'common.navigation.contractors', page: 'contractors', icon: HardHat, moduleKey: 'contractors' },
    { nameKey: 'common.navigation.vendorTypes', page: 'vendor-types', icon: Package, moduleKey: 'vendor-type' },
    { nameKey: 'common.navigation.vendors', page: 'vendors', icon: Truck, moduleKey: 'vendor' },
    { nameKey: 'common.navigation.propertyAmenities', page: 'property-amenities', icon: Building, moduleKey: 'property-amenity' },
    { nameKey: 'common.navigation.roleManagement', page: 'role', icon: Shield, moduleKey: 'role' },
    { nameKey: 'common.navigation.countries', page: 'countries', icon: Globe, moduleKey: 'country' },
    { nameKey: 'common.navigation.states', page: 'states', icon: Map, moduleKey: 'state' },
    { nameKey: 'common.navigation.cities', page: 'cities', icon: MapPin, moduleKey: 'city' },
    { nameKey: 'common.navigation.currencies', page: 'currencies', icon: DollarSign, moduleKey: 'currency' },
    { nameKey: 'common.navigation.languages', page: 'languages', icon: Globe2, moduleKey: 'language' },
    { nameKey: 'common.navigation.settings', page: 'settings', icon: Settings, moduleKey: 'settings' },
  ];

  if (loading) {
    return (
      <div className="flex h-full flex-col bg-background border-r">
        <div className="flex h-16 items-center px-6 border-b">
          <div className="animate-pulse bg-gray-200 h-6 w-32 rounded"></div>
        </div>
        <div className="flex-1 p-4 space-y-2">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-gray-200 h-10 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  // Filter navigation items based on user's accessible modules AND module availability
  const accessibleNavItems = allNavigationItems.filter(item => 
    canAccessModule(item.moduleKey)
  );

  // Group items into main and secondary navigation based on existing controllers/models
  const mainNavigation = accessibleNavItems.filter(item => 
    ['dashboard', 'land-owners', 'land-acquisition', 'project'].includes(item.moduleKey)
  );

  const managementNavigation = accessibleNavItems.filter(item => 
    ['employees', 'contractors', 'vendor-type', 'vendor', 'property-amenity'].includes(item.moduleKey)
  );

  const systemNavigation = accessibleNavItems.filter(item => 
    ['country', 'state', 'city', 'currency', 'language'].includes(item.moduleKey)
  );

  const documentsAdminNavigation = accessibleNavItems.filter(item => 
    ['role', 'settings'].includes(item.moduleKey)
  );

  // Helper function to render navigation item with status indicators
  const renderNavItem = (item) => {
    const Icon = item.icon;
    const isActive = currentPage === item.page;
    const moduleDetail = moduleDetails[item.moduleKey];
    const isAvailable = isModuleAvailable(item.moduleKey);
    const hasAccess = hasModuleAccess(item.moduleKey);
    
    // Determine status and styling
    let statusIcon = null;
    let statusColor = '';
    let tooltip = '';
    
    if (!isAvailable) {
      statusIcon = <AlertCircle className="h-3 w-3" />;
      statusColor = 'text-red-500';
      tooltip = `Module unavailable: ${moduleDetail?.errors?.join(', ') || 'Controller or model missing'}`;
    } else if (isAvailable && !hasAccess) {
      statusIcon = <Lock className="h-3 w-3" />;
      statusColor = 'text-yellow-500';
      tooltip = 'No permission to access this module';
    }

    return (
      <Link
        key={item.nameKey}
        to={`/${item.page}`}
        onClick={(event) => handleNavigation(item, event)}
        className={cn(
          "w-full flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors text-left relative group",
          isActive
            ? "bg-accent text-accent-foreground"
            : "text-muted-foreground hover:bg-accent hover:text-accent-foreground",
          !isAvailable && "opacity-60 cursor-not-allowed"
        )}
        title={tooltip}
      >
        <Icon className="h-4 w-4 flex-shrink-0" />
        <span className="flex-1">{t(item.nameKey)}</span>
        {statusIcon && (
          <span className={cn("flex-shrink-0", statusColor)}>
            {statusIcon}
          </span>
        )}
      </Link>
    );
  };

  return (
    <div className="flex h-full flex-col bg-background border-r">
      {/* Company Logo */}
      <div className="flex h-16 items-center px-6 border-b">
        <div className="flex items-center">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-foreground">
            <span className="text-xs font-bold text-background">R</span>
          </div>
          <span className="ml-2 text-sm font-semibold">Real Estate</span>
        </div>
        
        {/* Close button for mobile */}
        <Button
          variant="ghost"
          size="icon"
          className="ml-auto lg:hidden"
          onClick={onClose}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* User Info */}
      <div className="px-6 py-4 border-b bg-muted/50">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
            <span className="text-xs font-medium text-white">
              {user?.name?.charAt(0) || 'U'}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{user?.name || 'User'}</p>
            <p className="text-xs text-muted-foreground truncate">{role?.name || 'No Role'}</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-1">
        {/* Main Navigation */}
        {mainNavigation.map(renderNavItem)}

        {/* Management Section */}
        {managementNavigation.length > 0 && (
          <div className="pt-6">
            <div className="px-3 mb-2">
              <h3 className="text-xs font-medium text-muted-foreground">
                {t('common.navigation.managementSection', 'Management')}
              </h3>
            </div>
            {managementNavigation.map(renderNavItem)}
          </div>
        )}

        {/* Documents & Admin Section */}
        {documentsAdminNavigation.length > 0 && (
          <div className="pt-6">
            <div className="px-3 mb-2">
              <h3 className="text-xs font-medium text-muted-foreground">
                {t('common.navigation.documentsAdmin', 'Documents & Admin')}
              </h3>
            </div>
            {documentsAdminNavigation.map(renderNavItem)}
          </div>
        )}

        {/* System Configuration Section */}
        {systemNavigation.length > 0 && (
          <div className="pt-6">
            <div className="px-3 mb-2">
              <h3 className="text-xs font-medium text-muted-foreground">
                {t('common.navigation.systemConfiguration', 'System Configuration')}
              </h3>
            </div>
            {systemNavigation.map(renderNavItem)}
          </div>
        )}
      </nav>
      
      {/* Language Switcher and Logout at bottom */}
      <div className="px-4 py-4 border-t space-y-3">
        <LanguageSwitcher variant="dropdown" size="sm" />
        
        {/* Logout Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={handleLogout}
          className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200 hover:border-red-300"
        >
          <LogOut className="h-4 w-4 mr-2" />
          {t('Logout', 'Sign Out')}
        </Button>
      </div>
    </div>
  );
};

export default Sidebar;

<?php

namespace App\Http\Controllers;

use App\Models\Contractor;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class ContractorController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Contractor::with(['creator', 'updater']);

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('phone', 'like', "%{$searchTerm}%")
                  ->orWhere('trade_license', 'like', "%{$searchTerm}%")
                  ->orWhere('email', 'like', "%{$searchTerm}%")
                  ->orWhere('address', 'like', "%{$searchTerm}%");
            });
        }

        // Status filter
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        
        $allowedSortFields = ['name', 'phone', 'trade_license', 'email', 'status', 'created_at'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Pagination
        $perPage = $request->get('per_page', 10);
        $contractors = $query->paginate($perPage);

        return response()->json([
            'status' => 'success',
            'data' => $contractors
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => 'required|string|max:20|unique:contractors,phone',
            'trade_license' => 'required|string|max:255|unique:contractors,trade_license',
            'email' => 'required|email|max:255|unique:contractors,email',
            'address' => 'nullable|string|max:1000',
            'status' => 'sometimes|in:active,inactive',
            'files.*' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240' // 10MB max
        ]);

        DB::beginTransaction();

        try {
            // Set the creator
            $validated['created_by'] = auth()->id();

            // Handle file uploads
            $uploadedFiles = [];
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    $originalName = $file->getClientOriginalName();
                    $fileName = time() . '_' . $originalName;
                    $filePath = $file->storeAs('contractor-documents', $fileName, 'public');
                    
                    $uploadedFiles[] = [
                        'name' => $originalName,
                        'path' => $filePath,
                        'url' => Storage::url($filePath),
                        'size' => $file->getSize(),
                        'mime_type' => $file->getMimeType()
                    ];
                }
            }

            $validated['files'] = $uploadedFiles;

            $contractor = Contractor::create($validated);
            $contractor->load(['creator', 'updater']);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Contractor created successfully',
                'data' => $contractor
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create contractor: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Contractor $contractor)
    {
        $contractor->load(['creator', 'updater']);
        
        return response()->json([
            'status' => 'success',
            'data' => $contractor
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Contractor $contractor)
    {
        // Log the incoming request data for debugging
        \Log::info('Contractor update request data', [
            'contractor_id' => $contractor->id,
            'request_all' => $request->all(),
            'request_method' => $request->method(),
            'content_type' => $request->header('Content-Type')
        ]);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'phone' => [
                'required',
                'string',
                'max:20',
                Rule::unique('contractors')->ignore($contractor->id)
            ],
            'trade_license' => [
                'required',
                'string',
                'max:255',
                Rule::unique('contractors')->ignore($contractor->id)
            ],
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('contractors')->ignore($contractor->id)
            ],
            'address' => 'nullable|string|max:1000',
            'status' => 'sometimes|in:active,inactive',
            'files.*' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:10240', // 10MB max
            'existing_files' => 'nullable|array',
            'existing_files.*' => 'nullable|array'
        ]);

        DB::beginTransaction();

        try {
            // Set the updater
            $validated['updated_by'] = auth()->id();

            // Handle existing files (files to keep)
            $existingFiles = $request->input('existing_files', []);
            
            // Handle new file uploads
            $newFiles = [];
            if ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    $originalName = $file->getClientOriginalName();
                    $fileName = time() . '_' . $originalName;
                    $filePath = $file->storeAs('contractor-documents', $fileName, 'public');
                    
                    $newFiles[] = [
                        'name' => $originalName,
                        'path' => $filePath,
                        'url' => Storage::url($filePath),
                        'size' => $file->getSize(),
                        'mime_type' => $file->getMimeType()
                    ];
                }
            }

            // Combine existing files with new files
            $allFiles = array_merge($existingFiles, $newFiles);
            $validated['files'] = $allFiles;

            $contractor->update($validated);
            $contractor->load(['creator', 'updater']);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Contractor updated successfully',
                'data' => $contractor
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update contractor: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Contractor $contractor)
    {
        DB::beginTransaction();

        try {
            $contractor->delete();

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Contractor deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete contractor: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get contractors for dropdown
     */
    public function dropdown()
    {
        $contractors = Contractor::where('status', 'active')
                                ->select('id', 'name', 'trade_license')
                                ->orderBy('name')
                                ->get();

        return response()->json([
            'status' => 'success',
            'data' => $contractors
        ]);
    }

    /**
     * Get contractor statistics
     */
    public function statistics()
    {
        $stats = [
            'total_contractors' => Contractor::count(),
            'active_contractors' => Contractor::where('status', 'active')->count(),
            'inactive_contractors' => Contractor::where('status', 'inactive')->count(),
        ];

        return response()->json([
            'status' => 'success',
            'data' => $stats
        ]);
    }

    /**
     * Bulk status update
     */
    public function bulkStatusUpdate(Request $request)
    {
        $validated = $request->validate([
            'contractor_ids' => 'required|array',
            'contractor_ids.*' => 'exists:contractors,id',
            'status' => 'required|in:active,inactive'
        ]);

        $updatedCount = Contractor::whereIn('id', $validated['contractor_ids'])
                                 ->update([
                                     'status' => $validated['status'],
                                     'updated_by' => auth()->id()
                                 ]);

        return response()->json([
            'status' => 'success',
            'message' => "Successfully updated {$updatedCount} contractors",
            'data' => ['updated_count' => $updatedCount]
        ]);
    }
}

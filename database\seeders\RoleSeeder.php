<?php

namespace Database\Seeders;

use App\Models\Role;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RoleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $roles = [
            [
                'name' => 'Super Admin',
                'description' => 'Full system access with all permissions',
                'accessible_modules' => [
                    'dashboard', 'analytics', 'landowners', 'land-owners', 'land-acquisition', 'project', 'lifecycle', 
                    'employees', 'contractors', 'assign-contractor', 'assign-vendor', 'assign-employee', 
                    'vendor-type', 'vendor', 'property-amenity', 'customer', 'orders', 'components', 
                    'reports', 'role', 'word-assistant', 'settings', 'country', 'state', 'city', 'location',
                    'language', 'currency'
                ],
                'module_permissions' => [
                    'dashboard' => ['read'],
                    'analytics' => ['read'],
                    'landowners' => ['create', 'read', 'update', 'delete'],
                    'land-owners' => ['create', 'read', 'update', 'delete'],
                    'land-acquisition' => ['create', 'read', 'update', 'delete'],
                    'project' => ['create', 'read', 'update', 'delete'],
                    'lifecycle' => ['create', 'read', 'update', 'delete'],
                    'employees' => ['create', 'read', 'update', 'delete'],
                    'contractors' => ['create', 'read', 'update', 'delete'],
                    'assign-contractor' => ['create', 'read', 'update', 'delete'],
                    'assign-vendor' => ['create', 'read', 'update', 'delete'],
                    'assign-employee' => ['create', 'read', 'update', 'delete'],
                    'vendor-type' => ['create', 'read', 'update', 'delete'],
                    'vendor' => ['create', 'read', 'update', 'delete'],
                    'property-amenity' => ['create', 'read', 'update', 'delete'],
                    'customer' => ['create', 'read', 'update', 'delete'],
                    'orders' => ['create', 'read', 'update', 'delete'],
                    'components' => ['read'],
                    'reports' => ['read', 'export'],
                    'role' => ['create', 'read', 'update', 'delete'],
                    'word-assistant' => ['read', 'use'],
                    'settings' => ['read', 'update'],
                    'country' => ['create', 'read', 'update', 'delete'],
                    'state' => ['create', 'read', 'update', 'delete'],
                    'city' => ['create', 'read', 'update', 'delete'],
                    'location' => ['create', 'read', 'update', 'delete'],
                    'language' => ['create', 'read', 'update', 'delete'],
                    'currency' => ['create', 'read', 'update', 'delete']
                ],
                'status' => 'active',
                'users_count' => 2
            ],
            [
                'name' => 'Admin',
                'description' => 'Administrative access with limited system settings',
                'accessible_modules' => [
                    'dashboard', 'analytics', 'landowners', 'land-owners', 'land-acquisition', 'project', 'lifecycle', 
                    'employees', 'contractors', 'assign-contractor', 'assign-vendor', 'assign-employee', 
                    'vendor-type', 'vendor', 'property-amenity', 'customer', 'orders', 'components', 
                    'reports', 'role', 'settings', 'country', 'state', 'city', 'location', 'language', 'currency'
                ],
                'module_permissions' => [
                    'dashboard' => ['read'],
                    'analytics' => ['read'],
                    'landowners' => ['create', 'read', 'update', 'delete'],
                    'land-owners' => ['create', 'read', 'update', 'delete'],
                    'land-acquisition' => ['create', 'read', 'update', 'delete'],
                    'project' => ['create', 'read', 'update', 'delete'],
                    'lifecycle' => ['read', 'update'],
                    'employees' => ['create', 'read', 'update', 'delete'],
                    'contractors' => ['create', 'read', 'update', 'delete'],
                    'assign-contractor' => ['create', 'read', 'update'],
                    'assign-vendor' => ['create', 'read', 'update'],
                    'assign-employee' => ['create', 'read', 'update'],
                    'vendor-type' => ['create', 'read', 'update', 'delete'],
                    'vendor' => ['create', 'read', 'update', 'delete'],
                    'property-amenity' => ['create', 'read', 'update', 'delete'],
                    'customer' => ['create', 'read', 'update'],
                    'orders' => ['read', 'update'],
                    'components' => ['read'],
                    'reports' => ['read', 'export'],
                    'role' => ['read'],
                    'settings' => ['read'],
                    'country' => ['create', 'read', 'update', 'delete'],
                    'state' => ['create', 'read', 'update', 'delete'],
                    'city' => ['create', 'read', 'update', 'delete'],
                    'location' => ['create', 'read', 'update', 'delete'],
                    'language' => ['create', 'read', 'update', 'delete'],
                    'currency' => ['create', 'read', 'update', 'delete']
                ],
                'status' => 'active',
                'users_count' => 5
            ],
            [
                'name' => 'Manager',
                'description' => 'Management level access to land and owner records',
                'accessible_modules' => [
                    'dashboard', 'analytics', 'landowners', 'land-owners', 'land-acquisition', 'project', 'lifecycle', 
                    'employees', 'contractors', 'vendor-type', 'vendor', 'property-amenity', 'customer', 
                    'reports', 'country', 'state', 'city', 'location', 'language', 'currency'
                ],
                'module_permissions' => [
                    'dashboard' => ['read'],
                    'analytics' => ['read'],
                    'landowners' => ['create', 'read', 'update'],
                    'land-owners' => ['create', 'read', 'update'],
                    'land-acquisition' => ['create', 'read', 'update'],
                    'project' => ['create', 'read', 'update'],
                    'lifecycle' => ['read'],
                    'employees' => ['create', 'read', 'update'],
                    'contractors' => ['create', 'read', 'update'],
                    'vendor-type' => ['create', 'read', 'update'],
                    'vendor' => ['create', 'read', 'update'],
                    'property-amenity' => ['create', 'read', 'update'],
                    'customer' => ['read', 'update'],
                    'reports' => ['read'],
                    'country' => ['create', 'read', 'update'],
                    'state' => ['create', 'read', 'update'],
                    'city' => ['create', 'read', 'update'],
                    'location' => ['create', 'read', 'update'],
                    'language' => ['create', 'read', 'update'],
                    'currency' => ['create', 'read', 'update']
                ],
                'status' => 'active',
                'users_count' => 8
            ],
            [
                'name' => 'Editor',
                'description' => 'Can create and edit records but not delete',
                'accessible_modules' => ['dashboard', 'land-owners', 'land-acquisition', 'customer', 'orders'],
                'module_permissions' => [
                    'dashboard' => ['read'],
                    'land-owners' => ['create', 'read', 'update'],
                    'land-acquisition' => ['create', 'read', 'update'],
                    'customer' => ['create', 'read', 'update'],
                    'orders' => ['create', 'read', 'update']
                ],
                'status' => 'active',
                'users_count' => 12
            ],
            [
                'name' => 'Viewer',
                'description' => 'Read-only access to view records and reports',
                'accessible_modules' => ['dashboard', 'analytics', 'land-owners', 'land-acquisition', 'customer', 'reports'],
                'module_permissions' => [
                    'dashboard' => ['read'],
                    'analytics' => ['read'],
                    'land-owners' => ['read'],
                    'land-acquisition' => ['read'],
                    'customer' => ['read'],
                    'reports' => ['read']
                ],
                'status' => 'active',
                'users_count' => 25
            ],
            [
                'name' => 'Guest',
                'description' => 'Limited read access to basic information',
                'accessible_modules' => ['dashboard'],
                'module_permissions' => [
                    'dashboard' => ['read']
                ],
                'status' => 'inactive',
                'users_count' => 3
            ]
        ];

        foreach ($roles as $roleData) {
            Role::updateOrCreate(
                ['name' => $roleData['name']],
                $roleData
            );
        }
    }
}

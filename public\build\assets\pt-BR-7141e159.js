import e from"./pt-8e78a30d.js";const r={...e,common:{...e.common,buttons:{...e.common.buttons,delete:"Excluir",cancel:"Cancelar",search:"Pesquisar"},messages:{...e.common.messages,confirmDelete:"Tem certeza que deseja excluir este item?",deleted:"Item excluído com sucesso"},navigation:{...e.common.navigation,landOwners:"Proprietários de Terra",landAcquisition:"Aquisição de Terras",customers:"Clientes",orders:"Pedidos"},forms:{...e.common.forms,zipCode:"CEP",state:"Estado"}},landOwners:{...e.landOwners,fields:{...e.landOwners.fields,zipCode:"CEP",state:"Estado",nationalId:"CPF",nidNumber:"Número do CPF"},messages:{...e.landOwners.messages,confirmDelete:"Tem certeza que deseja excluir este proprietário?",deleteSuccess:"Proprietário excluído com sucesso",createSuccess:"Proprietário criado com sucesso",updateSuccess:"Proprietário atualizado com sucesso",loadError:"Falha ao carregar proprietários. Tente novamente.",deleteError:"Falha ao excluir proprietário. Tente novamente.",createError:"Falha ao criar proprietário. Tente novamente.",updateError:"Falha ao atualizar proprietário. Tente novamente."}},date:{...e.date,formats:{short:"DD/MM/YYYY",medium:"DD/MM/YYYY",long:"DD de MMMM de YYYY",full:"dddd, DD de MMMM de YYYY"}},currency:{...e.currency,fields:{...e.currency.fields,symbol:"Símbolo (R$)"}},country:{...e.country,fields:{...e.country.fields,phoneCode:"Código de Área",state:"Estado"}}};export{r as default};

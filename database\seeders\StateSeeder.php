<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\State;
use App\Models\Country;
use Illuminate\Support\Facades\DB;

class StateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get countries for reference
        $usa = Country::where('code', 'US')->first();
        $canada = Country::where('code', 'CA')->first();
        $australia = Country::where('code', 'AU')->first();
        $india = Country::where('code', 'IN')->first();
        $uk = Country::where('code', 'GB')->first();
        $philippines = Country::where('code', 'PH')->first();

        if (!$usa || !$canada || !$australia || !$india || !$uk || !$philippines) {
            $this->command->warn('Required countries not found. Please run CountrySeeder first.');
            return;
        }

        // Sample states for testing - using actual country IDs
        $states = [
            // United States
            [
                'name' => 'California',
                'code' => 'CA',
                'type' => 'state',
                'country_id' => $usa->id,
                'capital' => 'Sacramento',
                'timezone' => 'America/Los_Angeles',
                'latitude' => 36.7783,
                'longitude' => -119.4179,
                'description' => 'The Golden State',
                'status' => 'active'
            ],
            [
                'name' => 'Texas',
                'code' => 'TX',
                'type' => 'state',
                'country_id' => $usa->id,
                'capital' => 'Austin',
                'timezone' => 'America/Chicago',
                'latitude' => 31.9686,
                'longitude' => -99.9018,
                'description' => 'The Lone Star State',
                'status' => 'active'
            ],
            [
                'name' => 'New York',
                'code' => 'NY',
                'type' => 'state',
                'country_id' => $usa->id,
                'capital' => 'Albany',
                'timezone' => 'America/New_York',
                'latitude' => 43.2994,
                'longitude' => -74.2179,
                'description' => 'The Empire State',
                'status' => 'active'
            ],
            [
                'name' => 'Florida',
                'code' => 'FL',
                'type' => 'state',
                'country_id' => $usa->id,
                'capital' => 'Tallahassee',
                'timezone' => 'America/New_York',
                'latitude' => 27.7663,
                'longitude' => -81.6868,
                'description' => 'The Sunshine State',
                'status' => 'active'
            ],
            // Canada
            [
                'name' => 'Ontario',
                'code' => 'ON',
                'type' => 'province',
                'country_id' => $canada->id,
                'capital' => 'Toronto',
                'timezone' => 'America/Toronto',
                'latitude' => 51.2538,
                'longitude' => -85.3232,
                'description' => 'Canada\'s most populous province',
                'status' => 'active'
            ],
            [
                'name' => 'Quebec',
                'code' => 'QC',
                'type' => 'province',
                'country_id' => $canada->id,
                'capital' => 'Quebec City',
                'timezone' => 'America/Toronto',
                'latitude' => 52.9399,
                'longitude' => -73.5491,
                'description' => 'Predominantly French-speaking province',
                'status' => 'active'
            ],
            [
                'name' => 'British Columbia',
                'code' => 'BC',
                'type' => 'province',
                'country_id' => $canada->id,
                'capital' => 'Victoria',
                'timezone' => 'America/Vancouver',
                'latitude' => 53.7267,
                'longitude' => -127.6476,
                'description' => 'Beautiful British Columbia',
                'status' => 'active'
            ],
            // Australia
            [
                'name' => 'New South Wales',
                'code' => 'NSW',
                'type' => 'state',
                'country_id' => $australia->id,
                'capital' => 'Sydney',
                'timezone' => 'Australia/Sydney',
                'latitude' => -31.2532,
                'longitude' => 146.9211,
                'description' => 'The Premier State',
                'status' => 'active'
            ],
            [
                'name' => 'Victoria',
                'code' => 'VIC',
                'type' => 'state',
                'country_id' => $australia->id,
                'capital' => 'Melbourne',
                'timezone' => 'Australia/Melbourne',
                'latitude' => -37.4713,
                'longitude' => 144.7852,
                'description' => 'The Garden State',
                'status' => 'active'
            ],
            [
                'name' => 'Queensland',
                'code' => 'QLD',
                'type' => 'state',
                'country_id' => $australia->id,
                'capital' => 'Brisbane',
                'timezone' => 'Australia/Brisbane',
                'latitude' => -20.9176,
                'longitude' => 142.7028,
                'description' => 'The Sunshine State',
                'status' => 'active'
            ],
            // India
            [
                'name' => 'Maharashtra',
                'code' => 'MH',
                'type' => 'state',
                'country_id' => $india->id,
                'capital' => 'Mumbai',
                'timezone' => 'Asia/Kolkata',
                'latitude' => 19.7515,
                'longitude' => 75.7139,
                'description' => 'Financial capital state of India',
                'status' => 'active'
            ],
            [
                'name' => 'Karnataka',
                'code' => 'KA',
                'type' => 'state',
                'country_id' => $india->id,
                'capital' => 'Bangalore',
                'timezone' => 'Asia/Kolkata',
                'latitude' => 15.3173,
                'longitude' => 75.7139,
                'description' => 'Silicon Valley of India',
                'status' => 'active'
            ],
            // United Kingdom
            [
                'name' => 'England',
                'code' => 'ENG',
                'type' => 'region',
                'country_id' => $uk->id,
                'capital' => 'London',
                'timezone' => 'Europe/London',
                'latitude' => 52.3555,
                'longitude' => -1.1743,
                'description' => 'Largest country in the United Kingdom',
                'status' => 'active'
            ],
            [
                'name' => 'Scotland',
                'code' => 'SCT',
                'type' => 'region',
                'country_id' => $uk->id,
                'capital' => 'Edinburgh',
                'timezone' => 'Europe/London',
                'latitude' => 56.4907,
                'longitude' => -4.2026,
                'description' => 'Land of the brave',
                'status' => 'active'
            ],
            // Philippines
            [
                'name' => 'Metro Manila',
                'code' => 'NCR',
                'type' => 'region',
                'country_id' => $philippines->id,
                'capital' => 'Manila',
                'timezone' => 'Asia/Manila',
                'latitude' => 14.6042,
                'longitude' => 121.0000,
                'description' => 'National Capital Region of the Philippines',
                'status' => 'active'
            ],
        ];

        foreach ($states as $state) {
            // Check if state already exists
            $existingState = State::where('name', $state['name'])
                ->where('country_id', $state['country_id'])
                ->first();
            
            if (!$existingState) {
                State::create($state);
                $this->command->info("Created state: {$state['name']}");
            } else {
                $this->command->info("State already exists: {$state['name']}");
            }
        }

        $this->command->info('State seeding completed!');
    }
}

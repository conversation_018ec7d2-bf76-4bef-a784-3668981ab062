import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from '../TranslationProvider';
import LanguageSwitcher from '@/components/ui/LanguageSwitcher';
import {
  Home,
  BarChart3,
  Users,
  Settings,
  FileText,
  X,
  Shield,
  ShoppingCart,
  UserCheck,
  Layers,
  Bot,
  RotateCcw,
  Building,
  Globe,
  Languages,
  DollarSign
} from 'lucide-react';

const Sidebar = ({ onClose, currentPage, onPageChange }) => {
  const { hasModuleAccess, loading, user, role } = useAuth();
  const { t } = useTranslation();

  // All available navigation items with their module keys and translation keys
  // Only include items that have corresponding controllers and models
  const allNavigationItems = [
    { nameKey: 'common.navigation.dashboard', page: 'dashboard', icon: Home, moduleKey: 'dashboard' },
    { nameKey: 'common.navigation.landOwners', page: 'land-owners', icon: Users, moduleKey: 'land-owners' },
    { nameKey: 'common.navigation.landAcquisition', page: 'land-acquisition', icon: Building, moduleKey: 'land-acquisition' },
    { nameKey: 'common.navigation.country', page: 'country', icon: Globe, moduleKey: 'country' },
    { nameKey: 'common.navigation.language', page: 'language', icon: Languages, moduleKey: 'language' },
    { nameKey: 'common.navigation.currency', page: 'currency', icon: DollarSign, moduleKey: 'currency' },
    { nameKey: 'common.navigation.roleManagement', page: 'role', icon: Shield, moduleKey: 'role' },
    { nameKey: 'common.navigation.settings', page: 'settings', icon: Settings, moduleKey: 'settings' },
  ];

  if (loading) {
    return (
      <div className="flex h-full flex-col bg-background border-r">
        <div className="flex h-16 items-center px-6 border-b">
          <div className="animate-pulse bg-gray-200 h-6 w-32 rounded"></div>
        </div>
        <div className="flex-1 p-4 space-y-2">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="animate-pulse bg-gray-200 h-10 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  // Filter navigation items based on user's accessible modules
  const accessibleNavItems = allNavigationItems.filter(item => 
    hasModuleAccess(item.moduleKey)
  );

  // Group items into main and secondary navigation
  const mainNavigation = accessibleNavItems.filter(item => 
    ['dashboard', 'land-owners', 'land-acquisition'].includes(item.moduleKey)
  );

  const documentsNavigation = accessibleNavItems.filter(item => 
    ['role', 'settings'].includes(item.moduleKey)
  );

  const systemNavigation = accessibleNavItems.filter(item => 
    ['country', 'language', 'currency'].includes(item.moduleKey)
  );

  return (
    <div className="flex h-full flex-col bg-background border-r">
      {/* Company Logo */}
      <div className="flex h-16 items-center px-6 border-b">
        <div className="flex items-center">
          <div className="flex h-6 w-6 items-center justify-center rounded-full bg-foreground">
            <span className="text-xs font-bold text-background">R</span>
          </div>
          <span className="ml-2 text-sm font-semibold">Real Estate</span>
        </div>
        
        {/* Close button for mobile */}
        <Button
          variant="ghost"
          size="icon"
          className="ml-auto lg:hidden"
          onClick={onClose}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* User Info */}
      <div className="px-6 py-4 border-b bg-muted/50">
        <div className="flex items-center space-x-2">
          <div className="h-8 w-8 rounded-full bg-blue-600 flex items-center justify-center">
            <span className="text-xs font-medium text-white">
              {user?.name?.charAt(0) || 'U'}
            </span>
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium truncate">{user?.name || 'User'}</p>
            <p className="text-xs text-muted-foreground truncate">{role?.name || 'No Role'}</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-4 py-4 space-y-1">
        {/* Main Navigation */}
        {mainNavigation.map((item) => {
          const Icon = item.icon;
          const isActive = currentPage === item.page;
          return (
            <button
              key={item.nameKey}
              onClick={() => {
                onPageChange(item.page);
                onClose();
              }}
              className={cn(
                "w-full flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors text-left",
                isActive
                  ? "bg-accent text-accent-foreground"
                  : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
              )}
            >
              <Icon className="h-4 w-4 flex-shrink-0" />
              {t(item.nameKey)}
            </button>
          );
        })}

        {/* Documents Section */}
        {/* Documents & Admin Section */}
        {documentsNavigation.length > 0 && (
          <div className="pt-6">
            <div className="px-3 mb-2">
              <h3 className="text-xs font-medium text-muted-foreground">
                {t('common.navigation.documentsAdmin', 'Documents & Admin')}
              </h3>
            </div>
            {documentsNavigation.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.page;
              return (
                <button
                  key={item.nameKey}
                  onClick={() => {
                    onPageChange(item.page);
                    onClose();
                  }}
                  className={cn(
                    "w-full flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors text-left",
                    isActive
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  <Icon className="h-4 w-4 flex-shrink-0" />
                  {t(item.nameKey)}
                </button>
              );
            })}
          </div>
        )}

        {/* System Configuration Section */}
        {systemNavigation.length > 0 && (
          <div className="pt-6">
            <div className="px-3 mb-2">
              <h3 className="text-xs font-medium text-muted-foreground">
                {t('common.navigation.systemConfiguration', 'System Configuration')}
              </h3>
            </div>
            {systemNavigation.map((item) => {
              const Icon = item.icon;
              const isActive = currentPage === item.page;
              return (
                <button
                  key={item.nameKey}
                  onClick={() => {
                    onPageChange(item.page);
                    onClose();
                  }}
                  className={cn(
                    "w-full flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors text-left",
                    isActive
                      ? "bg-accent text-accent-foreground"
                      : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  <Icon className="h-4 w-4 flex-shrink-0" />
                  {t(item.nameKey)}
                </button>
              );
            })}
          </div>
        )}
      </nav>
      
      {/* Language Switcher at bottom */}
      <div className="px-4 py-4 border-t">
        <LanguageSwitcher variant="dropdown" size="sm" />
      </div>
    </div>
  );
};

export default Sidebar;

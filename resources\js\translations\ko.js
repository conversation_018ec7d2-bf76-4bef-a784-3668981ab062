export default {
  // Common UI elements
  common: {
    buttons: {
      add: '추가',
      edit: '편집',
      delete: '삭제',
      save: '저장',
      cancel: '취소',
      confirm: '확인',
      close: '닫기',
      search: '검색',
      filter: '필터',
      export: '내보내기',
      import: '가져오기',
      refresh: '새로고침',
      loading: '로딩 중...',
      submit: '제출',
      reset: '재설정',
      clear: '지우기',
      view: '보기',
      download: '다운로드',
      upload: '업로드'
    },
    status: {
      active: '활성',
      inactive: '비활성',
      pending: '대기 중',
      approved: '승인됨',
      rejected: '거부됨',
      completed: '완료됨',
      draft: '초안',
      published: '게시됨'
    },
    messages: {
      success: '작업이 성공적으로 완료되었습니다',
      error: '오류가 발생했습니다',
      warning: '경고',
      info: '정보',
      loading: '로딩 중입니다. 잠시만 기다려주세요...',
      noData: '사용 가능한 데이터가 없습니다',
      confirmDelete: '이 항목을 삭제하시겠습니까?',
      confirmAction: '이 작업을 수행하시겠습니까?',
      saved: '변경 사항이 성공적으로 저장되었습니다',
      deleted: '항목이 성공적으로 삭제되었습니다',
      updated: '항목이 성공적으로 업데이트되었습니다',
      created: '항목이 성공적으로 생성되었습니다'
    },
    navigation: {
      dashboard: '대시보드',
      analytics: '분석',
      landOwners: '토지 소유자',
      landAcquisition: '토지 취득',
      lifecycle: '수명주기',
      country: '국가',
      language: '언어',
      currency: '통화',
      customers: '고객',
      orders: '주문',
      components: '구성요소',
      reports: '보고서',
      roleManagement: '역할 관리',
      wordAssistant: '워드 어시스턴트',
      settings: '설정',
      documentsAdmin: '문서 및 관리',
      landDevelopment: '토지 및 개발',
      hrManagement: '인사 관리',
      developmentCosting: '주요 개발 비용'
    },
    forms: {
      required: '이 필드는 필수입니다',
      invalidEmail: '유효한 이메일 주소를 입력하세요',
      invalidPhone: '유효한 전화번호를 입력하세요',
      passwordMismatch: '비밀번호가 일치하지 않습니다',
      minLength: '최소 길이는 {min}자입니다',
      maxLength: '최대 길이는 {max}자입니다',
      selectOption: '옵션을 선택하세요',
      invalidDate: '유효한 날짜를 입력하세요',
      invalidNumber: '유효한 숫자를 입력하세요'
    },
    table: {
      name: '이름',
      code: '코드',
      status: '상태',
      actions: '작업',
      createdAt: '생성일',
      updatedAt: '수정일',
      noRecords: '레코드를 찾을 수 없습니다',
      showing: '{total}개 중 {start}부터 {end}까지 표시',
      previous: '이전',
      next: '다음',
      rowsPerPage: '페이지당 행 수'
    },
    auditLog: '감사 로그',
    loading: '로딩 중...',
    noData: '사용 가능한 데이터가 없습니다'
  },

  // Dashboard specific
  dashboard: {
    title: '대시보드',
    description: '오늘 비즈니스 현황을 확인하세요.',
    welcome: '환영합니다, {name}님!',
    overview: '개요',
    statistics: '통계',
    recentActivity: '최근 활동',
    quickActions: '빠른 작업',
    viewReport: '보고서 보기',
    statistics: {
      totalRevenue: '총 수익',
      subscriptions: '구독',
      sales: '판매',
      activeNow: '현재 활성',
      fromLastMonth: '지난 달부터',
      fromLastHour: '지난 시간부터'
    }
  },

  // Language Management
  language: {
    title: '언어 관리',
    addLanguage: '언어 추가',
    editLanguage: '언어 편집',
    default: '기본',
    current: '현재',
    select: '선택',
    fields: {
      name: '이름',
      code: '코드',
      nativeName: '원어 이름',
      flag: '플래그 (이모지)',
      direction: '방향',
      status: '상태',
      isDefault: '기본 언어로 설정'
    },
    direction: {
      ltr: '왼쪽에서 오른쪽 (LTR)',
      rtl: '오른쪽에서 왼쪽 (RTL)'
    },
    statistics: {
      totalLanguages: '전체 언어',
      activeLanguages: '활성 언어',
      defaultLanguage: '기본 언어',
      ltrLanguages: 'LTR 언어',
      rtlLanguages: 'RTL 언어'
    },
    messages: {
      setDefault: '기본 언어로 설정하시겠습니까?',
      setDefaultText: '이 언어를 시스템의 기본 언어로 설정합니다',
      setDefaultConfirm: '네, 기본으로 설정합니다!',
      defaultUpdated: '기본 언어가 성공적으로 업데이트되었습니다',
      onlyActiveCanBeDefault: '활성 언어만 기본으로 설정할 수 있습니다'
    }
  },

  // Country Management
  country: {
    title: '국가 관리',
    addCountry: '국가 추가',
    editCountry: '국가 편집',
    fields: {
      name: '이름',
      code: '코드',
      flag: '플래그',
      status: '상태'
    },
    statistics: {
      totalCountries: '전체 국가',
      activeCountries: '활성 국가'
    }
  },

  // Currency Management
  currency: {
    title: '통화 관리',
    addCurrency: '통화 추가',
    editCurrency: '통화 편집',
    fields: {
      name: '이름',
      code: '코드',
      symbol: '기호',
      exchangeRate: '환율',
      status: '상태'
    },
    statistics: {
      totalCurrencies: '전체 통화',
      activeCurrencies: '활성 통화'
    }
  },

  // Land Owners
  landOwners: {
    title: '토지 소유자',
    addLandOwner: '토지 소유자 추가',
    editLandOwner: '토지 소유자 편집',
    fields: {
      name: '이름',
      email: '이메일',
      phone: '전화',
      address: '주소',
      status: '상태'
    },
    statistics: {
      totalLandOwners: '전체 토지 소유자',
      activeLandOwners: '활성 토지 소유자'
    }
  },

  // Land Acquisition
  landAcquisition: {
    title: '토지 취득',
    addAcquisition: '취득 추가',
    editAcquisition: '취득 편집',
    fields: {
      title: '제목',
      description: '설명',
      area: '면적',
      price: '가격',
      status: '상태',
      location: '위치'
    },
    statistics: {
      totalAcquisitions: '전체 취득',
      activeAcquisitions: '활성 취득'
    }
  },

  // Analytics
  analytics: {
    title: '분석',
    overview: '개요',
    performance: '성능',
    trends: '트렌드',
    reports: '보고서'
  },

  // Settings
  settings: {
    title: '설정',
    general: '일반',
    security: '보안',
    notifications: '알림',
    preferences: '환경설정'
  },

  // Role Management
  role: {
    title: '역할 관리',
    addRole: '역할 추가',
    editRole: '역할 편집',
    fields: {
      name: '이름',
      description: '설명',
      permissions: '권한',
      status: '상태'
    },
    statistics: {
      totalRoles: '전체 역할',
      activeRoles: '활성 역할'
    }
  }
};

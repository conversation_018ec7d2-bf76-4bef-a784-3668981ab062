import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import { showAlert } from '../../utils/sweetAlert';
import { useTranslation } from '../../hooks/useTranslation';
import {
    ContentModal,
    ContentModalContent,
    ContentModalHeader,
    ContentModalTitle,
    ContentModalOverlay,
} from './content-modal';
import { 
    Plus, 
    Edit, 
    Trash2, 
    X,
    Save,
    Building,
    Square,
    DollarSign,
    Tag,
    Bed,
    Bath
} from 'lucide-react';

const UnitTypesModal = ({ isOpen, onClose, project }) => {
    const { t } = useTranslation();
    const [unitTypes, setUnitTypes] = useState([]);
    const [loading, setLoading] = useState(false);
    const [submitting, setSubmitting] = useState(false);
    const [showForm, setShowForm] = useState(false);
    const [editingType, setEditingType] = useState(null);
    const [formData, setFormData] = useState({
        project_id: project?.id || '',
        type_name: '',
        total_size_sqft: '',
        flat_size: '',
        total_bed_room: '',
        total_bathroom: '',
        description: '',
        base_price: '',
        is_active: true,
        auto_generate_units: true
    });

    useEffect(() => {
        if (isOpen && project) {
            fetchUnitTypes();
            setFormData(prev => ({ ...prev, project_id: project.id }));
        }
    }, [isOpen, project]);

    const fetchUnitTypes = async () => {
        try {
            setLoading(true);
            const response = await fetch(`/api/unit-types?project_id=${project.id}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                    'Accept': 'application/json',
                }
            });

            const data = await response.json();
            if (data.status === 'success') {
                setUnitTypes(data.data);
            } else {
                showAlert.error(t('common.error'), t('unitTypes.fetchError'));
            }
        } catch (error) {
            console.error('Error fetching unit types:', error);
            showAlert.error(t('common.error'), t('unitTypes.fetchError'));
        } finally {
            setLoading(false);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (submitting) return; // Prevent double submission
        
        try {
            setSubmitting(true);
            const url = editingType 
                ? `/api/unit-types/${editingType.id}`
                : '/api/unit-types';
            
            const method = editingType ? 'PUT' : 'POST';

            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                    'Accept': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();
            
            if (data.status === 'success') {
                // Show success message with shorter duration for smoother flow
                showAlert.success(t('common.success'), data.message, 1500);
                
                // Reset form immediately for instant feedback
                resetForm();
                
                // Refresh the unit types list
                await fetchUnitTypes();
                
                // Close form with smooth transition
                setTimeout(() => {
                    setShowForm(false);
                }, 200);
                
            } else {
                showAlert.error(t('common.error'), data.message || t('unitTypes.saveError'));
            }
        } catch (error) {
            console.error('Error saving unit type:', error);
            showAlert.error(t('common.error'), t('unitTypes.saveError'));
        } finally {
            setSubmitting(false);
        }
    };

    const handleEdit = (unitType) => {
        setEditingType(unitType);
        setFormData({
            project_id: unitType.project_id,
            type_name: unitType.type_name || '',
            total_size_sqft: unitType.total_size_sqft || '',
            flat_size: unitType.flat_size || '',
            total_bed_room: unitType.total_bed_room || '',
            total_bathroom: unitType.total_bathroom || '',
            description: unitType.description || '',
            base_price: unitType.base_price || '',
            is_active: unitType.is_active !== undefined ? unitType.is_active : true,
            auto_generate_units: false // Don't auto-generate when editing
        });
        setShowForm(true);
    };

    const handleDelete = async (unitType) => {
        const result = await Swal.fire({
            title: 'Are you sure?',
            text: `Delete unit type "${unitType.type_name}"? This will also delete all associated unit details. This action cannot be undone.`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!',
            customClass: {
                popup: 'swal2-popup-custom',
                title: 'swal2-title-custom',
                content: 'swal2-content-custom',
                confirmButton: 'swal2-confirm-custom',
                cancelButton: 'swal2-cancel-custom'
            }
        });

        if (result.isConfirmed) {
            try {
                const response = await fetch(`/api/unit-types/${unitType.id}`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
                        'Accept': 'application/json',
                    }
                });

                const data = await response.json();
                
                if (data.status === 'success') {
                    showAlert.success(t('common.deleted'), data.message);
                    fetchUnitTypes();
                } else {
                    showAlert.error(t('common.error'), data.message || t('unitTypes.deleteError'));
                }
            } catch (error) {
                console.error('Error deleting unit type:', error);
                showAlert.error('Error', 'Failed to delete unit type');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            project_id: project?.id || '',
            type_name: '',
            total_size_sqft: '',
            flat_size: '',
            total_bed_room: '',
            total_bathroom: '',
            description: '',
            base_price: '',
            is_active: true,
            auto_generate_units: true
        });
        setEditingType(null);
    };

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    if (!isOpen) return null;

    return (
        <ContentModal open={isOpen} onOpenChange={(open) => !open && onClose()}>
            <ContentModalOverlay />
            <ContentModalContent className="w-full max-w-5xl max-h-[90vh] overflow-hidden animate-slideIn">
                {/* Header */}
                <ContentModalHeader className="p-6 border-b">
                    <div className="flex items-center gap-3">
                        <Tag className="w-6 h-6 text-blue-600" />
                        <div>
                            <ContentModalTitle className="text-xl font-semibold text-gray-900">
                                Unit Types - {project?.project_name}
                            </ContentModalTitle>
                            <p className="text-sm text-gray-500">
                                Manage predefined unit types for this project
                            </p>
                        </div>
                    </div>
                </ContentModalHeader>

                <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                    {/* Action Buttons */}
                    <div className="flex justify-between items-center mb-6">
                        <h3 className="text-lg font-medium text-gray-900">
                            Unit Types ({unitTypes.length})
                        </h3>
                        <button
                            onClick={() => {
                                resetForm();
                                setShowForm(!showForm);
                                // Smooth scroll to form when opening
                                if (!showForm) {
                                    setTimeout(() => {
                                        const formElement = document.querySelector('.unit-type-form');
                                        if (formElement) {
                                            formElement.scrollIntoView({ 
                                                behavior: 'smooth', 
                                                block: 'nearest' 
                                            });
                                        }
                                    }, 100);
                                }
                            }}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
                        >
                            <Plus className="w-4 h-4" />
                            Add Unit Type
                        </button>
                    </div>

                    {/* Add/Edit Form */}
                    {showForm && (
                        <div className={`unit-type-form bg-gray-50 rounded-lg p-6 mb-6 form-enter ${submitting ? 'opacity-75 pointer-events-none' : ''}`}>
                            <h4 className="text-lg font-medium text-gray-900 mb-4">
                                {editingType ? 'Edit Unit Type' : 'Add New Unit Type'}
                            </h4>
                            <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Type Name *
                                    </label>
                                    <input
                                        type="text"
                                        name="type_name"
                                        value={formData.type_name}
                                        onChange={handleInputChange}
                                        required
                                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="e.g., Studio, 1BR, 2BR, Penthouse"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Total Size (sq ft) *
                                    </label>
                                    <input
                                        type="number"
                                        name="total_size_sqft"
                                        value={formData.total_size_sqft}
                                        onChange={handleInputChange}
                                        required
                                        min="0"
                                        step="0.01"
                                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="e.g., 850"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Flat Size (sq ft)
                                    </label>
                                    <input
                                        type="number"
                                        name="flat_size"
                                        value={formData.flat_size}
                                        onChange={handleInputChange}
                                        min="0"
                                        step="0.01"
                                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="e.g., 750"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Total Bedrooms
                                    </label>
                                    <input
                                        type="number"
                                        name="total_bed_room"
                                        value={formData.total_bed_room}
                                        onChange={handleInputChange}
                                        min="0"
                                        max="20"
                                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="e.g., 2"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Total Bathrooms
                                    </label>
                                    <input
                                        type="number"
                                        name="total_bathroom"
                                        value={formData.total_bathroom}
                                        onChange={handleInputChange}
                                        min="0"
                                        max="20"
                                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="e.g., 2"
                                    />
                                </div>

                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Base Price
                                    </label>
                                    <input
                                        type="number"
                                        name="base_price"
                                        value={formData.base_price}
                                        onChange={handleInputChange}
                                        min="0"
                                        step="0.01"
                                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Base price for this type"
                                    />
                                </div>

                                <div className="flex items-center">
                                    <input
                                        type="checkbox"
                                        name="is_active"
                                        checked={formData.is_active}
                                        onChange={handleInputChange}
                                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                    />
                                    <label className="ml-2 text-sm text-gray-700">Active</label>
                                </div>

                                {!editingType && (
                                    <div className="flex items-center">
                                        <input
                                            type="checkbox"
                                            name="auto_generate_units"
                                            checked={formData.auto_generate_units}
                                            onChange={handleInputChange}
                                            className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                                        />
                                        <label className="ml-2 text-sm text-gray-700">
                                            Auto-generate unit details
                                            <span className="block text-xs text-gray-500">
                                                Creates units for all floors automatically
                                            </span>
                                        </label>
                                    </div>
                                )}

                                <div className="col-span-full">
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Description
                                    </label>
                                    <textarea
                                        name="description"
                                        value={formData.description}
                                        onChange={handleInputChange}
                                        rows="3"
                                        className="w-full p-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Additional details about this unit type..."
                                    />
                                </div>

                                <div className="col-span-full flex gap-3 pt-4">
                                    <button
                                        type="submit"
                                        disabled={submitting}
                                        className={`px-6 py-2 rounded-lg flex items-center gap-2 transition-colors ${
                                            submitting 
                                                ? 'bg-blue-400 cursor-not-allowed' 
                                                : 'bg-blue-600 hover:bg-blue-700'
                                        } text-white`}
                                    >
                                        {submitting ? (
                                            <>
                                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                                {editingType ? 'Updating...' : 'Adding...'}
                                            </>
                                        ) : (
                                            <>
                                                <Save className="w-4 h-4" />
                                                {editingType ? 'Update Unit Type' : 'Add Unit Type'}
                                            </>
                                        )}
                                    </button>
                                    <button
                                        type="button"
                                        onClick={() => {
                                            setShowForm(false);
                                            resetForm();
                                        }}
                                        className="bg-gray-500 hover:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors"
                                    >
                                        Cancel
                                    </button>
                                </div>
                            </form>
                        </div>
                    )}

                    {/* Unit Types List */}
                    {loading ? (
                        <div className="text-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                            <p className="text-gray-500 mt-2">Loading unit types...</p>
                        </div>
                    ) : unitTypes.length === 0 ? (
                        <div className="text-center py-8">
                            <Tag className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                            <p className="text-gray-500">No unit types found for this project</p>
                            <p className="text-sm text-gray-400">Click "Add Unit Type" to get started</p>
                        </div>
                    ) : (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {unitTypes.map((unitType, index) => (
                                <div 
                                    key={unitType.id} 
                                    className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200 animate-slideIn"
                                    style={{ animationDelay: `${index * 50}ms` }}
                                >
                                    <div className="flex justify-between items-start mb-3">
                                        <h4 className="text-lg font-medium text-gray-900">{unitType.type_name}</h4>
                                        <div className="flex items-center gap-2">
                                            <button
                                                onClick={() => handleEdit(unitType)}
                                                className="text-blue-600 hover:text-blue-900"
                                                title="Edit Unit Type"
                                            >
                                                <Edit className="w-4 h-4" />
                                            </button>
                                            <button
                                                onClick={() => handleDelete(unitType)}
                                                className="text-red-600 hover:text-red-900"
                                                title="Delete Unit Type"
                                            >
                                                <Trash2 className="w-4 h-4" />
                                            </button>
                                        </div>
                                    </div>

                                    <div className="space-y-2 text-sm text-gray-600">
                                        <div className="flex items-center gap-2">
                                            <Square className="w-4 h-4 text-blue-500" />
                                            <span>{parseFloat(unitType.total_size_sqft).toLocaleString()} sq ft</span>
                                        </div>

                                        {unitType.flat_size && (
                                            <div className="flex items-center gap-2">
                                                <Building className="w-4 h-4 text-purple-500" />
                                                <span>Flat: {parseFloat(unitType.flat_size).toLocaleString()} sq ft</span>
                                            </div>
                                        )}

                                        <div className="flex items-center gap-4">
                                            {unitType.total_bed_room > 0 && (
                                                <div className="flex items-center gap-1">
                                                    <Bed className="w-4 h-4 text-indigo-500" />
                                                    <span>{unitType.total_bed_room} BR</span>
                                                </div>
                                            )}
                                            {unitType.total_bathroom > 0 && (
                                                <div className="flex items-center gap-1">
                                                    <Bath className="w-4 h-4 text-cyan-500" />
                                                    <span>{unitType.total_bathroom} BA</span>
                                                </div>
                                            )}
                                        </div>

                                        {unitType.base_price && (
                                            <div className="flex items-center gap-2">
                                                <DollarSign className="w-4 h-4 text-green-500" />
                                                <span className="font-medium">${parseFloat(unitType.base_price).toLocaleString()}</span>
                                            </div>
                                        )}

                                        {unitType.description && (
                                            <p className="text-xs text-gray-500 mt-2">{unitType.description}</p>
                                        )}
                                    </div>

                                    <div className="mt-3 pt-3 border-t border-gray-100">
                                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                            unitType.is_active 
                                                ? 'bg-green-100 text-green-800' 
                                                : 'bg-red-100 text-red-800'
                                        }`}>
                                            {unitType.is_active ? 'Active' : 'Inactive'}
                                        </span>
                                    </div>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </ContentModalContent>
        </ContentModal>
    );
};

export default UnitTypesModal;

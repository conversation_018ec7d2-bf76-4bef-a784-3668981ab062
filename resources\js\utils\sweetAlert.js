import Swal from 'sweetalert2';

/**
 * SweetAlert2 utility functions for consistent alerts across the application
 * All alerts now use elevated z-index values to ensure they appear above all content
 */

// Configure global defaults with high z-index
const defaultConfig = {
  heightAuto: false,
  customClass: {
    container: 'swal2-z-index-high',
    backdrop: 'swal2-backdrop-z-index-high'
  },
  didOpen: (popup) => {
    // Apply inline z-index as backup
    const container = popup.parentElement;
    if (container) {
      container.style.zIndex = '999999';
    }
    const backdrop = document.querySelector('.swal2-backdrop');
    if (backdrop) {
      backdrop.style.zIndex = '999998';
    }
    popup.style.zIndex = '999999';
  }
};

/**
 * Universal showAlert function for compatibility
 */
export const showAlert = (type, title, text, showConfirmButton = false) => {
  const alertTypes = {
    success: {
      icon: 'success',
      confirmButtonColor: '#10b981',
      timer: showConfirmButton ? 0 : 2000,
      showConfirmButton: showConfirmButton
    },
    error: {
      icon: 'error',
      confirmButtonColor: '#ef4444',
      showConfirmButton: true
    },
    warning: {
      icon: 'warning',
      confirmButtonColor: '#f59e0b',
      showConfirmButton: true,
      showCancelButton: showConfirmButton
    },
    info: {
      icon: 'info',
      confirmButtonColor: '#3b82f6',
      showConfirmButton: true
    }
  };

  const config = alertTypes[type] || alertTypes.info;
  
  return Swal.fire({
    ...defaultConfig,
    title,
    text,
    ...config
  });
};

export const showAlertMethods = {
  /**
   * Show success alert
   */
  success: (title, text, timer = 2000) => {
    return Swal.fire({
      ...defaultConfig,
      title,
      text,
      icon: 'success',
      timer,
      showConfirmButton: timer === 0,
      confirmButtonColor: '#10b981',
    });
  },

  /**
   * Show error alert
   */
  error: (title, text) => {
    return Swal.fire({
      ...defaultConfig,
      title,
      text,
      icon: 'error',
      confirmButtonText: 'OK',
      confirmButtonColor: '#ef4444',
    });
  },

  /**
   * Show warning alert
   */
  warning: (title, text) => {
    return Swal.fire({
      ...defaultConfig,
      title,
      text,
      icon: 'warning',
      confirmButtonText: 'OK',
      confirmButtonColor: '#f59e0b',
    });
  },

  /**
   * Show info alert
   */
  info: (title, text) => {
    return Swal.fire({
      ...defaultConfig,
      title,
      text,
      icon: 'info',
      confirmButtonText: 'OK',
      confirmButtonColor: '#3b82f6',
    });
  },

  /**
   * Show confirmation dialog
   */
  confirm: (title, text, confirmText = 'Yes, do it!', cancelText = 'Cancel') => {
    return Swal.fire({
      ...defaultConfig,
      title,
      text,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: confirmText,
      cancelButtonText: cancelText
    });
  },

  /**
   * Show delete confirmation dialog
   */
  confirmDelete: (itemName = 'item') => {
    return Swal.fire({
      ...defaultConfig,
      title: 'Are you sure?',
      text: `You won't be able to revert this action! This ${itemName} will be permanently deleted.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel'
    });
  },

  /**
   * Show loading alert
   */
  loading: (title, text) => {
    return Swal.fire({
      ...defaultConfig,
      title,
      text,
      allowOutsideClick: false,
      allowEscapeKey: false,
      allowEnterKey: false,
      showConfirmButton: false,
      didOpen: (popup) => {
        // Apply z-index from defaultConfig
        defaultConfig.didOpen(popup);
        Swal.showLoading();
      }
    });
  },

  /**
   * Close any open alert
   */
  close: () => {
    Swal.close();
  },

  /**
   * Show toast notification
   */
  toast: (text, icon = 'success', position = 'top-end') => {
    const Toast = Swal.mixin({
      ...defaultConfig,
      toast: true,
      position,
      showConfirmButton: false,
      timer: 3000,
      timerProgressBar: true,
      didOpen: (toast) => {
        // Apply z-index from defaultConfig
        defaultConfig.didOpen(toast);
        toast.addEventListener('mouseenter', Swal.stopTimer);
        toast.addEventListener('mouseleave', Swal.resumeTimer);
      }
    });

    return Toast.fire({
      icon,
      title: text
    });
  }
};

export default showAlert;

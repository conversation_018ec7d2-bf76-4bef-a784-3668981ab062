<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\LandOwners\Models\LandOwner;

class LandAcquisition extends Model
{
    use HasFactory;

    protected $fillable = [
        'dag_number', 'record_dag',          // Support both names
        'khatian_number', 'khatian',         // Support both names
        'mouza', 'mauza',                    // Support both names
        'land_size_decimal', 'land_size',    // Support both names
        'purchase_price', 'acquisition_price', // Support both names
        'landOwners_id',
    ];

    protected $casts = [
        'land_size_decimal' => 'decimal:2',  // Changed from 'land_size'
        'purchase_price' => 'decimal:2',     // Changed from 'acquisition_price'
        'landOwners_id' => 'integer',
    ];

    // Add mappings for frontend compatibility
    protected $appends = ['record_dag', 'khatian', 'mauza', 'land_size', 'acquisition_price'];

    /**
     * Accessor for frontend compatibility - record_dag
     */
    public function getRecordDagAttribute()
    {
        return $this->dag_number;
    }

    /**
     * Mutator for frontend compatibility - record_dag
     */
    public function setRecordDagAttribute($value)
    {
        $this->attributes['dag_number'] = $value;
    }

    /**
     * Accessor for frontend compatibility - khatian
     */
    public function getKhatianAttribute()
    {
        return $this->khatian_number;
    }

    /**
     * Mutator for frontend compatibility - khatian
     */
    public function setKhatianAttribute($value)
    {
        $this->attributes['khatian_number'] = $value;
    }

    /**
     * Accessor for frontend compatibility - mauza
     */
    public function getMauzaAttribute()
    {
        return $this->mouza;
    }

    /**
     * Mutator for frontend compatibility - mauza
     */
    public function setMauzaAttribute($value)
    {
        $this->attributes['mouza'] = $value;
    }

    /**
     * Accessor for frontend compatibility - land_size
     */
    public function getLandSizeAttribute()
    {
        return $this->land_size_decimal;
    }

    /**
     * Mutator for frontend compatibility - land_size
     */
    public function setLandSizeAttribute($value)
    {
        $this->attributes['land_size_decimal'] = $value;
    }

    /**
     * Accessor for frontend compatibility - acquisition_price
     */
    public function getAcquisitionPriceAttribute()
    {
        return $this->purchase_price;
    }

    /**
     * Mutator for frontend compatibility - acquisition_price
     */
    public function setAcquisitionPriceAttribute($value)
    {
        $this->attributes['purchase_price'] = $value;
    }

    /**
     * Get the formatted acquisition price
     */
    protected function formattedAcquisitionPrice(): Attribute
    {
        return Attribute::make(
            get: fn () => '৳' . number_format($this->purchase_price, 2)  // Changed from acquisition_price
        );
    }

    /**
     * Get the formatted land size
     */
    protected function formattedLandSize(): Attribute
    {
        return Attribute::make(
            get: fn () => number_format($this->land_size, 2) . ' decimal'
        );
    }

    /**
     * Relationship with LandOwner (assuming you have a LandOwner model)
     */
    public function landOwner(): BelongsTo
    {
        return $this->belongsTo(LandOwner::class, 'landOwners_id');
    }

    /**
     * Relationship with LandDocuments
     */
    public function landDocuments(): HasMany
    {
        return $this->hasMany(LandDocument::class);
    }

    /**
     * Relationship with LandAddress
     */
    public function landAddress(): HasOne
    {
        return $this->hasOne(LandAddress::class);
    }

    /**
     * Get the projects associated with this land acquisition
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class, 'land_id');
    }

    /**
     * Scope for filtering by mauza
     */
    public function scopeByMauza($query, $mauza)
    {
        return $query->where('mouza', 'like', "%{$mauza}%");  // Changed from 'mauza'
    }

    /**
     * Scope for filtering by khatian
     */
    public function scopeByKhatian($query, $khatian)
    {
        return $query->where('khatian_number', $khatian);  // Changed from 'khatian'
    }

    /**
     * Scope for filtering by record DAG
     */
    public function scopeByRecordDag($query, $recordDag)
    {
        return $query->where('dag_number', $recordDag);  // Changed from 'record_dag'
    }

    /**
     * Scope for filtering by price range
     */
    public function scopeByPriceRange($query, $min, $max)
    {
        return $query->whereBetween('purchase_price', [$min, $max]);  // Changed from 'acquisition_price'
    }

    /**
     * Scope for filtering by land size range
     */
    public function scopeByLandSizeRange($query, $min, $max)
    {
        return $query->whereBetween('land_size_decimal', [$min, $max]);  // Changed from 'land_size'
    }

    /**
     * Get total statistics
     */
    public static function getStatistics(): array
    {
        return [
            'total_records' => self::count(),
            'total_land_size' => self::sum('land_size_decimal'),     // Changed from 'land_size'
            'total_acquisition_value' => self::sum('purchase_price'), // Changed from 'acquisition_price'
            'average_price_per_decimal' => self::count() > 0 ? self::sum('purchase_price') / self::sum('land_size_decimal') : 0,
        ];
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('unit_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->string('unit_name')->nullable();
            $table->string('unit_type')->nullable(); // apartment, duplex, penthouse, etc.
            $table->integer('bedrooms')->default(0);
            $table->integer('bathrooms')->default(0);
            $table->decimal('unit_size_sqft', 10, 2)->nullable();
            $table->decimal('price', 15, 2)->nullable();
            $table->text('description')->nullable();
            $table->enum('status', ['available', 'sold', 'reserved', 'under_construction'])->default('available');
            $table->integer('floor_number')->nullable();
            $table->string('unit_number')->nullable();
            $table->boolean('has_balcony')->default(false);
            $table->boolean('has_parking')->default(false);
            $table->json('amenities')->nullable(); // JSON field for flexible amenities
            $table->timestamps();
            
            // Indexes for better performance
            $table->index(['project_id', 'status']);
            $table->index(['unit_type', 'bedrooms']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unit_details');
    }
};

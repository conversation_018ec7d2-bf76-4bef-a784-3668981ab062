import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  IdCard,
  CreditCard,
  Eye,
  FileText,
  Download,
  X
} from 'lucide-react';

const DocumentViewer = ({ 
  owner,
  onViewImage
}) => {
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);

  // Helper function to get full image URL
  const getImageUrl = (photoPath) => {
    if (!photoPath || typeof photoPath !== 'string') return null;
    
    // If it's already a full URL, return as is
    if (photoPath.startsWith('http://') || photoPath.startsWith('https://')) {
      return photoPath;
    }
    
    // If it starts with /landowners, use the base URL
    if (photoPath.startsWith('/landowners/')) {
      return `${window.location.protocol}//${window.location.host}${photoPath}`;
    }
    
    // If it starts with /storage, use the base URL
    if (photoPath.startsWith('/storage/')) {
      return `${window.location.protocol}//${window.location.host}${photoPath}`;
    }
    
    // If it's just a filename or relative path, assume it's in landowners/documents directory
    return `${window.location.protocol}//${window.location.host}/landowners/documents/${photoPath}`;
  };

  const handleDocumentClick = (documentType, documentUrl, title) => {
    const fullUrl = getImageUrl(documentUrl);
    if (fullUrl) {
      setSelectedDocument({
        type: documentType,
        url: fullUrl,
        title: title,
        ownerName: owner.name
      });
      setShowDocumentModal(true);
    }
  };

  const downloadDocument = (url, filename) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const hasDocuments = owner.nid_front || owner.nid_back || owner.passport_photo;

  if (!hasDocuments) {
    return (
      <div className="text-center">
        <Badge variant="secondary" className="text-xs">
          <FileText className="h-3 w-3 mr-1" />
          No Documents
        </Badge>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-2">
        {/* Document Type Badge */}
        {owner.document_type && (
          <div className="flex justify-center">
            <Badge variant="outline" className="text-xs">
              {owner.document_type === 'nid' ? (
                <>
                  <IdCard className="h-3 w-3 mr-1" />
                  NID Card
                </>
              ) : (
                <>
                  <CreditCard className="h-3 w-3 mr-1" />
                  Passport
                </>
              )}
            </Badge>
          </div>
        )}

        {/* Document Buttons */}
        <div className="flex flex-col gap-1">
          {owner.document_type === 'nid' && (
            <>
              {owner.nid_front && (
                <Button
                  variant="outline"
                  size="sm"
                  className="h-6 text-xs"
                  onClick={() => handleDocumentClick('nid_front', owner.nid_front, 'NID Front Side')}
                >
                  <Eye className="h-3 w-3 mr-1" />
                  Front
                </Button>
              )}
              {owner.nid_back && (
                <Button
                  variant="outline"
                  size="sm"
                  className="h-6 text-xs"
                  onClick={() => handleDocumentClick('nid_back', owner.nid_back, 'NID Back Side')}
                >
                  <Eye className="h-3 w-3 mr-1" />
                  Back
                </Button>
              )}
            </>
          )}

          {owner.document_type === 'passport' && owner.passport_photo && (
            <Button
              variant="outline"
              size="sm"
              className="h-6 text-xs"
              onClick={() => handleDocumentClick('passport', owner.passport_photo, 'Passport Page')}
            >
              <Eye className="h-3 w-3 mr-1" />
              View
            </Button>
          )}
        </div>
      </div>

      {/* Document Modal */}
      <Dialog open={showDocumentModal} onOpenChange={setShowDocumentModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {selectedDocument?.type === 'passport' ? (
                <CreditCard className="h-5 w-5" />
              ) : (
                <IdCard className="h-5 w-5" />
              )}
              {selectedDocument?.title}
            </DialogTitle>
            <DialogDescription>
              Document for {selectedDocument?.ownerName}
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex flex-col items-center space-y-4">
            {/* Document Image */}
            <div className="relative max-w-full max-h-[60vh] overflow-hidden rounded-lg border">
              <img
                src={selectedDocument?.url}
                alt={selectedDocument?.title}
                className="max-w-full max-h-full object-contain"
                onError={(e) => {
                  console.error('Failed to load document image:', selectedDocument?.url);
                  e.target.style.display = 'none';
                }}
              />
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => window.open(selectedDocument?.url, '_blank')}
              >
                <Eye className="h-4 w-4 mr-2" />
                Open Full Size
              </Button>
              <Button
                variant="outline"
                onClick={() => downloadDocument(
                  selectedDocument?.url, 
                  `${owner.name}_${selectedDocument?.title.replace(/\s+/g, '_')}.jpg`
                )}
              >
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowDocumentModal(false)}
              >
                <X className="h-4 w-4 mr-2" />
                Close
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default DocumentViewer;

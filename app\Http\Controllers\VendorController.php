<?php

namespace App\Http\Controllers;

use App\Models\Vendor;
use App\Models\VendorType;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

class VendorController extends Controller
{
    /**
     * Display a listing of vendors.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Vendor::with(['vendorType', 'creator', 'updater']);

            // Apply filters
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%")
                      ->orWhere('address', 'like', "%{$search}%")
                      ->orWhereHas('vendorType', function ($subQ) use ($search) {
                          $subQ->where('name', 'like', "%{$search}%");
                      });
                });
            }

            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('vendor_type_id')) {
                $query->where('vendor_type_id', $request->vendor_type_id);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 10);
            $vendors = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $vendors,
                'message' => 'Vendors retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving vendors: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created vendor.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'phone' => 'nullable|string|max:20',
                'email' => 'nullable|email|max:255',
                'vendor_type_id' => 'required|exists:vendor_types,id',
                'address' => 'nullable|string|max:1000',
                'status' => 'in:active,inactive'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                    'message' => 'Validation failed'
                ], 422);
            }

            DB::beginTransaction();

            $vendor = Vendor::create(array_merge(
                $validator->validated(),
                ['created_by' => Auth::id()]
            ));

            $vendor->load(['vendorType', 'creator']);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $vendor,
                'message' => 'Vendor created successfully'
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error creating vendor: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified vendor.
     */
    public function show(Vendor $vendor): JsonResponse
    {
        try {
            $vendor->load(['vendorType', 'creator', 'updater']);

            return response()->json([
                'success' => true,
                'data' => $vendor,
                'message' => 'Vendor retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving vendor: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified vendor.
     */
    public function update(Request $request, Vendor $vendor): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255',
                'phone' => 'nullable|string|max:20',
                'email' => 'nullable|email|max:255',
                'vendor_type_id' => 'required|exists:vendor_types,id',
                'address' => 'nullable|string|max:1000',
                'status' => 'in:active,inactive'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                    'message' => 'Validation failed'
                ], 422);
            }

            DB::beginTransaction();

            $vendor->update(array_merge(
                $validator->validated(),
                ['updated_by' => Auth::id()]
            ));

            $vendor->load(['vendorType', 'creator', 'updater']);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $vendor,
                'message' => 'Vendor updated successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error updating vendor: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified vendor.
     */
    public function destroy(Vendor $vendor): JsonResponse
    {
        try {
            DB::beginTransaction();

            $vendor->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Vendor deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error deleting vendor: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get vendors dropdown list.
     */
    public function dropdown(): JsonResponse
    {
        try {
            $vendors = Vendor::active()
                ->select('id', 'name', 'vendor_type_id')
                ->with('vendorType:id,name')
                ->orderBy('name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $vendors,
                'message' => 'Vendors dropdown retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving vendors dropdown: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get vendors statistics.
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total' => Vendor::count(),
                'active' => Vendor::active()->count(),
                'inactive' => Vendor::inactive()->count(),
                'by_vendor_type' => Vendor::select('vendor_type_id')
                    ->selectRaw('COUNT(*) as count')
                    ->with('vendorType:id,name')
                    ->groupBy('vendor_type_id')
                    ->get()
                    ->map(function ($item) {
                        return [
                            'vendor_type' => $item->vendorType->name ?? 'Unknown',
                            'count' => $item->count
                        ];
                    })
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Vendor statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving vendor statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk update vendor status.
     */
    public function bulkStatusUpdate(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'vendor_ids' => 'required|array',
                'vendor_ids.*' => 'exists:vendors,id',
                'status' => 'required|in:active,inactive'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'errors' => $validator->errors(),
                    'message' => 'Validation failed'
                ], 422);
            }

            DB::beginTransaction();

            $updated = Vendor::whereIn('id', $request->vendor_ids)
                ->update([
                    'status' => $request->status,
                    'updated_by' => Auth::id()
                ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => ['updated_count' => $updated],
                'message' => "Successfully updated {$updated} vendor(s) status to {$request->status}"
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error updating vendor status: ' . $e->getMessage()
            ], 500);
        }
    }
}

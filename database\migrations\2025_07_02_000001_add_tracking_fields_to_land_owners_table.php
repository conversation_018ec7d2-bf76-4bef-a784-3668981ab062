<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('land_owners', function (Blueprint $table) {
            // Check if columns don't exist before adding them
            if (!Schema::hasColumn('land_owners', 'created_by')) {
                $table->unsignedBigInteger('created_by')->nullable()->after('cash_received');
                $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
                $table->index(['created_by']);
            }
            
            if (!Schema::hasColumn('land_owners', 'updated_by')) {
                $table->unsignedBigInteger('updated_by')->nullable()->after('created_by');
                $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
                $table->index(['updated_by']);
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('land_owners', function (Blueprint $table) {
            // Check if columns exist before dropping them
            if (Schema::hasColumn('land_owners', 'created_by')) {
                $table->dropForeign(['created_by']);
                $table->dropColumn(['created_by']);
            }
            
            if (Schema::hasColumn('land_owners', 'updated_by')) {
                $table->dropForeign(['updated_by']);
                $table->dropColumn(['updated_by']);
            }
        });
    }
};

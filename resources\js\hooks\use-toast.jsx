import { showAlertMethods as sweetAlert } from '@/utils/sweetAlert';

// Hook to use toast notifications
export const useToast = () => {
  const toast = ({ title, description, variant = "default" }) => {
    let icon = 'success';
    
    // Map variant to sweetAlert icon
    switch (variant) {
      case 'destructive':
        icon = 'error';
        break;
      case 'warning':
        icon = 'warning';
        break;
      case 'info':
        icon = 'info';
        break;
      default:
        icon = 'success';
    }

    // Use sweetAlert toast
    sweetAlert.toast(description || title, icon);
  };

  return { toast };
};

// Named export for direct import
export const toast = ({ title, description, variant = "default" }) => {
  let icon = 'success';
  
  // Map variant to sweetAlert icon
  switch (variant) {
    case 'destructive':
      icon = 'error';
      break;
    case 'warning':
      icon = 'warning';
      break;
    case 'info':
      icon = 'info';
      break;
    default:
      icon = 'success';
  }

  // Use sweetAlert toast
  sweetAlert.toast(description || title, icon);
};

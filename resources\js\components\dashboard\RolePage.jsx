import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { useTranslation } from '@/hooks/useTranslation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Shield,
  Users,
  Settings,
  Eye,
  EyeOff,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import * as roleAPI from '../../services/roleAPI';
import Swal from 'sweetalert2';

const RolePage = () => {
  // State management
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [perPage, setPerPage] = useState(10);
  const [showAddRoleModal, setShowAddRoleModal] = useState(false);
  const [showEditRoleModal, setShowEditRoleModal] = useState(false);
  const [editingRole, setEditingRole] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  const [statistics, setStatistics] = useState({
    total_roles: 0,
    active_roles: 0,
    inactive_roles: 0,
    total_users: 0
  });

  // Form data for new role
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    status: 'active'
  });

  // Form data for editing role
  const [editFormData, setEditFormData] = useState({
    name: '',
    description: '',
    status: 'active'
  });

  // Module permissions state
  const [selectedModules, setSelectedModules] = useState({});
  const [modulePermissions, setModulePermissions] = useState({});
  const [editSelectedModules, setEditSelectedModules] = useState({});
  const [editModulePermissions, setEditModulePermissions] = useState({});
  const [availableModules, setAvailableModules] = useState([]);
  const [availablePermissions, setAvailablePermissions] = useState([]);

  // Fetch available modules and permissions on component mount
  useEffect(() => {
    const fetchModulesAndPermissions = async () => {
      try {
        const response = await roleAPI.getAvailableModules();
        if (response.success) {
          setAvailableModules(response.data.modules);
          setAvailablePermissions(response.data.permissions);
        }
      } catch (error) {
        console.error('Error fetching modules:', error);
      }
    };

    fetchModulesAndPermissions();
  }, []);

  // Fetch roles data
  useEffect(() => {
    fetchRoles();
  }, [searchTerm, currentPage, perPage]);

  // Fetch statistics
  useEffect(() => {
    fetchStatistics();
  }, [roles]);

  const fetchRoles = async () => {
    setLoading(true);
    try {
      const params = {
        search: searchTerm,
        page: currentPage,
        per_page: perPage,
        sort_by: 'updated_at',
        sort_order: 'desc'
      };

      const response = await roleAPI.getRoles(params);
      if (response.success) {
        setRoles(response.data);
        setTotalRecords(response.pagination.total);
        setTotalPages(response.pagination.last_page);
      }
    } catch (error) {
      console.error('Error fetching roles:', error);
      // You can add a toast notification here
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await roleAPI.getStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    // Search logic is handled in useEffect
  };

  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'inactive':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPermissionColor = (permission) => {
    const colors = [
      'bg-blue-100 text-blue-800',
      'bg-purple-100 text-purple-800',
      'bg-green-100 text-green-800',
      'bg-yellow-100 text-yellow-800',
      'bg-pink-100 text-pink-800',
      'bg-indigo-100 text-indigo-800',
      'bg-orange-100 text-orange-800'
    ];
    return colors[permission.length % colors.length];
  };

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle module selection
  const handleModuleChange = (moduleKey, checked) => {
    setSelectedModules(prev => ({
      ...prev,
      [moduleKey]: checked
    }));

    // If unchecking a module, remove all its permissions
    if (!checked) {
      setModulePermissions(prev => {
        const newPerms = { ...prev };
        delete newPerms[moduleKey];
        return newPerms;
      });
    }
  };

  // Handle permission changes for a module
  const handlePermissionChange = (moduleKey, permission, checked) => {
    setModulePermissions(prev => ({
      ...prev,
      [moduleKey]: {
        ...prev[moduleKey],
        [permission]: checked
      }
    }));
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      status: 'active'
    });
    setSelectedModules({});
    setModulePermissions({});
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Build the role data
      const accessibleModules = Object.keys(selectedModules).filter(key => selectedModules[key]);
      const modulePerms = {};
      
      accessibleModules.forEach(moduleKey => {
        const perms = modulePermissions[moduleKey] || {};
        modulePerms[moduleKey] = Object.keys(perms).filter(perm => perms[perm]);
      });

      const newRoleData = {
        name: formData.name,
        description: formData.description,
        status: formData.status,
        accessible_modules: accessibleModules,
        module_permissions: modulePerms
      };

      const response = await roleAPI.createRole(newRoleData);
      
      if (response.success) {
        // Reset form and close modal
        resetForm();
        setShowAddRoleModal(false);
        
        // Refresh the roles list
        await fetchRoles();
        await fetchStatistics();

        // Show success message (you can replace this with a toast notification)
        alert('Role created successfully!');
      }

    } catch (error) {
      console.error('Error creating role:', error);
      
      if (error.response?.data?.errors) {
        // Handle validation errors
        const errors = error.response.data.errors;
        let errorMessage = 'Validation errors:\n';
        Object.keys(errors).forEach(field => {
          errorMessage += `${field}: ${errors[field].join(', ')}\n`;
        });
        alert(errorMessage);
      } else {
        alert('Error creating role. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Sweet Alert helper functions
  const showAlert = {
    success: (title, text) => {
      Swal.fire({
        icon: 'success',
        title: title,
        text: text,
        confirmButtonColor: '#10b981',
        customClass: {
          popup: 'swal2-z-index-high',
          backdrop: 'swal2-backdrop-z-index-high'
        }
      });
    },
    error: (title, text) => {
      Swal.fire({
        icon: 'error',
        title: title,
        text: text,
        confirmButtonColor: '#ef4444',
        customClass: {
          popup: 'swal2-z-index-high',
          backdrop: 'swal2-backdrop-z-index-high'
        }
      });
    },
    warning: (title, text) => {
      Swal.fire({
        icon: 'warning',
        title: title,
        text: text,
        confirmButtonColor: '#f59e0b',
        customClass: {
          popup: 'swal2-z-index-high',
          backdrop: 'swal2-backdrop-z-index-high'
        }
      });
    },
    confirm: (title, text) => {
      return Swal.fire({
        title,
        text,
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'Yes, delete it!',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        customClass: {
          popup: 'swal2-z-index-high',
          backdrop: 'swal2-backdrop-z-index-high'
        }
      });
    },
    loading: (title, text) => {
      return Swal.fire({
        title,
        text,
        icon: 'info',
        allowOutsideClick: false,
        allowEscapeKey: false,
        showConfirmButton: false,
        customClass: {
          popup: 'swal2-z-index-high',
          backdrop: 'swal2-backdrop-z-index-high'
        },
        didOpen: () => {
          Swal.showLoading();
        }
      });
    }
  };

  // Handle edit role
  const handleEditRole = async (role) => {
    try {
      // Fetch the complete role data
      const response = await roleAPI.getRole(role.id);
      
      if (response.success) {
        const roleData = response.data;
        
        // Set edit form data
        setEditFormData({
          name: roleData.name,
          description: roleData.description || '',
          status: roleData.status
        });
        
        // Set editing role
        setEditingRole(roleData);
        
        // Set edit module permissions
        const editModules = {};
        const editPermissions = {};
        
        roleData.accessible_modules.forEach(moduleKey => {
          editModules[moduleKey] = true;
          editPermissions[moduleKey] = {};
          
          const modulePerms = roleData.module_permissions[moduleKey] || [];
          modulePerms.forEach(permission => {
            editPermissions[moduleKey][permission] = true;
          });
        });
        
        setEditSelectedModules(editModules);
        setEditModulePermissions(editPermissions);
        
        // Show edit modal
        setShowEditRoleModal(true);
      }
    } catch (error) {
      console.error('Error fetching role details:', error);
      showAlert.error('Error!', 'Failed to load role details. Please try again.');
    }
  };

  // Handle delete role
  const handleDeleteRole = async (role) => {
    const result = await showAlert.confirm(
      'Are you sure?',
      `You are about to delete the role "${role.name}". This action cannot be undone.`
    );

    if (result.isConfirmed) {
      // Show loading alert
      showAlert.loading(
        'Deleting Role...',
        'Please wait while we delete the role.'
      );

      try {
        const response = await roleAPI.deleteRole(role.id);

        if (response.success) {
          // Refresh data
          await fetchRoles();
          await fetchStatistics();

          // Success alert
          showAlert.success(
            'Deleted!',
            'Role has been deleted successfully.'
          );
        } else {
          // Error alert
          showAlert.error(
            'Error!',
            response.message || 'Failed to delete the role'
          );
        }
      } catch (error) {
        console.error('Error deleting role:', error);
        // Network/Server error alert
        showAlert.error(
          'Error!',
          'Error deleting role. Please try again.'
        );
      }
    }
  };

  // Handle edit form submission
  const handleEditSubmit = async (e) => {
    e.preventDefault();
    setIsUpdating(true);

    try {
      // Build the role data
      const accessibleModules = Object.keys(editSelectedModules).filter(key => editSelectedModules[key]);
      const modulePerms = {};
      
      accessibleModules.forEach(moduleKey => {
        const perms = editModulePermissions[moduleKey] || {};
        modulePerms[moduleKey] = Object.keys(perms).filter(perm => perms[perm]);
      });

      const updatedRoleData = {
        name: editFormData.name,
        description: editFormData.description,
        status: editFormData.status,
        accessible_modules: accessibleModules,
        module_permissions: modulePerms
      };

      const response = await roleAPI.updateRole(editingRole.id, updatedRoleData);
      
      if (response.success) {
        // Reset edit form and close modal
        resetEditForm();
        setShowEditRoleModal(false);
        
        // Refresh the roles list
        await fetchRoles();
        await fetchStatistics();

        // Show success message
        showAlert.success('Success!', 'Role updated successfully!');
      }

    } catch (error) {
      console.error('Error updating role:', error);
      
      if (error.response?.data?.errors) {
        // Handle validation errors
        const errors = error.response.data.errors;
        let errorMessage = 'Validation errors:\n';
        Object.keys(errors).forEach(field => {
          errorMessage += `${field}: ${errors[field].join(', ')}\n`;
        });
        showAlert.error('Validation Error!', errorMessage);
      } else {
        showAlert.error('Error!', 'Error updating role. Please try again.');
      }
    } finally {
      setIsUpdating(false);
    }
  };

  // Reset edit form
  const resetEditForm = () => {
    setEditFormData({
      name: '',
      description: '',
      status: 'active'
    });
    setEditSelectedModules({});
    setEditModulePermissions({});
    setEditingRole(null);
  };

  // Handle edit module selection
  const handleEditModuleChange = (moduleKey, checked) => {
    setEditSelectedModules(prev => ({
      ...prev,
      [moduleKey]: checked
    }));

    // If unchecking a module, clear its permissions
    if (!checked) {
      setEditModulePermissions(prev => ({
        ...prev,
        [moduleKey]: {}
      }));
    }
  };

  // Handle edit permission changes for a module
  const handleEditPermissionChange = (moduleKey, permission, checked) => {
    setEditModulePermissions(prev => ({
      ...prev,
      [moduleKey]: {
        ...prev[moduleKey],
        [permission]: checked
      }
    }));
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Role Management</h1>
          <p className="text-muted-foreground">
            Manage user roles and permissions in the system
          </p>
        </div>
        <Button onClick={() => setShowAddRoleModal(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add New Role
        </Button>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search Roles
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by role name or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button type="submit">
              <Search className="mr-2 h-4 w-4" />
              Search
            </Button>
          </form>
        </CardContent>
      </Card>


  {/* Role Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.total_roles}</div>
            <p className="text-xs text-muted-foreground">
              {statistics.active_roles} active roles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Users</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {statistics.total_users}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all roles
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Roles</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {statistics.active_roles}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive Roles</CardTitle>
            <EyeOff className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {statistics.inactive_roles}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently inactive
            </p>
          </CardContent>
        </Card>
      </div>



      {/* Roles Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            System Roles ({totalRecords})
          </CardTitle>
          <CardDescription>
            Manage roles and their associated permissions
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : roles.length === 0 ? (
            <div className="text-center py-8">
              <Shield className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No roles found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? 'Try adjusting your search criteria.' : 'Get started by creating a new role.'}
              </p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Role Name</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead>Accessible Modules</TableHead>
                    <TableHead>Users</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Last Updated</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {roles.map((role) => (
                    <TableRow key={role.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center gap-2">
                          <Shield className="h-4 w-4 text-blue-600" />
                          {role.name}
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-gray-600">{role.description}</span>
                      </TableCell>
                      <TableCell>
                        <div className="flex flex-wrap gap-1 max-w-[300px]">
                          {role.accessible_modules.slice(0, 4).map((moduleKey) => {
                            const module = availableModules.find(m => m.key === moduleKey);
                            return (
                              <Badge
                                key={moduleKey}
                                variant="secondary"
                                className={`text-xs ${getPermissionColor(moduleKey)}`}
                              >
                                {module ? module.name : moduleKey}
                              </Badge>
                            );
                          })}
                          {role.accessible_modules.length > 4 && (
                            <Badge variant="outline" className="text-xs">
                              +{role.accessible_modules.length - 4} more
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Users className="h-4 w-4 text-gray-500" />
                          <span className="font-medium">{role.users_count}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge className={`${getStatusColor(role.status)} border-0`}>
                          {role.status === 'active' ? (
                            <Eye className="mr-1 h-3 w-3" />
                          ) : (
                            <EyeOff className="mr-1 h-3 w-3" />
                          )}
                          {role.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-gray-500">
                          {new Date(role.updated_at).toLocaleDateString()}
                        </span>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Settings className="mr-2 h-4 w-4" />
                              Module Permissions
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditRole(role)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Role
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Settings className="mr-2 h-4 w-4" />
                              Manage Permissions
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              className="text-red-600"
                              onClick={() => handleDeleteRole(role)}
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Role
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between space-x-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * perPage) + 1} to {Math.min(currentPage * perPage, totalRecords)} of {totalRecords} entries
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage <= 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1;
                        return (
                          <Button
                            key={page}
                            variant={currentPage === page ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(page)}
                          >
                            {page}
                          </Button>
                        );
                      })}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage >= totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add New Role Modal */}
      <Dialog open={showAddRoleModal} onOpenChange={setShowAddRoleModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Plus className="h-5 w-5" />
              Add New Role
            </DialogTitle>
            <DialogDescription>
              Create a new role and configure its module permissions
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="roleName">Role Name *</Label>
                <Input
                  id="roleName"
                  placeholder="Enter role name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="roleStatus">Status</Label>
                <select
                  id="roleStatus"
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                >
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="roleDescription">Description</Label>
              <Textarea
                id="roleDescription"
                placeholder="Enter role description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={3}
              />
            </div>

            {/* Module Permissions */}
            <div className="space-y-4">
              <div>
                <Label className="text-base font-semibold">Module Permissions</Label>
                <p className="text-sm text-muted-foreground">
                  Select which modules this role can access and their specific permissions
                </p>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {availableModules.map((module) => {
                  const isModuleSelected = selectedModules[module.key] || false;
                  const modulePerms = modulePermissions[module.key] || {};

                  return (
                    <Card key={module.key} className="p-4">
                      <div className="space-y-3">
                        {/* Module Selection */}
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id={`module-${module.key}`}
                            checked={isModuleSelected}
                            onCheckedChange={(checked) => handleModuleChange(module.key, checked)}
                          />
                          <Label 
                            htmlFor={`module-${module.key}`}
                            className="text-sm font-medium cursor-pointer"
                          >
                            {module.name}
                          </Label>
                        </div>

                        {/* Permissions for this module */}
                        {isModuleSelected && (
                          <div className="ml-6 space-y-2">
                            <Label className="text-xs text-muted-foreground">Permissions</Label>
                            <div className="grid grid-cols-2 gap-2">
                              {availablePermissions.map((permission) => (
                                <div key={permission} className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`${module.key}-${permission}`}
                                    checked={modulePerms[permission] || false}
                                    onCheckedChange={(checked) => 
                                      handlePermissionChange(module.key, permission, checked)
                                    }
                                  />
                                  <Label 
                                    htmlFor={`${module.key}-${permission}`}
                                    className="text-xs cursor-pointer capitalize"
                                  >
                                    {permission}
                                  </Label>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </Card>
                  );
                })}
              </div>
            </div>

            <DialogFooter className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  resetForm();
                  setShowAddRoleModal(false);
                }}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting || !formData.name.trim()}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Role
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Role Modal */}
      <Dialog open={showEditRoleModal} onOpenChange={setShowEditRoleModal}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <form onSubmit={handleEditSubmit}>
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <Edit className="h-5 w-5" />
                Edit Role
              </DialogTitle>
              <DialogDescription>
                Update the role details and permissions
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-6 py-4">
              {/* Basic Information */}
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="edit-name">Role Name *</Label>
                  <Input
                    id="edit-name"
                    type="text"
                    placeholder="Enter role name"
                    value={editFormData.name}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, name: e.target.value }))}
                    required
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea
                    id="edit-description"
                    placeholder="Enter role description"
                    value={editFormData.description}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, description: e.target.value }))}
                    rows={3}
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="edit-status">Status</Label>
                  <select
                    id="edit-status"
                    value={editFormData.status}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, status: e.target.value }))}
                    className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
              </div>

              {/* Module Permissions */}
              <div className="grid gap-4">
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  <Label className="text-base font-semibold">Module Permissions</Label>
                </div>
                
                {availableModules.map((module) => (
                  <Card key={module.key} className="p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <Checkbox
                        id={`edit-module-${module.key}`}
                        checked={editSelectedModules[module.key] || false}
                        onCheckedChange={(checked) => handleEditModuleChange(module.key, checked)}
                      />
                      <Label htmlFor={`edit-module-${module.key}`} className="font-medium">
                        {module.name}
                      </Label>
                      <Badge variant="outline" className="ml-2">
                        {module.key}
                      </Badge>
                    </div>
                    
                    {module.description && (
                      <p className="text-sm text-gray-600 mb-3">{module.description}</p>
                    )}

                    {editSelectedModules[module.key] && (
                      <div className="ml-6 space-y-2">
                        <Label className="text-sm font-medium text-gray-700">Permissions:</Label>
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                          {availablePermissions.map((permission) => (
                            <div key={`${module.key}-${permission}`} className="flex items-center space-x-2">
                              <Checkbox
                                id={`edit-${module.key}-${permission}`}
                                checked={editModulePermissions[module.key]?.[permission] || false}
                                onCheckedChange={(checked) => handleEditPermissionChange(module.key, permission, checked)}
                              />
                              <Label 
                                htmlFor={`edit-${module.key}-${permission}`}
                                className="text-sm capitalize"
                              >
                                {permission}
                              </Label>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </Card>
                ))}
              </div>
            </div>

            <DialogFooter className="flex gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  resetEditForm();
                  setShowEditRoleModal(false);
                }}
                disabled={isUpdating}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isUpdating || !editFormData.name.trim()}
              >
                {isUpdating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Updating...
                  </>
                ) : (
                  <>
                    <Edit className="mr-2 h-4 w-4" />
                    Update Role
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RolePage;
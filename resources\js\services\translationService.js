import { languageAPI } from './languageAPI.js';

class TranslationService {
  constructor() {
    this.translations = {};
    this.currentLanguage = 'en'; // Default fallback
    this.fallbackLanguage = 'en';
    this.isLoaded = false;
    this.loadPromise = null;
  }

  /**
   * Initialize the translation service
   */
  async init() {
    if (this.loadPromise) {
      return this.loadPromise;
    }

    this.loadPromise = this.loadTranslations();
    return this.loadPromise;
  }

  /**
   * Load translations for the current language
   */
  async loadTranslations() {
    try {
      // Get the default language from the API
      const defaultLanguage = await this.getDefaultLanguage();
      this.currentLanguage = defaultLanguage?.code || 'en';

      // Load translation files
      await this.loadTranslationFiles();
      this.isLoaded = true;
      
      // Dispatch event to notify components that translations are loaded
      window.dispatchEvent(new CustomEvent('translationsLoaded', {
        detail: { language: this.currentLanguage }
      }));

    } catch (error) {
      // Debug statement removed
      // Fallback to English if loading fails
      this.currentLanguage = 'en';
      await this.loadTranslationFiles();
      this.isLoaded = true;
    }
  }

  /**
   * Get the default language from the API
   */
  async getDefaultLanguage() {
    try {
      const response = await languageAPI.getLanguages({ 
        search: '', 
        page: 1, 
        per_page: 100 
      });
      
      if (response.success) {
        return response.data.find(lang => lang.is_default && lang.status === 'active');
      }
    } catch (error) {
      // Debug statement removed
    }
    return null;
  }

  /**
   * Load translation files for the current language
   */
  async loadTranslationFiles() {
    try {
      // Try to load the specific language file
      let languageCode = this.currentLanguage;
      
      // Handle language variants (e.g., pt-BR -> pt)
      if (languageCode.includes('-')) {
        const baseLanguage = languageCode.split('-')[0];
        try {
          const translations = await import(`../translations/${languageCode}.js`);
          this.translations[this.currentLanguage] = translations.default;
        } catch (variantError) {
          // If variant not found, try base language
          try {
            const baseTranslations = await import(`../translations/${baseLanguage}.js`);
            this.translations[this.currentLanguage] = baseTranslations.default;
          } catch (baseError) {
            throw variantError; // Throw original error
          }
        }
      } else {
        const translations = await import(`../translations/${languageCode}.js`);
        this.translations[this.currentLanguage] = translations.default;
      }
      
      // Also load fallback language if different
      if (this.currentLanguage !== this.fallbackLanguage) {
        try {
          const fallbackTranslations = await import(`../translations/${this.fallbackLanguage}.js`);
          this.translations[this.fallbackLanguage] = fallbackTranslations.default;
        } catch (fallbackError) {
          // Debug statement removed
        }
      }
    } catch (error) {
      // Debug statement removed
      // Load fallback language
      if (this.currentLanguage !== this.fallbackLanguage) {
        try {
          const fallbackTranslations = await import(`../translations/${this.fallbackLanguage}.js`);
          this.translations[this.fallbackLanguage] = fallbackTranslations.default;
          this.currentLanguage = this.fallbackLanguage;
        } catch (fallbackError) {
          // Debug statement removed
        }
      }
    }
  }

  /**
   * Translate a key with optional parameters
   */
  t(key, params = {}) {
    if (!this.isLoaded) {
      return key; // Return key if translations not loaded yet
    }

    let translation = this.getTranslation(key, this.currentLanguage);
    
    // Fallback to fallback language if translation not found
    if (!translation && this.currentLanguage !== this.fallbackLanguage) {
      translation = this.getTranslation(key, this.fallbackLanguage);
    }
    
    // If still no translation, return the key
    if (!translation) {
      // Debug statement removed
      return key;
    }

    // Ensure we have a string before interpolation
    if (typeof translation !== 'string') {
      // Debug statement removed
      return key;
    }

    // Replace parameters in the translation
    return this.interpolate(translation, params);
  }

  /**
   * Get translation from a specific language
   */
  getTranslation(key, language) {
    const translations = this.translations[language];
    if (!translations) return null;

    // Support nested keys like 'common.buttons.save'
    const keys = key.split('.');
    let current = translations;
    
    for (const k of keys) {
      if (current && typeof current === 'object' && k in current) {
        current = current[k];
      } else {
        return null;
      }
    }
    
    return typeof current === 'string' ? current : null;
  }

  /**
   * Interpolate parameters into translation string
   */
  interpolate(text, params) {
    return text.replace(/\{(\w+)\}/g, (match, key) => {
      return params[key] !== undefined ? params[key] : match;
    });
  }

  /**
   * Change the current language
   */
  async changeLanguage(languageCode) {
    if (languageCode === this.currentLanguage) return;
    
    this.currentLanguage = languageCode;
    this.isLoaded = false;
    await this.loadTranslationFiles();
    this.isLoaded = true;
    
    // Dispatch event to notify components of language change
    window.dispatchEvent(new CustomEvent('languageChanged', {
      detail: { language: this.currentLanguage }
    }));
  }

  /**
   * Get current language
   */
  getCurrentLanguage() {
    return this.currentLanguage;
  }

  /**
   * Check if translations are loaded
   */
  isReady() {
    return this.isLoaded;
  }

  /**
   * Get available languages
   */
  async getAvailableLanguages() {
    try {
      const response = await languageAPI.getLanguages({ 
        search: '', 
        page: 1, 
        per_page: 100 
      });
      
      if (response.success) {
        return response.data.filter(lang => lang.status === 'active');
      }
    } catch (error) {
      // Debug statement removed
    }
    return [];
  }

  /**
   * Format date according to current language
   */
  formatDate(date, options = {}) {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    
    try {
      return new Intl.DateTimeFormat(this.currentLanguage, options).format(dateObj);
    } catch (error) {
      // Fallback to default locale
      return new Intl.DateTimeFormat('en-US', options).format(dateObj);
    }
  }

  /**
   * Format number according to current language
   */
  formatNumber(number, options = {}) {
    try {
      return new Intl.NumberFormat(this.currentLanguage, options).format(number);
    } catch (error) {
      // Fallback to default locale
      return new Intl.NumberFormat('en-US', options).format(number);
    }
  }

  /**
   * Format currency according to current language
   */
  formatCurrency(amount, currency = 'USD') {
    return this.formatNumber(amount, {
      style: 'currency',
      currency: currency
    });
  }
}

// Create and export a singleton instance
export const translator = new TranslationService();

// Export the translation function for convenience
export const t = (key, params) => translator.t(key, params);

export default translator;

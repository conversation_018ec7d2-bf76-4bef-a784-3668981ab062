import axios from 'axios';
import { API_BASE_URL } from '../config/api.js';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor for authentication
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error);
    
    // Handle 401 Unauthorized
    if (error.response?.status === 401) {
      localStorage.removeItem('auth_token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    
    return Promise.reject(error);
  }
);

export const landDocumentAPI = {
  // Get documents by land acquisition ID
  async getByLandAcquisition(landAcquisitionId) {
    try {
      const response = await api.get(`/land-documents/by-land/${landAcquisitionId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching land documents:', error);
      throw error;
    }
  },

  // Upload multiple documents for a land acquisition
  async bulkUpload(landAcquisitionId, documents) {
    try {
      const formData = new FormData();
      formData.append('land_acquisition_id', landAcquisitionId);
      
      documents.forEach((document, index) => {
        if (document.file) {
          formData.append(`documents[${index}][file]`, document.file);
          formData.append(`documents[${index}][document_type]`, document.document_type);
          formData.append(`documents[${index}][description]`, document.description || '');
        }
      });

      const response = await api.post('/land-documents/bulk-upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error uploading documents:', error);
      throw error;
    }
  },

  // Delete a document
  async delete(documentId) {
    try {
      const response = await api.delete(`/land-documents/${documentId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting document:', error);
      throw error;
    }
  },

  // Get all documents (with filtering)
  async getAll(params = {}) {
    try {
      const response = await api.get('/land-documents', { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching all documents:', error);
      throw error;
    }
  },

  // Create a single document
  async create(documentData) {
    try {
      const formData = new FormData();
      
      Object.keys(documentData).forEach(key => {
        if (documentData[key] !== null && documentData[key] !== undefined) {
          formData.append(key, documentData[key]);
        }
      });

      const response = await api.post('/land-documents', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error creating document:', error);
      throw error;
    }
  },

  // Update a document
  async update(documentId, documentData) {
    try {
      const formData = new FormData();
      formData.append('_method', 'PUT');
      
      Object.keys(documentData).forEach(key => {
        if (documentData[key] !== null && documentData[key] !== undefined) {
          formData.append(key, documentData[key]);
        }
      });

      const response = await api.post(`/land-documents/${documentId}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error updating document:', error);
      throw error;
    }
  },

  // Get a single document
  async get(documentId) {
    try {
      const response = await api.get(`/land-documents/${documentId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching document:', error);
      throw error;
    }
  },

  // Download a document
  async download(documentId) {
    try {
      const response = await api.get(`/land-documents/${documentId}/download`, {
        responseType: 'blob',
      });
      return response;
    } catch (error) {
      console.error('Error downloading document:', error);
      throw error;
    }
  }
};

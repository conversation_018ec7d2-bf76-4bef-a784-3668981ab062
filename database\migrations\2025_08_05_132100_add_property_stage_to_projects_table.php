<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->enum('property_stage', [
                'pre_planning', 
                'planning', 
                'design', 
                'approvals', 
                'pre_construction', 
                'foundation', 
                'structure', 
                'roofing', 
                'exterior', 
                'interior', 
                'finishing', 
                'inspection', 
                'completed', 
                'handover', 
                'warranty'
            ])->default('pre_planning')->after('status');
            
            // Add index for better query performance
            $table->index(['property_stage', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            $table->dropIndex(['property_stage', 'status']);
            $table->dropColumn('property_stage');
        });
    }
};

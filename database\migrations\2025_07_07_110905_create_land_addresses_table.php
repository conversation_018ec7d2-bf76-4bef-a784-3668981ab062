<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('land_addresses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('land_acquisition_id')->constrained()->onDelete('cascade');
            $table->string('plot_no')->nullable();
            $table->string('road')->nullable();
            $table->string('area')->nullable();
            $table->string('upazila')->nullable();
            $table->string('thana')->nullable();
            $table->string('city')->nullable();
            $table->string('district')->nullable();
            $table->string('country')->default('Bangladesh');
            $table->string('zip_code')->nullable();
            $table->timestamps();
            
            // Add index for better query performance
            $table->index('land_acquisition_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('land_addresses');
    }
};

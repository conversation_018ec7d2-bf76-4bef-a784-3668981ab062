<?php

namespace App\Http\Controllers;

use App\Models\LandOwner;
use App\Models\LandOwnerAudit;
use App\Traits\EnvironmentAwareTrait;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class LandOwnerController extends Controller
{
    use EnvironmentAwareTrait;
    /**
     * Constructor to apply auth middleware
     */
    public function __construct()
    {
        // Auth middleware is handled at route level via auth:sanctum
        // $this->middleware('auth:sanctum');
    }
  
    public function index(Request $request): JsonResponse
    {
        try {
            $query = LandOwner::with(['creator', 'updater']);

            // Apply search filter
            if ($request->has('search') && $request->search !== '') {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('first_name', 'like', "%{$search}%")
                      ->orWhere('last_name', 'like', "%{$search}%")
                      ->orWhere('father_name', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%")
                      ->orWhere('nid_number', 'like', "%{$search}%");
                });
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'first_name');
            $sortOrder = $request->get('sort_order', 'asc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 50);
            $landOwners = $query->paginate($perPage);

            // Fix pagination URLs for current environment
            $data = $this->fixPaginationUrls($landOwners);

            return response()->json([
                'success' => true,
                'data' => $data
            ]);

        } catch (\Exception $e) {
            \Log::error('Land Owner index failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_params' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch land owners: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created land owner
     */
    public function store(Request $request): JsonResponse
    {
        try {
     
        
            // Build validation rules
            $rules = [
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'father_name' => 'required|string|max:255',
                'mother_name' => 'nullable|string|max:255',
                'address' => 'required|string',
                'phone' => 'nullable|string|max:20',
                'nid_number' => 'nullable|string|max:20|unique:land_owners,nid_number',
                'email' => 'nullable|email|max:255',
                'document_type' => 'nullable|in:nid,passport',
            ];

            // Only validate image fields if they are actual file uploads
            if ($request->hasFile('photo')) {
                $rules['photo'] = 'nullable|image|mimes:jpeg,png,jpg,gif,webp,bmp,tiff,svg|max:10240';
            }
            if ($request->hasFile('nid_front')) {
                $rules['nid_front'] = 'nullable|image|mimes:jpeg,png,jpg,gif,webp,bmp,tiff,svg|max:10240';
            }
            if ($request->hasFile('nid_back')) {
                $rules['nid_back'] = 'nullable|image|mimes:jpeg,png,jpg,gif,webp,bmp,tiff,svg|max:10240';
            }
            if ($request->hasFile('passport_photo')) {
                $rules['passport_photo'] = 'nullable|image|mimes:jpeg,png,jpg,gif,webp,bmp,tiff,svg|max:10240';
            }

            $validated = $request->validate($rules);

            // Handle file uploads and add URLs to validated data
            if ($request->hasFile('photo')) {
                try {
                    $validated['photo'] = $this->handleImageUpload($request->file('photo'));
                   
                } catch (\Exception $e) {
                 
                    throw new \Exception('Photo upload failed: ' . $e->getMessage());
                }
            }

            if ($request->hasFile('nid_front')) {
                try {
                    $validated['nid_front'] = $this->handleImageUpload($request->file('nid_front'), 'documents');
                  
                } catch (\Exception $e) {
                  
                    throw new \Exception('NID Front upload failed: ' . $e->getMessage());
                }
            }

            if ($request->hasFile('nid_back')) {
                try {
                    $validated['nid_back'] = $this->handleImageUpload($request->file('nid_back'), 'documents');
                 
                } catch (\Exception $e) {
                 
                    throw new \Exception('NID Back upload failed: ' . $e->getMessage());
                }
            }

            if ($request->hasFile('passport_photo')) {
                try {
                    $validated['passport_photo'] = $this->handleImageUpload($request->file('passport_photo'), 'documents');
                
                } catch (\Exception $e) {
                
                    throw new \Exception('Passport Photo upload failed: ' . $e->getMessage());
                }
            }

            // Create the land owner record (audit will be created automatically via the trait)
            $landOwner = LandOwner::create($validated);

            return response()->json([
                'success' => true,
                'message' => 'Land owner created successfully',
                'data' => $landOwner->load(['creator', 'updater'])
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
        
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
         
              
            return response()->json([
                'success' => false,
                'message' => 'Failed to create land owner: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified land owner
     */
    public function show(LandOwner $landOwner): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $landOwner->load(['landAcquisitions', 'creator', 'updater', 'audits.user'])
        ]);
    }

    /**
     * Update the specified land owner
     */
    public function update(Request $request, LandOwner $landOwner): JsonResponse
    {
        try {
           

            // Build validation rules
            $rules = [
                'first_name' => 'sometimes|required|string|max:255',
                'last_name' => 'sometimes|required|string|max:255',
                'father_name' => 'sometimes|required|string|max:255',
                'mother_name' => 'sometimes|nullable|string|max:255',
                'address' => 'sometimes|required|string',
                'phone' => 'sometimes|nullable|string|max:20',
                'nid_number' => 'sometimes|nullable|string|max:20|unique:land_owners,nid_number,' . $landOwner->id,
                'email' => 'sometimes|nullable|email|max:255',
                'document_type' => 'sometimes|nullable|in:nid,passport',
            ];

            // Only validate image fields if they are actual file uploads
            if ($request->hasFile('photo')) {
                $rules['photo'] = 'sometimes|nullable|image|mimes:jpeg,png,jpg,gif,webp,bmp,tiff,svg|max:10240';
            }
            if ($request->hasFile('nid_front')) {
                $rules['nid_front'] = 'sometimes|nullable|image|mimes:jpeg,png,jpg,gif,webp,bmp,tiff,svg|max:10240';
            }
            if ($request->hasFile('nid_back')) {
                $rules['nid_back'] = 'sometimes|nullable|image|mimes:jpeg,png,jpg,gif,webp,bmp,tiff,svg|max:10240';
            }
            if ($request->hasFile('passport_photo')) {
                $rules['passport_photo'] = 'sometimes|nullable|image|mimes:jpeg,png,jpg,gif,webp,bmp,tiff,svg|max:10240';
            }

            $validated = $request->validate($rules);

            // Handle photo upload if present
            if ($request->hasFile('photo')) {
                try {
                    // Delete old photo if exists
                    if ($landOwner->photo) {
                        $this->deleteOldImage($landOwner->photo);
                    }
                    
                    // Upload new photo
                    $validated['photo'] = $this->handleImageUpload($request->file('photo'));
                  
                } catch (\Exception $e) {
                  
                    throw new \Exception('Photo update failed: ' . $e->getMessage());
                }
            }

            // Handle NID front upload
            if ($request->hasFile('nid_front')) {
                try {
                    // Delete old file if exists
                    if ($landOwner->nid_front) {
                        $this->deleteOldImage($landOwner->nid_front);
                    }
                    
                    // Upload new file
                    $validated['nid_front'] = $this->handleImageUpload($request->file('nid_front'), 'documents');
                
                } catch (\Exception $e) {
                 
                    throw new \Exception('NID Front update failed: ' . $e->getMessage());
                }
            }

            // Handle NID back upload
            if ($request->hasFile('nid_back')) {
                try {
                    // Delete old file if exists
                    if ($landOwner->nid_back) {
                        $this->deleteOldImage($landOwner->nid_back);
                    }
                    
                    // Upload new file
                    $validated['nid_back'] = $this->handleImageUpload($request->file('nid_back'), 'documents');
                  
                } catch (\Exception $e) {
              
                    throw new \Exception('NID Back update failed: ' . $e->getMessage());
                }
            }

            // Handle passport photo upload
            if ($request->hasFile('passport_photo')) {
                try {
                    // Delete old file if exists
                    if ($landOwner->passport_photo) {
                        $this->deleteOldImage($landOwner->passport_photo);
                    }
                    
                    // Upload new file
                    $validated['passport_photo'] = $this->handleImageUpload($request->file('passport_photo'), 'documents');
               
                } catch (\Exception $e) {
                  
                    throw new \Exception('Passport Photo update failed: ' . $e->getMessage());
                }
            }

            // Update the land owner record
            $landOwner->update($validated);
          

            return response()->json([
                'success' => true,
                'message' => 'Land owner updated successfully',
                'data' => $landOwner->fresh(['creator', 'updater'])
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
         
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);

        } catch (\Exception $e) {
          
      
            return response()->json([
                'success' => false,
                'message' => 'Failed to update land owner: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified land owner
     */
    public function destroy($id): JsonResponse
    {
        try {
            $owner = LandOwner::find($id);
            if (!$owner) {
                return response()->json(['success' => false, 'message' => 'Not found'], 404);
            }

            // Delete the associated image files if they exist
            if ($owner->photo) {
                $this->deleteOldImage($owner->photo);
            }
            if ($owner->nid_front) {
                $this->deleteOldImage($owner->nid_front);
            }
            if ($owner->nid_back) {
                $this->deleteOldImage($owner->nid_back);
            }
            if ($owner->passport_photo) {
                $this->deleteOldImage($owner->passport_photo);
            }

            // Delete the database record (audit will be created automatically via the trait)
            $owner->delete();

            return response()->json(['success' => true, 'message' => 'Land owner and associated files deleted successfully']);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete land owner: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all land owners for dropdown (simplified response)
     */
    public function dropdown(): JsonResponse
    {
        $landOwners = LandOwner::select('id', 'first_name', 'last_name', 'father_name')
            ->orderBy('first_name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $landOwners
        ]);
    }

    /**
     * Get audit history for a specific land owner
     */
    public function auditHistory($id): JsonResponse
    {
        try {
            $landOwner = LandOwner::findOrFail($id);
            
            $audits = LandOwnerAudit::where('land_owner_id', $id)
                ->with('user:id,name,email')
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            return response()->json([
                'success' => true,
                'data' => [
                    'land_owner' => $landOwner->only(['id', 'name', 'father_name']),
                    'audits' => $audits
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch audit history: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get detailed audit history with change information for a specific land owner
     */
    public function detailedAuditHistory($id): JsonResponse
    {
        try {
            $landOwner = LandOwner::findOrFail($id);
            
            $audits = LandOwnerAudit::where('land_owner_id', $id)
                ->with('user:id,name,email')
                ->orderBy('created_at', 'desc')
                ->paginate(20);

            // Process each audit to show detailed changes
            $audits->getCollection()->transform(function ($audit) {
                $changes = [];
                
                if ($audit->old_values && $audit->new_values) {
                    $oldValues = is_array($audit->old_values) ? $audit->old_values : json_decode($audit->old_values, true);
                    $newValues = is_array($audit->new_values) ? $audit->new_values : json_decode($audit->new_values, true);
                    
                    if ($oldValues && $newValues) {
                        foreach ($newValues as $field => $newValue) {
                            $oldValue = $oldValues[$field] ?? null;
                            
                            if ($oldValue !== $newValue) {
                                $changes[] = [
                                    'field' => $this->getFieldDisplayName($field),
                                    'old_value' => $this->formatFieldValue($field, $oldValue),
                                    'new_value' => $this->formatFieldValue($field, $newValue),
                                ];
                            }
                        }
                    }
                }
                
                $audit->detailed_changes = $changes;
                return $audit;
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'land_owner' => $landOwner->only(['id', 'name', 'father_name']),
                    'audits' => $audits
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch detailed audit history: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all audit records (admin only)
     */
    public function allAudits(Request $request): JsonResponse
    {
        try {
            $query = LandOwnerAudit::with(['landOwner:id,first_name,last_name,father_name', 'user:id,name,email']);

            // Filter by action
            if ($request->has('action') && $request->action !== '') {
                $query->where('action', $request->action);
            }

            // Filter by user
            if ($request->has('user_id') && $request->user_id !== '') {
                $query->where('user_id', $request->user_id);
            }

            // Filter by date range
            if ($request->has('date_from') && $request->date_from !== '') {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to') && $request->date_to !== '') {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            $audits = $query->orderBy('created_at', 'desc')->paginate(50);

            return response()->json([
                'success' => true,
                'data' => $audits
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch audit records: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get audit statistics
     */
    public function auditStats(): JsonResponse
    {
        try {
            $stats = [
                'total_records' => LandOwner::count(),
                'created_today' => LandOwner::whereDate('created_at', today())->count(),
                'updated_today' => LandOwnerAudit::where('action', 'updated')->whereDate('created_at', today())->count(),
                'deleted_today' => LandOwnerAudit::where('action', 'deleted')->whereDate('created_at', today())->count(),
                'total_audits' => LandOwnerAudit::count(),
                'recent_activity' => LandOwnerAudit::with(['landOwner:id,name,father_name', 'user:id,name'])
                    ->orderBy('created_at', 'desc')
                    ->limit(10)
                    ->get(),
                'actions_summary' => LandOwnerAudit::selectRaw('action, COUNT(*) as count')
                    ->groupBy('action')
                    ->get(),
                'most_active_users' => LandOwnerAudit::selectRaw('user_id, users.name, COUNT(*) as activity_count')
                    ->join('users', 'land_owner_audits.user_id', '=', 'users.id')
                    ->groupBy('user_id', 'users.name')
                    ->orderBy('activity_count', 'desc')
                    ->limit(5)
                    ->get()
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch audit statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle image upload for land owner photos and documents
     */
    private function handleImageUpload($file, $subdirectory = 'photo'): string
    {
        try {
            // Check if file is valid
            if (!$file->isValid()) {
                throw new \Exception('Uploaded file is not valid: ' . $file->getErrorMessage());
            }

            // Generate unique filename with appropriate prefix
            $prefix = $subdirectory === 'documents' ? 'document' : 'landowner';
            $extension = $file->getClientOriginalExtension();
            $filename = $prefix . '_' . time() . '_' . Str::random(10) . '.' . $extension;
            
            // Create appropriate directory if it doesn't exist
            $publicDir = public_path('landowners/' . $subdirectory);
            if (!is_dir($publicDir)) {
                if (!mkdir($publicDir, 0755, true)) {
                    throw new \Exception('Failed to create directory: ' . $publicDir);
                }
            }
            
            // Use Laravel's move method instead of move_uploaded_file
            $destinationPath = $publicDir . DIRECTORY_SEPARATOR . $filename;
            
            if ($file->move($publicDir, $filename)) {
                // Return the public URL path
                $url = '/landowners/' . $subdirectory . '/' . $filename;
                
                // Verify the file exists and has content
                if (file_exists($destinationPath) && filesize($destinationPath) > 0) {
                    return $url;
                } else {
                    throw new \Exception('File was not properly stored or is empty');
                }
            } else {
                throw new \Exception('Failed to move uploaded file to public directory');
            }
            
        } catch (\Exception $e) {
            throw new \Exception('Image upload failed: ' . $e->getMessage());
        }
    }

    /**
     * Delete old image file
     */
    private function deleteOldImage(string $imageUrl): void
    {
        try {
            // Extract the path from URL
            // URL format: /landowners/photo/filename.jpg or /landowners/documents/filename.jpg
            if (preg_match('#/landowners/([^/]+)/([^/]+)$#', $imageUrl, $matches)) {
                $subdirectory = $matches[1];
                $filename = $matches[2];
                
                // Construct the full file path with proper directory separators
                $filePath = public_path('landowners' . DIRECTORY_SEPARATOR . $subdirectory . DIRECTORY_SEPARATOR . $filename);
                
                // Check if file exists before attempting to delete
                if (file_exists($filePath)) {
                    // Delete the file
                    unlink($filePath);
                }
            }
            
        } catch (\Exception $e) {
            throw $e;
        }
    }

    /**
     * Test endpoint without permissions - for debugging
     */
    public function testIndex(): JsonResponse
    {
        try {
            $landOwners = LandOwner::with(['creator', 'updater'])->limit(5)->get();

            return response()->json([
                'success' => true,
                'message' => 'Test endpoint - no permissions required',
                'count' => $landOwners->count(),
                'data' => $landOwners
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get display name for field
     */
    private function getFieldDisplayName($field): string
    {
        $fieldNames = [
            'first_name' => 'First Name',
            'last_name' => 'Last Name',
            'father_name' => 'Father\'s Name',
            'mother_name' => 'Mother\'s Name',
            'address' => 'Address',
            'phone' => 'Phone',
            'nid_number' => 'NID Number',
            'email' => 'Email',
            'photo' => 'Photo',
            'document_type' => 'Document Type',
            'nid_front' => 'NID Front',
            'nid_back' => 'NID Back',
            'passport_photo' => 'Passport Photo',
        ];

        return $fieldNames[$field] ?? ucfirst(str_replace('_', ' ', $field));
    }

    /**
     * Format field value for display
     */
    private function formatFieldValue($field, $value): string
    {
        if ($value === null) {
            return 'Not set';
        }

        if ($value === '') {
            return 'Empty';
        }

        switch ($field) {
            case 'photo':
            case 'nid_front':
            case 'nid_back':
            case 'passport_photo':
                return $value ? 'File uploaded' : 'No file';
            case 'document_type':
                return ucfirst($value);
            default:
                return (string) $value;
        }
    }

    /**
     * Activate a land owner
     */
    public function activate(LandOwner $landOwner): JsonResponse
    {
        try {
            $landOwner->update(['status' => 'active']);
            
            return response()->json([
                'success' => true,
                'message' => 'Land owner activated successfully',
                'data' => $landOwner->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to activate land owner: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Deactivate a land owner
     */
    public function deactivate(LandOwner $landOwner): JsonResponse
    {
        try {
            $landOwner->update(['status' => 'inactive']);
            
            return response()->json([
                'success' => true,
                'message' => 'Land owner deactivated successfully',
                'data' => $landOwner->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to deactivate land owner: ' . $e->getMessage()
            ], 500);
        }
    }
}

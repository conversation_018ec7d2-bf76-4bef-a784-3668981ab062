<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\LandOwners\Models\LandOwner;

class LandOwnerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $landOwners = [
            [
                'name' => '<PERSON>',
                'father_name' => '<PERSON>',
                'address' => 'House 15, Road 7, Dhanmondi, Dhaka-1205',
                'phone' => '+880171234567',
                'nid_number' => '1234567890123',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Fatima Begum',
                'father_name' => '<PERSON>',
                'address' => 'House 25, Road 12, Gulshan-1, Dhaka-1212',
                'phone' => '+880181234567',
                'nid_number' => '2345678901234',
                'email' => '<EMAIL>',
            ],
            [
                'name' => '<PERSON>',
                'father_name' => '<PERSON>',
                'address' => 'House 35, Road 18, Banani, Dhaka-1213',
                'phone' => '+880191234567',
                'nid_number' => '3456789012345',
                'email' => '<EMAIL>',
            ],
            [
                'name' => '<PERSON>a <PERSON><PERSON>un',
                'father_name' => '<PERSON> Latif',
                'address' => 'House 45, Road 22, Uttara, Dhaka-1230',
                'phone' => '+880161234567',
                'nid_number' => '4567890123456',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Aminul Islam',
                'father_name' => 'Rafiqul Islam',
                'address' => 'House 55, Road 28, Mirpur, Dhaka-1216',
                'phone' => '+880151234567',
                'nid_number' => '5678901234567',
                'email' => '<EMAIL>',
            ],
        ];

        foreach ($landOwners as $owner) {
            LandOwner::create($owner);
        }
    }
}

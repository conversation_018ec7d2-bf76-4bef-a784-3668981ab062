<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LandDocument extends Model
{
    use HasFactory;

    protected $fillable = [
        'land_acquisition_id',
        'document_name',
        'document_file_path',
        'original_filename',
        'file_type',
        'file_size',
        'description',
        'uploaded_by',
    ];

    protected $casts = [
        'land_acquisition_id' => 'integer',
        'file_size' => 'integer',
        'uploaded_by' => 'integer',
    ];

    /**
     * Get the land acquisition that owns the document
     */
    public function landAcquisition(): BelongsTo
    {
        return $this->belongsTo(LandAcquisition::class);
    }

    /**
     * Get the user who uploaded the document
     */
    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the formatted file size
     */
    public function getFormattedFileSizeAttribute(): string
    {
        if (!$this->file_size) {
            return 'Unknown';
        }

        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get the full file URL
     */
    public function getFileUrlAttribute(): string
    {
        if (!$this->document_file_path) {
            return '';
        }

        // If it's already a full URL, return as is
        if (str_starts_with($this->document_file_path, 'http')) {
            return $this->document_file_path;
        }

        // If it starts with /landowners, use the base URL
        if (str_starts_with($this->document_file_path, '/landowners/')) {
            return url($this->document_file_path);
        }

        // Default fallback
        return url('/landowners/documents/' . $this->document_file_path);
    }
}

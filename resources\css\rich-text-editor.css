/* Rich Text Editor Custom Styles */
.rich-text-editor .ql-toolbar {
  border-top: 1px solid #d1d5db !important;
  border-left: 1px solid #d1d5db !important;
  border-right: 1px solid #d1d5db !important;
  border-bottom: none !important;
  border-radius: 6px 6px 0 0 !important;
  background-color: #f9fafb !important;
}

.rich-text-editor .ql-container {
  border-bottom: 1px solid #d1d5db !important;
  border-left: 1px solid #d1d5db !important;
  border-right: 1px solid #d1d5db !important;
  border-top: none !important;
  border-radius: 0 0 6px 6px !important;
  font-family: inherit !important;
}

.rich-text-editor .ql-editor {
  font-size: 14px !important;
  line-height: 1.5 !important;
  padding: 12px 15px !important;
}

.rich-text-editor .ql-editor.ql-blank::before {
  color: #9ca3af !important;
  font-style: normal !important;
}

.rich-text-editor .ql-toolbar.ql-snow .ql-picker-label:hover,
.rich-text-editor .ql-toolbar.ql-snow .ql-picker-item:hover {
  color: #3b82f6 !important;
}

.rich-text-editor .ql-toolbar.ql-snow button:hover {
  color: #3b82f6 !important;
}

.rich-text-editor .ql-toolbar.ql-snow button.ql-active {
  color: #3b82f6 !important;
}

.rich-text-editor .ql-snow .ql-picker.ql-expanded .ql-picker-options {
  z-index: 1000 !important;
}

/* Hide toolbar when disabled */
.rich-text-editor .ql-container.ql-disabled {
  border-radius: 6px !important;
  border: 1px solid #d1d5db !important;
}

.rich-text-editor .ql-toolbar.ql-snow.ql-disabled {
  display: none !important;
}

/* Focus styles */
.rich-text-editor .ql-container.ql-focused {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 1px #3b82f6 !important;
}

.rich-text-editor .ql-toolbar:has(+ .ql-container.ql-focused) {
  border-color: #3b82f6 !important;
}

/* Custom button spacing */
.rich-text-editor .ql-toolbar .ql-formats {
  margin-right: 15px !important;
}

.rich-text-editor .ql-toolbar .ql-formats:last-child {
  margin-right: 0 !important;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .rich-text-editor .ql-toolbar .ql-formats {
    margin-right: 8px !important;
  }
  
  .rich-text-editor .ql-editor {
    padding: 8px 12px !important;
    font-size: 13px !important;
  }
}

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Badge } from '../components/ui/badge';
import ModuleExtensionsSidebar from '../components/ui/ModuleExtensionsSidebar';
import { Package, Info, Eye, Settings } from 'lucide-react';

const ModulesSidebarTestPage = () => {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight">Module Extensions Sidebar</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Test the always-available module extensions sidebar that shows all your system modules
          without requiring controller or model checking.
        </p>
      </div>

      {/* Demo Action */}
      <div className="flex justify-center">
        <Button 
          onClick={() => setSidebarOpen(true)}
          size="lg"
          className="text-lg px-8 py-6"
        >
          <Package className="h-6 w-6 mr-3" />
          Open Module Extensions Sidebar
        </Button>
      </div>

      {/* Features Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mt-12">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5 text-blue-500" />
              Always Available
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              The sidebar is always accessible and shows predefined modules even if the API is down.
              No controller or model checking required.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5 text-green-500" />
              Module Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              View detailed status of each module including controller, model, and Laravel module information.
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-purple-500" />
              Filter & Manage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Filter modules by status (All, Available, Issues, Missing) and access management actions.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Default Modules List */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Info className="h-5 w-5" />
            Predefined Modules (Always Available)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
            {[
              { name: 'Land Owners', key: 'land-owners', laravel: true },
              { name: 'Projects', key: 'projects', laravel: false },
              { name: 'Employees', key: 'employees', laravel: false },
              { name: 'Contractors', key: 'contractors', laravel: false },
              { name: 'Vendors', key: 'vendors', laravel: false },
              { name: 'Vendor Types', key: 'vendor-types', laravel: false },
              { name: 'Units', key: 'units', laravel: false },
              { name: 'Reports', key: 'reports', laravel: false }
            ].map((module) => (
              <div key={module.key} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium text-sm">{module.name}</div>
                  <div className="text-xs text-muted-foreground">{module.key}</div>
                </div>
                <div className="flex gap-1">
                  <Badge variant="default" className="bg-green-100 text-green-800 text-xs">
                    Available
                  </Badge>
                  {module.laravel && (
                    <Badge variant="outline" className="text-xs">
                      Laravel
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle>How to Access</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h4 className="font-medium">Method 1: Header Button</h4>
            <p className="text-sm text-muted-foreground">
              Look for the Package (📦) icon in the top header of your application and click it.
            </p>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium">Method 2: Test Button</h4>
            <p className="text-sm text-muted-foreground">
              Use the "Open Module Extensions Sidebar" button above to test the functionality.
            </p>
          </div>
          <div className="space-y-2">
            <h4 className="font-medium">Method 3: Programmatic</h4>
            <p className="text-sm text-muted-foreground">
              Call <code className="bg-gray-100 px-1 rounded">setModulesSidebarOpen(true)</code> in any component.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Module Extensions Sidebar */}
      <ModuleExtensionsSidebar 
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      />
    </div>
  );
};

export default ModulesSidebarTestPage;

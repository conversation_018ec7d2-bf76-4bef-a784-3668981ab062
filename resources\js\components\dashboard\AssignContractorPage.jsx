import React, { useState, useEffect } from 'react';
import projectContractorAPI from '../../services/projectContractorAPI';
import Swal from 'sweetalert2';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Textarea } from '../ui/textarea';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '../ui/dialog';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { 
  Search, 
  Plus, 
  Edit2, 
  Trash2, 
  Users, 
  Building2, 
  Check, 
  X, 
  Filter, 
  ChevronLeft, 
  ChevronRight,
  DollarSign,
  Calendar,
  UserCheck,
  FileText
} from 'lucide-react';

const AssignContractorPage = () => {
  const [assignments, setAssignments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState({});
  const [projects, setProjects] = useState([]);
  const [contractors, setContractors] = useState([]);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingAssignment, setEditingAssignment] = useState(null);
  
  // Form data
  const [formData, setFormData] = useState({
    project_id: '',
    contractor_id: '',
    start_date: '',
    end_date: '',
    status: 'active',
    contract_amount: '',
    notes: '',
    contract_documents: [],
    existing_documents: []
  });

  // Pagination and filtering state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [projectFilter, setProjectFilter] = useState('all');
  const [contractorFilter, setContractorFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');

  // Fetch assignments
  const fetchAssignments = async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        per_page: perPage,
        search: searchTerm,
        project_id: projectFilter === 'all' ? '' : projectFilter,
        contractor_id: contractorFilter === 'all' ? '' : contractorFilter,
        status: statusFilter === 'all' ? '' : statusFilter,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      const response = await projectContractorAPI.getAssignments(params);
      setAssignments(response.data.data);
      setCurrentPage(response.data.current_page);
      setTotalPages(response.data.last_page);
    } catch (error) {
      console.error('Error fetching assignments:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to fetch contractor assignments',
        timer: 3000,
        showConfirmButton: false
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const response = await projectContractorAPI.getStatistics();
      setStatistics(response.data);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  // Fetch dropdown data
  const fetchDropdownData = async () => {
    try {
      const [projectsResponse, contractorsResponse] = await Promise.all([
        projectContractorAPI.getProjects(),
        projectContractorAPI.getContractors()
      ]);
      
      console.log('Projects response:', projectsResponse);
      console.log('Contractors response:', contractorsResponse);
      setProjects(projectsResponse.data);
      setContractors(contractorsResponse.data);
    } catch (error) {
      console.error('Error fetching dropdown data:', error);
    }
  };

  useEffect(() => {
    // Check authentication status
    const token = localStorage.getItem('auth_token');
    setIsAuthenticated(!!token);
    
    if (token) {
      fetchAssignments(1);
      fetchStatistics();
      fetchDropdownData();
    }
  }, [perPage, searchTerm, projectFilter, contractorFilter, statusFilter, sortBy, sortOrder]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    fetchAssignments(1);
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      project_id: '',
      contractor_id: '',
      start_date: '',
      end_date: '',
      status: 'active',
      contract_amount: '',
      notes: '',
      contract_documents: [],
      existing_documents: []
    });
  };

  // Handle add assignment
  const handleAddAssignment = async (e) => {
    e.preventDefault();
    
    if (!formData.project_id || !formData.contractor_id) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        text: 'Project and Contractor are required',
        timer: 3000,
        showConfirmButton: false
      });
      return;
    }
    
    try {
      const submitData = new FormData();
      
      // Append basic fields
      Object.keys(formData).forEach(key => {
        if (key !== 'contract_documents' && key !== 'existing_documents') {
          // Only append non-empty values to avoid validation issues
          const value = formData[key];
          if (value !== null && value !== undefined && value !== '') {
            submitData.append(key, value);
          }
        }
      });
      
      // Append files
      if (formData.contract_documents?.length > 0) {
        formData.contract_documents.forEach((file) => {
          submitData.append('contract_documents[]', file);
        });
      }

      console.log('Submitting form data:', Object.fromEntries(submitData));
      const response = await projectContractorAPI.createAssignment(submitData);
      console.log('Assignment creation response:', response);
      
      setShowAddModal(false);
      resetForm();
      fetchAssignments();
      fetchStatistics();
      
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: 'Contractor assigned to project successfully',
        timer: 3000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error creating assignment:', error);
      console.error('Error response:', error.response);
      
      let errorMessage = 'Failed to assign contractor';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.errors) {
        const errors = Object.values(error.response.data.errors).flat();
        errorMessage = errors.join(', ');
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: errorMessage,
        timer: 5000,
        showConfirmButton: true
      });
    }
  };

  // Handle edit assignment
  const handleEditAssignment = (assignment) => {
    setEditingAssignment(assignment);
    setFormData({
      project_id: assignment.project_id.toString(),
      contractor_id: assignment.contractor_id.toString(),
      start_date: assignment.start_date || '',
      end_date: assignment.end_date || '',
      status: assignment.status || 'active',
      contract_amount: assignment.contract_amount || '',
      notes: assignment.notes || '',
      contract_documents: [],
      existing_documents: assignment.contract_documents || []
    });
    setShowEditModal(true);
  };

  // Handle update assignment
  const handleUpdateAssignment = async (e) => {
    e.preventDefault();
    
    if (!formData.project_id || !formData.contractor_id) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        text: 'Project and Contractor are required',
        timer: 3000,
        showConfirmButton: false
      });
      return;
    }
    
    try {
      // Check if we have files to upload
      const hasFiles = formData.contract_documents?.length > 0;
      
      if (hasFiles) {
        // Use FormData for file uploads
        const submitData = new FormData();
        
        // Append basic fields
        Object.keys(formData).forEach(key => {
          if (key !== 'contract_documents') {
            if (key === 'existing_documents') {
              submitData.append(key, JSON.stringify(formData[key]));
            } else {
              // Only append non-empty values to avoid validation issues
              const value = formData[key];
              if (value !== null && value !== undefined && value !== '') {
                submitData.append(key, value);
              }
            }
          }
        });
        
        // Append new files
        formData.contract_documents.forEach((file) => {
          submitData.append('contract_documents[]', file);
        });

        await projectContractorAPI.updateAssignment(editingAssignment.id, submitData);
      } else {
        // Use JSON for updates without files
        const submitData = {};
        
        Object.keys(formData).forEach(key => {
          if (key !== 'contract_documents') {
            if (key === 'existing_documents') {
              submitData[key] = formData[key];
            } else {
              // Only include non-empty values
              const value = formData[key];
              if (value !== null && value !== undefined && value !== '') {
                submitData[key] = value;
              }
            }
          }
        });

        await projectContractorAPI.updateAssignmentJSON(editingAssignment.id, submitData);
      }
      setShowEditModal(false);
      setEditingAssignment(null);
      resetForm();
      fetchAssignments();
      fetchStatistics();
      
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: 'Assignment updated successfully',
        timer: 3000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error updating assignment:', error);
      console.error('Error response:', error.response);
      
      let errorMessage = 'Failed to update assignment';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.errors) {
        const errors = Object.values(error.response.data.errors).flat();
        errorMessage = errors.join(', ');
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: errorMessage,
        timer: 5000,
        showConfirmButton: true
      });
    }
  };

  // Handle delete assignment
  const handleDeleteAssignment = async (assignment) => {
    const result = await Swal.fire({
      title: 'Delete Assignment',
      text: `Are you sure you want to delete this contractor assignment? This action cannot be undone.`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'Cancel'
    });

    if (result.isConfirmed) {
      try {
        await projectContractorAPI.deleteAssignment(assignment.id);
        fetchAssignments();
        fetchStatistics();
        
        Swal.fire({
          icon: 'success',
          title: 'Deleted!',
          text: 'Assignment deleted successfully',
          timer: 3000,
          showConfirmButton: false
        });
      } catch (error) {
        console.error('Error deleting assignment:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to delete assignment',
          timer: 3000,
          showConfirmButton: false
        });
      }
    }
  };

  // Handle file change
  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setFormData(prev => ({
      ...prev,
      contract_documents: files
    }));
  };

  // Remove existing document
  const removeExistingDocument = (index) => {
    setFormData(prev => ({
      ...prev,
      existing_documents: prev.existing_documents.filter((_, i) => i !== index)
    }));
  };

  // Get status badge class
  const getStatusBadgeClass = (status) => {
    const classes = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800',
      completed: 'bg-blue-100 text-blue-800',
      terminated: 'bg-red-100 text-red-800'
    };
    return classes[status] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {!isAuthenticated ? (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <X className="w-8 h-8 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-red-900 mb-2">Authentication Required</h3>
              <p className="text-red-700 mb-4">
                You need to be logged in to access the contractor assignment module.
              </p>
              <Button 
                onClick={() => window.location.href = '/login'} 
                className="bg-red-600 hover:bg-red-700"
              >
                Go to Login
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Header */}
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 flex items-center">
                <UserCheck className="w-8 h-8 mr-3 text-blue-600" />
                Assign Contractor
              </h1>
              <p className="text-gray-600 mt-1">Manage contractor assignments to projects</p>
            </div>
            <Button className="bg-blue-600 hover:bg-blue-700" onClick={() => setShowAddModal(true)}>
              <Plus className="w-4 h-4 mr-2" />
              Assign Contractor
            </Button>
          </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-800">Total Assignments</CardTitle>
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <Users className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900">{statistics.total_assignments || 0}</div>
            <p className="text-xs text-blue-600 mt-1">All contractor assignments</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-800">Active Assignments</CardTitle>
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
              <Check className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">{statistics.active_assignments || 0}</div>
            <p className="text-xs text-green-600 mt-1">Currently active</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-800">Completed</CardTitle>
            <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
              <UserCheck className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900">{statistics.completed_assignments || 0}</div>
            <p className="text-xs text-purple-600 mt-1">Completed assignments</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-yellow-800">Total Contract Value</CardTitle>
            <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
              <DollarSign className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-900">
              ${Number(statistics.total_contract_value || 0).toLocaleString()}
            </div>
            <p className="text-xs text-yellow-600 mt-1">Total contract amount</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card className="border-gray-200 shadow-sm">
        <CardHeader className="bg-gray-50 border-b">
          <CardTitle className="flex items-center text-lg">
            <Filter className="w-5 h-5 mr-2 text-blue-600" />
            Search & Filter Assignments
          </CardTitle>
          <CardDescription>
            Find assignments by project, contractor, or status
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-6">
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div>
                <Label htmlFor="search" className="text-sm font-medium text-gray-700">Search</Label>
                <div className="relative mt-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="search"
                    type="text"
                    placeholder="Search assignments..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div>
                <Label htmlFor="project_filter" className="text-sm font-medium text-gray-700">Project</Label>
                <Select value={projectFilter} onValueChange={setProjectFilter}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="All Projects" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Projects</SelectItem>
                    {projects.map((project) => (
                      <SelectItem key={project.id} value={project.id.toString()}>
                        {project.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="contractor_filter" className="text-sm font-medium text-gray-700">Contractor</Label>
                <Select value={contractorFilter} onValueChange={setContractorFilter}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="All Contractors" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Contractors</SelectItem>
                    {contractors.map((contractor) => (
                      <SelectItem key={contractor.id} value={contractor.id.toString()}>
                        {contractor.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="status_filter" className="text-sm font-medium text-gray-700">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="mt-1">
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="terminated">Terminated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="sort_by" className="text-sm font-medium text-gray-700">Sort By</Label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="mt-1">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="created_at">Date Created</SelectItem>
                    <SelectItem value="start_date">Start Date</SelectItem>
                    <SelectItem value="contract_amount">Contract Amount</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="flex gap-2 pt-2">
              <Button type="submit" className="bg-blue-600 hover:bg-blue-700">
                <Search className="w-4 h-4 mr-2" />
                Apply Filters
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  setSearchTerm('');
                  setProjectFilter('all');
                  setContractorFilter('all');
                  setStatusFilter('all');
                  setSortBy('created_at');
                  setSortOrder('desc');
                }}
              >
                Clear Filters
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Assignments Table */}
      <Card>
        <CardHeader>
          <CardTitle>Contractor Assignments</CardTitle>
          <CardDescription>Manage project contractor assignments</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full table-auto">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Project</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Contractor</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Contract Amount</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Start Date</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {assignments.map((assignment) => (
                      <tr key={assignment.id} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <div className="flex-shrink-0">
                              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                                <Building2 className="w-5 h-5 text-blue-600" />
                              </div>
                            </div>
                            <div className="ml-3">
                              <div className="font-medium text-gray-900">{assignment.project?.name || 'N/A'}</div>
                              <div className="text-sm text-gray-500">{assignment.project?.status || ''}</div>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div>
                            <div className="font-medium text-gray-900">{assignment.contractor?.name || 'N/A'}</div>
                            <div className="text-sm text-gray-500">{assignment.contractor?.email || ''}</div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <Badge className={getStatusBadgeClass(assignment.status)}>
                            {assignment.status}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">
                          <div className="text-sm text-gray-900">
                            {assignment.contract_amount ? `$${Number(assignment.contract_amount).toLocaleString()}` : 'N/A'}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="text-sm text-gray-700">
                            {assignment.start_date ? new Date(assignment.start_date).toLocaleDateString() : 'N/A'}
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditAssignment(assignment)}
                              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            >
                              <Edit2 className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteAssignment(assignment)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-gray-700">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchAssignments(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="w-4 h-4" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchAssignments(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Assignment Modal */}
      <Dialog open={showAddModal} onOpenChange={setShowAddModal} modal={true}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto" aria-hidden={false}>
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <UserCheck className="w-5 h-5 mr-2" />
              Assign Contractor to Project
            </DialogTitle>
            <DialogDescription>
              Create a new contractor assignment to a project.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAddAssignment} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="project_id">Project *</Label>
                <Select value={formData.project_id} onValueChange={(value) => setFormData({...formData, project_id: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Project" />
                  </SelectTrigger>
                  <SelectContent>
                    {projects.map((project) => (
                      <SelectItem key={project.id} value={project.id.toString()}>
                        {project.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="contractor_id">Contractor *</Label>
                <Select value={formData.contractor_id} onValueChange={(value) => setFormData({...formData, contractor_id: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Contractor" />
                  </SelectTrigger>
                  <SelectContent>
                    {contractors.map((contractor) => (
                      <SelectItem key={contractor.id} value={contractor.id.toString()}>
                        {contractor.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="terminated">Terminated</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="start_date">Start Date</Label>
                <Input
                  id="start_date"
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => setFormData({...formData, start_date: e.target.value})}
                />
              </div>

              <div>
                <Label htmlFor="end_date">End Date</Label>
                <Input
                  id="end_date"
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => setFormData({...formData, end_date: e.target.value})}
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="contract_amount">Contract Amount</Label>
                <Input
                  id="contract_amount"
                  type="number"
                  step="0.01"
                  value={formData.contract_amount}
                  onChange={(e) => setFormData({...formData, contract_amount: e.target.value})}
                  placeholder="0.00"
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="contract_documents">Work Order</Label>
                <Input
                  id="contract_documents"
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  onChange={handleFileChange}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Accepted formats: PDF, DOC, DOCX, JPG, JPEG, PNG (Max 10MB each)
                </p>
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  placeholder="Additional notes about the assignment..."
                  rows={3}
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowAddModal(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <Plus className="mr-2 h-4 w-4" />
                Assign Contractor
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Assignment Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal} modal={true}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto" aria-hidden={false}>
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <UserCheck className="w-5 h-5 mr-2" />
              Edit Contractor Assignment
            </DialogTitle>
            <DialogDescription>
              Update contractor assignment information.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateAssignment} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit_project_id">Project *</Label>
                <Select value={formData.project_id} onValueChange={(value) => setFormData({...formData, project_id: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Project" />
                  </SelectTrigger>
                  <SelectContent>
                    {projects.map((project) => (
                      <SelectItem key={project.id} value={project.id.toString()}>
                        {project.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="edit_contractor_id">Contractor *</Label>
                <Select value={formData.contractor_id} onValueChange={(value) => setFormData({...formData, contractor_id: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Contractor" />
                  </SelectTrigger>
                  <SelectContent>
                    {contractors.map((contractor) => (
                      <SelectItem key={contractor.id} value={contractor.id.toString()}>
                        {contractor.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

             

              <div>
                <Label htmlFor="edit_status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="terminated">Terminated</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="edit_start_date">Start Date</Label>
                <Input
                  id="edit_start_date"
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => setFormData({...formData, start_date: e.target.value})}
                />
              </div>

              <div>
                <Label htmlFor="edit_end_date">End Date</Label>
                <Input
                  id="edit_end_date"
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => setFormData({...formData, end_date: e.target.value})}
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="edit_contract_amount">Contract Amount</Label>
                <Input
                  id="edit_contract_amount"
                  type="number"
                  step="0.01"
                  value={formData.contract_amount}
                  onChange={(e) => setFormData({...formData, contract_amount: e.target.value})}
                  placeholder="0.00"
                />
              </div>

              {/* Existing Documents */}
              {formData.existing_documents?.length > 0 && (
                <div className="md:col-span-2">
                  <Label>Existing Documents</Label>
                  <div className="mt-2 space-y-2">
                    {formData.existing_documents.map((doc, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm text-gray-700 flex items-center">
                          <FileText className="w-4 h-4 mr-2" />
                          {doc.split('/').pop()}
                        </span>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeExistingDocument(index)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <X className="w-3 h-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className="md:col-span-2">
                <Label htmlFor="edit_contract_documents">Add New Documents</Label>
                <Input
                  id="edit_contract_documents"
                  type="file"
                  multiple
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                  onChange={handleFileChange}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Accepted formats: PDF, DOC, DOCX, JPG, JPEG, PNG (Max 10MB each)
                </p>
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="edit_notes">Notes</Label>
                <Textarea
                  id="edit_notes"
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                  placeholder="Additional notes about the assignment..."
                  rows={3}
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowEditModal(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <Edit2 className="mr-2 h-4 w-4" />
                Update Assignment
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
        </>
      )}
    </div>
  );
};

export default AssignContractorPage;

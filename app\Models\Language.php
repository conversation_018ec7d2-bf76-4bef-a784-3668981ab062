<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Language extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'native_name',
        'flag',
        'direction',
        'status',
        'is_default'
    ];

    protected $casts = [
        'status' => 'string',
        'direction' => 'string',
        'is_default' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    // Static methods
    public static function getDefault()
    {
        return self::where('is_default', true)->first();
    }

    public static function setDefault($id)
    {
        // Remove default from all languages
        self::where('is_default', true)->update(['is_default' => false]);
        
        // Set new default
        return self::where('id', $id)->update(['is_default' => true]);
    }

    // Accessors
    public function getFullNameAttribute()
    {
        return $this->name . ' (' . $this->code . ')';
    }

    public function getDisplayNameAttribute()
    {
        return $this->native_name ?: $this->name;
    }
}

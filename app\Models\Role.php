<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Role extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'accessible_modules',
        'module_permissions',
        'status',
        'users_count'
    ];

    protected $casts = [
        'accessible_modules' => 'array',
        'module_permissions' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    // Accessors
    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->format('Y-m-d');
    }

    public function getFormattedUpdatedAtAttribute()
    {
        return $this->updated_at->format('Y-m-d');
    }

    // Helper methods
    public function hasModuleAccess($moduleKey)
    {
        return in_array($moduleKey, $this->accessible_modules ?? []);
    }

    public function getModulePermissions($moduleKey)
    {
        return $this->module_permissions[$moduleKey] ?? [];
    }

    public function hasPermission($moduleKey, $permission)
    {
        $modulePermissions = $this->getModulePermissions($moduleKey);
        return in_array($permission, $modulePermissions);
    }

    // Relationships (if needed in the future)
    public function users()
    {
        return $this->hasMany(User::class);
    }
}

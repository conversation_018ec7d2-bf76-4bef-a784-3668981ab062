import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import MetricCard from './widgets/MetricCard';
import RecentActivity from './widgets/RecentActivity';
import {
  TrendingUp,
  Users,
  ShoppingCart,
  DollarSign,
  Activity,
  Download,
  Calendar
} from 'lucide-react';
import {
  Line,
  LineChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
} from 'recharts';

const AnalyticsPage = () => {
  // Website traffic data for the last 30 days
  const trafficData = [
    { name: 'Week 1', visitors: 2400, pageViews: 4800 },
    { name: 'Week 2', visitors: 1398, pageViews: 3200 },
    { name: 'Week 3', visitors: 9800, pageViews: 12000 },
    { name: 'Week 4', visitors: 3908, pageViews: 7800 },
    { name: 'Week 5', visitors: 4800, pageViews: 9600 },
    { name: 'Week 6', visitors: 3800, pageViews: 7200 },
    { name: 'Week 7', visitors: 4300, pageViews: 8600 },
  ];

  const metrics = [
    {
      title: "Total Users",
      value: "54,239",
      change: "+12.5%",
      trend: "up",
      icon: Users,
      description: "from last month"
    },
    {
      title: "Conversion Rate",
      value: "3.24%",
      change: "+0.4%",
      trend: "up",
      icon: TrendingUp,
      description: "from last month"
    },
    {
      title: "Average Order",
      value: "$127.50",
      change: "-2.1%",
      trend: "down",
      icon: ShoppingCart,
      description: "from last month"
    },
    {
      title: "Session Duration",
      value: "4m 32s",
      change: "+8.2%",
      trend: "up",
      icon: Activity,
      description: "from last month"
    }
  ];

  const topPages = [
    { page: "/dashboard", views: "12,543", bounce: "23.4%" },
    { page: "/products", views: "8,921", bounce: "31.2%" },
    { page: "/checkout", views: "5,432", bounce: "45.1%" },
    { page: "/profile", views: "3,210", bounce: "28.7%" },
    { page: "/support", views: "2,876", bounce: "52.3%" }
  ];

  const trafficSources = [
    { source: "Organic Search", visitors: "45,231", percentage: "42.3%" },
    { source: "Direct", visitors: "23,456", percentage: "21.9%" },
    { source: "Social Media", visitors: "18,765", percentage: "17.5%" },
    { source: "Email", visitors: "12,543", percentage: "11.7%" },
    { source: "Referral", visitors: "6,789", percentage: "6.3%" }
  ];

  return (
    <div className="space-y-6">
      {/* Page header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Analytics</h1>
          <p className="text-muted-foreground">
            Detailed insights into your website performance and user behavior.
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Calendar className="mr-2 h-4 w-4" />
            Last 30 days
          </Button>
          <Button>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Metrics grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {metrics.map((metric, index) => (
          <MetricCard key={index} {...metric} />
        ))}
      </div>

      {/* Charts and tables */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        {/* Traffic Chart */}
        <Card className="col-span-4">
          <CardHeader>
            <CardTitle>Website Traffic</CardTitle>
            <CardDescription>
              Visitors and page views over the last 7 weeks
            </CardDescription>
          </CardHeader>
          <CardContent className="pl-2">
            <ResponsiveContainer width="100%" height={300}>
              <LineChart
                data={trafficData}
                margin={{
                  top: 10,
                  right: 30,
                  left: 0,
                  bottom: 0,
                }}
              >
                <XAxis
                  dataKey="name"
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                />
                <YAxis
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: 'hsl(var(--muted-foreground))' }}
                />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="rounded-lg border bg-background p-2 shadow-sm">
                          <div className="grid grid-cols-1 gap-2">
                            <div className="flex flex-col">
                              <span className="text-[0.70rem] uppercase text-muted-foreground">
                                {label}
                              </span>
                              <span className="font-bold text-blue-600">
                                Visitors: {payload[0]?.value}
                              </span>
                              <span className="font-bold text-green-600">
                                Page Views: {payload[1]?.value}
                              </span>
                            </div>
                          </div>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Line
                  type="monotone"
                  dataKey="visitors"
                  stroke="hsl(var(--primary))"
                  strokeWidth={2}
                  dot={{ fill: 'hsl(var(--primary))' }}
                />
                <Line
                  type="monotone"
                  dataKey="pageViews"
                  stroke="hsl(var(--secondary))"
                  strokeWidth={2}
                  dot={{ fill: 'hsl(var(--secondary))' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Recent activity */}
        <div className="col-span-3">
          <RecentActivity />
        </div>
      </div>

      {/* Additional analytics */}
      <div className="grid gap-4 md:grid-cols-2">
        {/* Top pages */}
        <Card>
          <CardHeader>
            <CardTitle>Top Pages</CardTitle>
            <CardDescription>
              Most visited pages this month
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topPages.map((page, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{page.page}</p>
                    <p className="text-sm text-muted-foreground">{page.views} views</p>
                  </div>
                  <Badge variant="outline">{page.bounce} bounce</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Traffic sources */}
        <Card>
          <CardHeader>
            <CardTitle>Traffic Sources</CardTitle>
            <CardDescription>
              Where your visitors are coming from
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {trafficSources.map((source, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{source.source}</p>
                    <p className="text-sm text-muted-foreground">{source.visitors} visitors</p>
                  </div>
                  <Badge>{source.percentage}</Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AnalyticsPage;

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            // Add the new property_type_id column
            $table->unsignedBigInteger('property_type_id')->nullable()->after('property_type');
            
            // Add foreign key constraint
            $table->foreign('property_type_id')->references('id')->on('property_types')->onDelete('set null');
            
            // Add index for better performance
            $table->index('property_type_id');
        });

        // Migrate existing data - map enum values to property_type IDs
        $propertyTypeMapping = [
            'residential' => 1, // Assuming these IDs exist in property_types table
            'commercial' => 2,
            'industrial' => 3,
            'land' => 4,
            'mixed' => 5,
        ];

        foreach ($propertyTypeMapping as $enumValue => $propertyTypeId) {
            DB::table('projects')
                ->where('property_type', $enumValue)
                ->update(['property_type_id' => $propertyTypeId]);
        }

        // Remove the old enum column after migration
        Schema::table('projects', function (Blueprint $table) {
            $table->dropColumn('property_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            // Add back the enum column
            $table->enum('property_type', ['residential', 'commercial', 'industrial', 'land', 'mixed'])->after('description');
        });

        // Migrate data back to enum values
        $propertyTypeMapping = [
            1 => 'residential',
            2 => 'commercial', 
            3 => 'industrial',
            4 => 'land',
            5 => 'mixed',
        ];

        foreach ($propertyTypeMapping as $propertyTypeId => $enumValue) {
            DB::table('projects')
                ->where('property_type_id', $propertyTypeId)
                ->update(['property_type' => $enumValue]);
        }

        Schema::table('projects', function (Blueprint $table) {
            // Remove foreign key and column
            $table->dropForeign(['property_type_id']);
            $table->dropIndex(['property_type_id']);
            $table->dropColumn('property_type_id');
        });
    }
};

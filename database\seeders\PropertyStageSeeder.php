<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PropertyStage;
use Illuminate\Support\Str;

class PropertyStageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Starting Property Stage seeding...');

        $stages = [
            [
                'name' => 'Pre-Planning',
                'slug' => 'pre_planning',
                'description' => 'Initial project conception and feasibility studies',
                'color' => '#EF4444',
                'icon' => 'Lightbulb',
                'status' => 'active',
                'sort_order' => 1,
                'completion_percentage' => 5.0,
                'metadata' => [
                    'typical_duration' => '2-4 weeks',
                    'key_activities' => ['Market research', 'Site analysis', 'Initial budgeting']
                ]
            ],
            [
                'name' => 'Planning',
                'slug' => 'planning',
                'description' => 'Detailed project planning and design development',
                'color' => '#F97316',
                'icon' => 'ClipboardList',
                'status' => 'active',
                'sort_order' => 2,
                'completion_percentage' => 10.0,
                'metadata' => [
                    'typical_duration' => '4-8 weeks',
                    'key_activities' => ['Architectural planning', 'Engineering design', 'Permit applications']
                ]
            ],
            [
                'name' => 'Design',
                'slug' => 'design',
                'description' => 'Architectural and engineering design phase',
                'color' => '#F59E0B',
                'icon' => 'PencilRuler',
                'status' => 'active',
                'sort_order' => 3,
                'completion_percentage' => 15.0,
                'metadata' => [
                    'typical_duration' => '6-12 weeks',
                    'key_activities' => ['Detailed drawings', 'Specifications', 'Material selection']
                ]
            ],
            [
                'name' => 'Approvals',
                'slug' => 'approvals',
                'description' => 'Obtaining necessary permits and approvals',
                'color' => '#EAB308',
                'icon' => 'CheckCircle',
                'status' => 'active',
                'sort_order' => 4,
                'completion_percentage' => 20.0,
                'metadata' => [
                    'typical_duration' => '4-16 weeks',
                    'key_activities' => ['Building permits', 'Environmental clearances', 'Zoning approvals']
                ]
            ],
            [
                'name' => 'Pre-Construction',
                'slug' => 'pre_construction',
                'description' => 'Site preparation and pre-construction activities',
                'color' => '#84CC16',
                'icon' => 'Truck',
                'status' => 'active',
                'sort_order' => 5,
                'completion_percentage' => 25.0,
                'metadata' => [
                    'typical_duration' => '2-6 weeks',
                    'key_activities' => ['Site clearing', 'Utility connections', 'Material procurement']
                ]
            ],
            [
                'name' => 'Foundation',
                'slug' => 'foundation',
                'description' => 'Foundation and underground work',
                'color' => '#22C55E',
                'icon' => 'Home',
                'status' => 'active',
                'sort_order' => 6,
                'completion_percentage' => 35.0,
                'metadata' => [
                    'typical_duration' => '3-8 weeks',
                    'key_activities' => ['Excavation', 'Foundation pouring', 'Basement construction']
                ]
            ],
            [
                'name' => 'Structure',
                'slug' => 'structure',
                'description' => 'Main structural construction',
                'color' => '#10B981',
                'icon' => 'Building',
                'status' => 'active',
                'sort_order' => 7,
                'completion_percentage' => 50.0,
                'metadata' => [
                    'typical_duration' => '8-16 weeks',
                    'key_activities' => ['Frame construction', 'Floor systems', 'Load-bearing elements']
                ]
            ],
            [
                'name' => 'Roofing',
                'slug' => 'roofing',
                'description' => 'Roof construction and weatherproofing',
                'color' => '#14B8A6',
                'icon' => 'Shield',
                'status' => 'active',
                'sort_order' => 8,
                'completion_percentage' => 60.0,
                'metadata' => [
                    'typical_duration' => '2-4 weeks',
                    'key_activities' => ['Roof structure', 'Waterproofing', 'Insulation']
                ]
            ],
            [
                'name' => 'Exterior Work',
                'slug' => 'exterior',
                'description' => 'External finishing and landscaping',
                'color' => '#06B6D4',
                'icon' => 'TreePine',
                'status' => 'active',
                'sort_order' => 9,
                'completion_percentage' => 70.0,
                'metadata' => [
                    'typical_duration' => '4-8 weeks',
                    'key_activities' => ['Exterior cladding', 'Windows and doors', 'Landscaping']
                ]
            ],
            [
                'name' => 'Interior Work',
                'slug' => 'interior',
                'description' => 'Interior construction and systems',
                'color' => '#0EA5E9',
                'icon' => 'Sofa',
                'status' => 'active',
                'sort_order' => 10,
                'completion_percentage' => 80.0,
                'metadata' => [
                    'typical_duration' => '6-12 weeks',
                    'key_activities' => ['Electrical systems', 'Plumbing', 'HVAC installation']
                ]
            ],
            [
                'name' => 'Finishing',
                'slug' => 'finishing',
                'description' => 'Final finishes and fixtures',
                'color' => '#3B82F6',
                'icon' => 'Paintbrush',
                'status' => 'active',
                'sort_order' => 11,
                'completion_percentage' => 90.0,
                'metadata' => [
                    'typical_duration' => '4-8 weeks',
                    'key_activities' => ['Painting', 'Flooring', 'Fixtures installation']
                ]
            ],
            [
                'name' => 'Inspection',
                'slug' => 'inspection',
                'description' => 'Quality control and regulatory inspections',
                'color' => '#6366F1',
                'icon' => 'Search',
                'status' => 'active',
                'sort_order' => 12,
                'completion_percentage' => 95.0,
                'metadata' => [
                    'typical_duration' => '1-3 weeks',
                    'key_activities' => ['Building inspections', 'Quality checks', 'Safety compliance']
                ]
            ],
            [
                'name' => 'Completed',
                'slug' => 'completed',
                'description' => 'Project construction completed',
                'color' => '#8B5CF6',
                'icon' => 'CheckBadge',
                'status' => 'active',
                'sort_order' => 13,
                'completion_percentage' => 98.0,
                'metadata' => [
                    'typical_duration' => '1 week',
                    'key_activities' => ['Final walkthrough', 'Punch list completion', 'Documentation']
                ]
            ],
            [
                'name' => 'Handover',
                'slug' => 'handover',
                'description' => 'Project handover to client',
                'color' => '#A855F7',
                'icon' => 'HandRaised',
                'status' => 'active',
                'sort_order' => 14,
                'completion_percentage' => 99.0,
                'metadata' => [
                    'typical_duration' => '1-2 weeks',
                    'key_activities' => ['Key handover', 'Documentation transfer', 'Training']
                ]
            ],
            [
                'name' => 'Warranty Period',
                'slug' => 'warranty',
                'description' => 'Post-completion warranty and maintenance',
                'color' => '#C084FC',
                'icon' => 'ShieldCheck',
                'status' => 'active',
                'sort_order' => 15,
                'completion_percentage' => 100.0,
                'metadata' => [
                    'typical_duration' => '12 months',
                    'key_activities' => ['Warranty support', 'Maintenance', 'Issue resolution']
                ]
            ]
        ];

        foreach ($stages as $stageData) {
            // Check if stage already exists
            $existingStage = PropertyStage::where('slug', $stageData['slug'])->first();

            if (!$existingStage) {
                PropertyStage::create($stageData);
                $this->command->info("Created property stage: {$stageData['name']}");
            } else {
                $this->command->info("Property stage already exists: {$stageData['name']}");
            }
        }

        $this->command->info('Property Stage seeding completed!');
    }
}

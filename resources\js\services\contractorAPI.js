import api from '../config/api';

const contractorAPI = {
  // Get all contractors with filters
  getContractors: (params = {}) => {
    return api.get('/contractors', { params });
  },

  // Get contractor by ID
  getContractor: (id) => {
    return api.get(`/contractors/${id}`);
  },

  // Create new contractor
  createContractor: (data) => {
    return api.post('/contractors', data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Update contractor
  updateContractor: (id, data) => {
    // Append the _method field for Laravel method spoofing
    data.append('_method', 'PUT');
    
    return api.post(`/contractors/${id}`, data, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Delete contractor
  deleteContractor: (id) => {
    return api.delete(`/contractors/${id}`);
  },

  // Get contractors for dropdown
  getContractorsDropdown: () => {
    return api.get('/contractors-dropdown');
  },

  // Get contractor statistics
  getContractorStatistics: () => {
    return api.get('/contractors-statistics');
  },

  // Bulk status update
  bulkStatusUpdate: (contractorIds, status) => {
    return api.post('/contractors/bulk-status-update', {
      contractor_ids: contractorIds,
      status: status
    });
  }
};

export default contractorAPI;

import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  Plus, Search, Edit2, Trash2, Eye, ToggleLeft, ToggleRight, 
  Move, Save, X, Palette, Hash, <PERSON>, FileText, Package,
  CheckCircle, AlertCircle, Clock, DollarSign, Key, Wrench
} from 'lucide-react';
import { showAlert, showAlertMethods as showConfirmation } from '@/utils/sweetAlert';
import propertyStatusAPI from '@/services/propertyStatusAPI';

const PropertyStatusManagement = () => {
  const [propertyStatuses, setPropertyStatuses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedStatus, setSelectedStatus] = useState(null);
  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    color: '#3B82F6',
    icon: '',
    status: 'active',
    sort_order: 0,
    metadata: {}
  });

  // Icon options
  const iconOptions = [
    { value: 'ClipboardList', label: 'Planning', icon: Clock },
    { value: 'Hammer', label: 'Construction', icon: AlertCircle },
    { value: 'CheckCircle', label: 'Completed', icon: CheckCircle },
    { value: 'DollarSign', label: 'Sold', icon: DollarSign },
    { value: 'Key', label: 'Rented', icon: Key },
    { value: 'Wrench', label: 'Maintenance', icon: Wrench },
    { value: 'Package', label: 'Package', icon: Package },
    { value: 'FileText', label: 'Document', icon: FileText },
  ];

  // Color options
  const colorOptions = [
    '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', 
    '#6B7280', '#F97316', '#EC4899', '#14B8A6', '#84CC16'
  ];

  useEffect(() => {
    fetchPropertyStatuses();
  }, [currentPage, searchTerm, statusFilter]);

  const fetchPropertyStatuses = async () => {
    setLoading(true);
    try {
      const params = {
        page: currentPage,
        per_page: 15,
        search: searchTerm || undefined,
        status: statusFilter !== 'all' ? statusFilter : undefined,
        include_projects_count: true
      };

      const response = await propertyStatusAPI.getAll(params);
      
      if (response.success) {
        setPropertyStatuses(response.data.data || []);
        setTotalPages(Math.ceil((response.data.total || 0) / (response.data.per_page || 15)));
      }
    } catch (error) {
      console.error('Error fetching property statuses:', error);
      showAlert('Error', 'Failed to fetch property statuses', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => {
      const updated = { ...prev, [field]: value };
      
      // Auto-generate slug when name changes
      if (field === 'name' && !prev.slug) {
        updated.slug = value.toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '_')
          .replace(/-+/g, '_');
      }
      
      return updated;
    });
  };

  const resetForm = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      color: '#3B82F6',
      icon: '',
      status: 'active',
      sort_order: 0,
      metadata: {}
    });
    setSelectedStatus(null);
  };

  const openModal = (mode, status = null) => {
    setModalMode(mode);
    setSelectedStatus(status);
    
    if (status && (mode === 'edit' || mode === 'view')) {
      setFormData({
        name: status.name || '',
        slug: status.slug || '',
        description: status.description || '',
        color: status.color || '#3B82F6',
        icon: status.icon || '',
        status: status.status || 'active',
        sort_order: status.sort_order || 0,
        metadata: status.metadata || {}
      });
    } else {
      resetForm();
    }
    
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    resetForm();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      let response;
      const submitData = { ...formData };

      if (modalMode === 'create') {
        response = await propertyStatusAPI.create(submitData);
      } else {
        response = await propertyStatusAPI.update(selectedStatus.id, submitData);
      }

      if (response.success) {
        showAlert(
          'Success', 
          `Property status ${modalMode === 'create' ? 'created' : 'updated'} successfully!`, 
          'success'
        );
        closeModal();
        fetchPropertyStatuses();
      }
    } catch (error) {
      console.error('Error saving property status:', error);
      showAlert('Error', error.message || 'Failed to save property status', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (status) => {
    try {
      const newStatus = status.status === 'active' ? 'inactive' : 'active';
      const result = await showConfirmation.confirm(
        'Toggle Status',
        `Are you sure you want to ${newStatus === 'active' ? 'activate' : 'deactivate'} "${status.name}"?`,
        'Yes, do it!',
        'Cancel'
      );

      if (result.isConfirmed) {
        const response = await propertyStatusAPI.toggleStatus(status.id);
        if (response.success) {
          showAlert('Success', `Property status ${newStatus} successfully!`, 'success');
          fetchPropertyStatuses();
        }
      }
    } catch (error) {
      console.error('Error toggling status:', error);
      showAlert('Error', 'Failed to toggle status', 'error');
    }
  };

  const handleDelete = async (status) => {
    try {
      const result = await showConfirmation.confirmDelete(status.name);

      if (result.isConfirmed) {
        const response = await propertyStatusAPI.delete(status.id);
        if (response.success) {
          showAlert('Success', 'Property status deleted successfully!', 'success');
          fetchPropertyStatuses();
        }
      }
    } catch (error) {
      console.error('Error deleting property status:', error);
      showAlert('Error', error.message || 'Failed to delete property status', 'error');
    }
  };

  const getIconComponent = (iconName) => {
    const iconMap = {
      ClipboardList: Clock,
      Hammer: AlertCircle,
      CheckCircle: CheckCircle,
      DollarSign: DollarSign,
      Key: Key,
      Wrench: Wrench,
      Package: Package,
      FileText: FileText,
    };
    return iconMap[iconName] || Package;
  };

  const filteredStatuses = propertyStatuses.filter(status => {
    const matchesSearch = status.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         status.slug.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || status.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Property Status Management</h1>
          <p className="text-gray-600 mt-1">Manage property statuses for your real estate projects</p>
        </div>
        <Button onClick={() => openModal('create')} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Property Status
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search property statuses..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Property Statuses Table */}
      <Card>
        <CardHeader>
          <CardTitle>Property Statuses ({filteredStatuses.length})</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Status</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Slug</TableHead>
                    <TableHead>Projects</TableHead>
                    <TableHead>Sort Order</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredStatuses.map((status) => {
                    const IconComponent = getIconComponent(status.icon);
                    return (
                      <TableRow key={status.id}>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-4 h-4 rounded-full" 
                              style={{ backgroundColor: status.color }}
                            ></div>
                            <IconComponent className="h-4 w-4" style={{ color: status.color }} />
                          </div>
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{status.name}</div>
                            {status.description && (
                              <div className="text-sm text-gray-500 truncate max-w-xs">
                                {status.description}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                            {status.slug}
                          </code>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {status.projects_count || 0} projects
                          </Badge>
                        </TableCell>
                        <TableCell>{status.sort_order}</TableCell>
                        <TableCell>
                          <Badge variant={status.status === 'active' ? 'default' : 'secondary'}>
                            {status.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openModal('view', status)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => openModal('edit', status)}
                            >
                              <Edit2 className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleToggleStatus(status)}
                            >
                              {status.status === 'active' ? (
                                <ToggleRight className="h-4 w-4 text-green-600" />
                              ) : (
                                <ToggleLeft className="h-4 w-4 text-gray-400" />
                              )}
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDelete(status)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>

              {filteredStatuses.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No property statuses found
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Create/Edit Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {modalMode === 'create' ? 'Create Property Status' : 
               modalMode === 'edit' ? 'Edit Property Status' : 'View Property Status'}
            </DialogTitle>
          </DialogHeader>

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter status name"
                  required
                  disabled={modalMode === 'view'}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="slug">Slug *</Label>
                <Input
                  id="slug"
                  value={formData.slug}
                  onChange={(e) => handleInputChange('slug', e.target.value)}
                  placeholder="status-slug"
                  required
                  disabled={modalMode === 'view'}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Enter status description"
                rows={3}
                disabled={modalMode === 'view'}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="color">Color</Label>
                <div className="flex gap-2">
                  <Input
                    id="color"
                    type="color"
                    value={formData.color}
                    onChange={(e) => handleInputChange('color', e.target.value)}
                    className="w-20 h-10 p-1 border rounded"
                    disabled={modalMode === 'view'}
                  />
                  <div className="flex gap-1 flex-wrap">
                    {colorOptions.map((color) => (
                      <button
                        key={color}
                        type="button"
                        className="w-8 h-8 rounded border-2 border-gray-300 hover:border-gray-500"
                        style={{ backgroundColor: color }}
                        onClick={() => handleInputChange('color', color)}
                        disabled={modalMode === 'view'}
                      />
                    ))}
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="icon">Icon</Label>
                <Select 
                  value={formData.icon} 
                  onValueChange={(value) => handleInputChange('icon', value)}
                  disabled={modalMode === 'view'}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an icon" />
                  </SelectTrigger>
                  <SelectContent>
                    {iconOptions.map((option) => {
                      const IconComponent = option.icon;
                      return (
                        <SelectItem key={option.value} value={option.value}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="h-4 w-4" />
                            {option.label}
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select 
                  value={formData.status} 
                  onValueChange={(value) => handleInputChange('status', value)}
                  disabled={modalMode === 'view'}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="sort_order">Sort Order</Label>
                <Input
                  id="sort_order"
                  type="number"
                  value={formData.sort_order}
                  onChange={(e) => handleInputChange('sort_order', parseInt(e.target.value) || 0)}
                  placeholder="0"
                  min="0"
                  disabled={modalMode === 'view'}
                />
              </div>
            </div>

            {/* Preview */}
            <div className="p-4 bg-gray-50 rounded-lg">
              <Label>Preview</Label>
              <div className="mt-2 flex items-center gap-3">
                <div 
                  className="w-6 h-6 rounded-full" 
                  style={{ backgroundColor: formData.color }}
                ></div>
                {formData.icon && (
                  <div className="w-6 h-6" style={{ color: formData.color }}>
                    {React.createElement(getIconComponent(formData.icon), { className: "h-6 w-6" })}
                  </div>
                )}
                <Badge style={{ backgroundColor: formData.color, color: 'white' }}>
                  {formData.name || 'Status Name'}
                </Badge>
              </div>
            </div>

            {modalMode !== 'view' && (
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={closeModal}>
                  Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                  {loading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  ) : (
                    <Save className="h-4 w-4 mr-2" />
                  )}
                  {modalMode === 'create' ? 'Create' : 'Update'}
                </Button>
              </div>
            )}
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PropertyStatusManagement;

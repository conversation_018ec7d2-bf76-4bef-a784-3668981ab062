import React from 'react';
import { 
    X,
    Info,
    Building,
    Home,
    Tag,
    Square,
    BedDouble,
    Droplets
} from 'lucide-react';
import {
    ContentModal,
    ContentModalContent,
    ContentModalHeader,
    ContentModalTitle,
    ContentModalOverlay,
} from '@/components/ui/content-modal';

const UnitTypePreviewModal = ({ isOpen, onClose, unitType }) => {
    if (!unitType) return null;

    return (
        <ContentModal open={isOpen} onOpenChange={onClose}>
            <ContentModalOverlay />
            <ContentModalContent className="w-full max-w-2xl max-h-[90vh] overflow-hidden">
                {/* Header */}
                <ContentModalHeader className="p-6 border-b bg-gradient-to-r from-blue-50 to-indigo-50">
                    <div className="flex items-center gap-3">
                        <div className="p-2 bg-blue-100 rounded-lg">
                            <Info className="w-6 h-6 text-blue-600" />
                        </div>
                        <div>
                            <ContentModalTitle className="text-xl font-semibold text-gray-900">
                                Unit Type Details
                            </ContentModalTitle>
                            <p className="text-sm text-gray-500">
                                {unitType.type_name} specifications
                            </p>
                        </div>
                    </div>
                </ContentModalHeader>

                {/* Content */}
                <div className="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                    <div className="space-y-6">
                        {/* Basic Info */}
                        <div className="bg-gray-50 rounded-lg p-4">
                            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <Building className="w-5 h-5 mr-2 text-blue-600" />
                                Basic Information
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Type Name
                                    </label>
                                    <p className="text-lg font-semibold text-gray-900">
                                        {unitType.type_name}
                                    </p>
                                </div>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Status
                                    </label>
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                        unitType.is_active 
                                            ? 'bg-green-100 text-green-800' 
                                            : 'bg-red-100 text-red-800'
                                    }`}>
                                        {unitType.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </div>
                            </div>
                        </div>

                        {/* Size & Layout */}
                        <div className="bg-gray-50 rounded-lg p-4">
                            <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                <Square className="w-5 h-5 mr-2 text-purple-600" />
                                Size & Layout
                            </h3>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {unitType.total_size_sqft && (
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Total Size
                                        </label>
                                        <p className="text-gray-900">
                                            {unitType.total_size_sqft} sq ft
                                        </p>
                                    </div>
                                )}
                                {unitType.flat_size && (
                                    <div>
                                        <label className="block text-sm font-medium text-gray-700 mb-1">
                                            Flat Size
                                        </label>
                                        <p className="text-gray-900">
                                            {unitType.flat_size} sq ft
                                        </p>
                                    </div>
                                )}
                                {unitType.total_bed_room && (
                                    <div className="flex items-center gap-2">
                                        <BedDouble className="w-4 h-4 text-gray-600" />
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Bedrooms
                                            </label>
                                            <p className="text-gray-900">
                                                {unitType.total_bed_room}
                                            </p>
                                        </div>
                                    </div>
                                )}
                                {unitType.total_bathroom && (
                                    <div className="flex items-center gap-2">
                                        <Droplets className="w-4 h-4 text-gray-600" />
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                                Bathrooms
                                            </label>
                                            <p className="text-gray-900">
                                                {unitType.total_bathroom}
                                            </p>
                                        </div>
                                    </div>
                                )}
                            </div>
                        </div>

                        {/* Pricing */}
                        {unitType.base_price && (
                            <div className="bg-gray-50 rounded-lg p-4">
                                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                    <Tag className="w-5 h-5 mr-2 text-green-600" />
                                    Pricing
                                </h3>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Base Price
                                    </label>
                                    <p className="text-2xl font-bold text-green-600">
                                        ৳{Number(unitType.base_price).toLocaleString()}
                                    </p>
                                </div>
                            </div>
                        )}

                        {/* Description */}
                        {unitType.description && (
                            <div className="bg-gray-50 rounded-lg p-4">
                                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                    <Home className="w-5 h-5 mr-2 text-orange-600" />
                                    Description
                                </h3>
                                <p className="text-gray-700 leading-relaxed">
                                    {unitType.description}
                                </p>
                            </div>
                        )}

                        {/* Project Info */}
                        {unitType.project && (
                            <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                                <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
                                    <Building className="w-5 h-5 mr-2 text-blue-600" />
                                    Project Information
                                </h3>
                                <div>
                                    <label className="block text-sm font-medium text-gray-700 mb-1">
                                        Project Name
                                    </label>
                                    <p className="text-gray-900 font-medium">
                                        {unitType.project.project_name}
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            </ContentModalContent>
        </ContentModal>
    );
};

export default UnitTypePreviewModal;

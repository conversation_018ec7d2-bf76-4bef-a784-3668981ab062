<?php

namespace App\Http\Controllers;

use App\Models\PropertyStage;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PropertyStageController extends Controller
{
    /**
     * Display a listing of property stages with filtering and pagination
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = PropertyStage::query();

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            // Apply sorting
            $sortBy = $request->get('sort_by', 'sort_order');
            $sortOrder = $request->get('sort_order', 'asc');

            if (in_array($sortBy, ['name', 'status', 'sort_order', 'completion_percentage', 'created_at'])) {
                $query->orderBy($sortBy, $sortOrder);
            } else {
                $query->ordered(); // Use the model's default ordering
            }

            // Get pagination parameters
            $perPage = $request->get('per_page', 15);
            $perPage = min($perPage, 100); // Limit max per page

            // Load relationships and get paginated results
            $stages = $query->withCount('projects')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $stages->items(),
                'pagination' => [
                    'current_page' => $stages->currentPage(),
                    'last_page' => $stages->lastPage(),
                    'per_page' => $stages->perPage(),
                    'total' => $stages->total(),
                    'from' => $stages->firstItem(),
                    'to' => $stages->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property stages',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Store a newly created property stage
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:property_stages,name',
                'slug' => 'nullable|string|max:100|unique:property_stages,slug',
                'description' => 'nullable|string|max:1000',
                'color' => 'nullable|string|regex:/^#[A-Fa-f0-9]{6}$/',
                'icon' => 'nullable|string|max:50',
                'status' => 'required|in:active,inactive',
                'sort_order' => 'nullable|integer|min:0',
                'completion_percentage' => 'nullable|numeric|min:0|max:100',
                'metadata' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = Str::slug($data['name']);

                // Ensure slug uniqueness
                $originalSlug = $data['slug'];
                $counter = 1;
                while (PropertyStage::where('slug', $data['slug'])->exists()) {
                    $data['slug'] = $originalSlug . '-' . $counter;
                    $counter++;
                }
            }

            // Set default sort_order if not provided
            if (!isset($data['sort_order'])) {
                $data['sort_order'] = PropertyStage::max('sort_order') + 1;
            }

            $stage = PropertyStage::create($data);

            return response()->json([
                'success' => true,
                'message' => 'Property stage created successfully',
                'data' => $stage->load('projects:id,title,property_stage')
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create property stage',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Display the specified property stage
     */
    public function show(PropertyStage $propertyStage): JsonResponse
    {
        try {
            $propertyStage->load(['projects:id,title,property_stage']);
            $propertyStage->loadCount('projects');

            return response()->json([
                'success' => true,
                'data' => $propertyStage
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property stage',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Update the specified property stage
     */
    public function update(Request $request, PropertyStage $propertyStage): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:property_stages,name,' . $propertyStage->id,
                'slug' => 'nullable|string|max:100|unique:property_stages,slug,' . $propertyStage->id,
                'description' => 'nullable|string|max:1000',
                'color' => 'nullable|string|regex:/^#[A-Fa-f0-9]{6}$/',
                'icon' => 'nullable|string|max:50',
                'status' => 'required|in:active,inactive',
                'sort_order' => 'nullable|integer|min:0',
                'completion_percentage' => 'nullable|numeric|min:0|max:100',
                'metadata' => 'nullable|array',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Generate slug if name changed and slug not provided
            if ($propertyStage->name !== $data['name'] && empty($data['slug'])) {
                $data['slug'] = Str::slug($data['name']);

                // Ensure slug uniqueness
                $originalSlug = $data['slug'];
                $counter = 1;
                while (PropertyStage::where('slug', $data['slug'])->where('id', '!=', $propertyStage->id)->exists()) {
                    $data['slug'] = $originalSlug . '-' . $counter;
                    $counter++;
                }
            }

            $propertyStage->update($data);
            $propertyStage->load(['projects:id,title,property_stage']);

            return response()->json([
                'success' => true,
                'message' => 'Property stage updated successfully',
                'data' => $propertyStage
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update property stage',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Remove the specified property stage
     */
    public function destroy(PropertyStage $propertyStage): JsonResponse
    {
        try {
            // Check if stage is being used by any projects
            $projectsCount = $propertyStage->projects()->count();

            if ($projectsCount > 0) {
                return response()->json([
                    'success' => false,
                    'message' => "Cannot delete property stage. It is currently being used by {$projectsCount} project(s)."
                ], 422);
            }

            $propertyStage->delete();

            return response()->json([
                'success' => true,
                'message' => 'Property stage deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete property stage',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get property stages dropdown data
     */
    public function dropdown(): JsonResponse
    {
        try {
            $stages = PropertyStage::active()
                ->ordered()
                ->select('id', 'name', 'slug', 'color', 'icon', 'completion_percentage')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $stages
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property stages dropdown',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get property stages statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $totalStages = PropertyStage::count();
            $activeStages = PropertyStage::active()->count();
            $inactiveStages = PropertyStage::inactive()->count();

            // Get stage usage statistics
            $stageUsage = PropertyStage::withCount('projects')
                ->orderBy('projects_count', 'desc')
                ->get(['id', 'name', 'slug', 'color', 'projects_count']);

            // Get completion percentage distribution
            $completionRanges = [
                '0-25%' => PropertyStage::whereBetween('completion_percentage', [0, 25])->count(),
                '26-50%' => PropertyStage::whereBetween('completion_percentage', [26, 50])->count(),
                '51-75%' => PropertyStage::whereBetween('completion_percentage', [51, 75])->count(),
                '76-100%' => PropertyStage::whereBetween('completion_percentage', [76, 100])->count(),
            ];

            // Get recent stages
            $recentStages = PropertyStage::latest()
                ->take(5)
                ->get(['id', 'name', 'slug', 'status', 'created_at']);

            return response()->json([
                'success' => true,
                'data' => [
                    'overview' => [
                        'total_stages' => $totalStages,
                        'active_stages' => $activeStages,
                        'inactive_stages' => $inactiveStages,
                    ],
                    'stage_usage' => $stageUsage,
                    'completion_distribution' => $completionRanges,
                    'recent_stages' => $recentStages,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property stages statistics',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Toggle status of a property stage
     */
    public function toggleStatus(PropertyStage $propertyStage): JsonResponse
    {
        try {
            $newStatus = $propertyStage->status === 'active' ? 'inactive' : 'active';
            $propertyStage->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'message' => "Property stage status changed to {$newStatus}",
                'data' => $propertyStage
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle property stage status',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Update sort orders for multiple property stages
     */
    public function updateSortOrders(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'stages' => 'required|array',
                'stages.*.id' => 'required|exists:property_stages,id',
                'stages.*.sort_order' => 'required|integer|min:0',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            DB::transaction(function () use ($request) {
                foreach ($request->stages as $stageData) {
                    PropertyStage::where('id', $stageData['id'])
                        ->update(['sort_order' => $stageData['sort_order']]);
                }
            });

            return response()->json([
                'success' => true,
                'message' => 'Sort orders updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update sort orders',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Bulk update status for multiple property stages
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'stage_ids' => 'required|array',
                'stage_ids.*' => 'exists:property_stages,id',
                'status' => 'required|in:active,inactive',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $updatedCount = PropertyStage::whereIn('id', $request->stage_ids)
                ->update(['status' => $request->status]);

            return response()->json([
                'success' => true,
                'message' => "Successfully updated {$updatedCount} property stage(s) to {$request->status}",
                'updated_count' => $updatedCount
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to bulk update property stages',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }
}

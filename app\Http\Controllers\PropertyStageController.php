<?php

namespace App\Http\Controllers;


use Illuminate\Http\Request;
use App\Models\PropertyStage;

class PropertyStageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $stages = PropertyStage::all();
        return response()->json($stages);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);
        $stage = PropertyStage::create($validated);
        return response()->json($stage, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $stage = PropertyStage::findOrFail($id);
        return response()->json($stage);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
        ]);
        $stage = PropertyStage::findOrFail($id);
        $stage->update($validated);
        return response()->json($stage);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $stage = PropertyStage::findOrFail($id);
        $stage->delete();
        return response()->json(['message' => 'Deleted successfully']);
    }
}

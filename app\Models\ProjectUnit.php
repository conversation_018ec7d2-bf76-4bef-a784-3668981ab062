<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProjectUnit extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'project_id',
        'unit_number',
        'rent_type',
        'unit_type',
        'lease_for',
        'floor_number',
        'propertyService',
        'unit_size',
        'area_sqft',
        'bedrooms',
        'bathrooms',
        'rent_price',
        'sell_price',
        'lease_price',
        'currency',
        'status',
        'description',
        'features',
        'is_available',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'rent_price' => 'decimal:2',
        'sell_price' => 'decimal:2',
        'lease_price' => 'decimal:2',
        'area' => 'decimal:2',
        'features' => 'array',
        'amenities' => 'array',
        'bedrooms' => 'integer',
        'bathrooms' => 'integer',
        'floor_number' => 'integer',
        'propertyService' => 'string',
        'unit_size' => 'integer',
    ];

    protected $dates = ['deleted_at'];

    // Relationships
    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Scopes
    public function scopeAvailable($query)
    {
        return $query->where('status', 'available');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('unit_type', $type);
    }

    // Accessors
    public function getFormattedRentPriceAttribute()
    {
        return number_format($this->rent_price, 2);
    }

    public function getFormattedSellPriceAttribute()
    {
        return number_format($this->sell_price, 2);
    }

    public function getFormattedLeasePriceAttribute()
    {
        return number_format($this->lease_price, 2);
    }
}

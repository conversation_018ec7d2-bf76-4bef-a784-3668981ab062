<?php

namespace App\Http\Controllers;

use App\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class RoleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Role::query();

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('description', 'LIKE', "%{$search}%");
                });
            }

            // Filter by status
            if ($request->has('status') && $request->status) {
                $query->where('status', $request->status);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'updated_at');
            $sortOrder = $request->get('sort_order', 'desc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 10);
            $roles = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $roles->items(),
                'pagination' => [
                    'current_page' => $roles->currentPage(),
                    'last_page' => $roles->lastPage(),
                    'per_page' => $roles->perPage(),
                    'total' => $roles->total(),
                ],
                'message' => 'Roles retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving roles: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'name' => 'required|string|max:255|unique:roles,name',
                'description' => 'nullable|string|max:1000',
                'accessible_modules' => 'nullable|array',
                'accessible_modules.*' => 'string',
                'module_permissions' => 'nullable|array',
                'status' => 'required|in:active,inactive',
            ]);

            DB::beginTransaction();

            $role = Role::create($validatedData);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $role,
                'message' => 'Role created successfully'
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error creating role: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Role $role): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => $role,
                'message' => 'Role retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving role: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Role $role): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'name' => ['required', 'string', 'max:255', Rule::unique('roles')->ignore($role->id)],
                'description' => 'nullable|string|max:1000',
                'accessible_modules' => 'nullable|array',
                'accessible_modules.*' => 'string',
                'module_permissions' => 'nullable|array',
                'status' => 'required|in:active,inactive',
            ]);

            DB::beginTransaction();

            $role->update($validatedData);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $role->fresh(),
                'message' => 'Role updated successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error updating role: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Role $role): JsonResponse
    {
        try {
            // Check if role has users (prevent deletion if users are assigned)
            if ($role->users_count > 0) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot delete role with assigned users. Please reassign users first.'
                ], 422);
            }

            DB::beginTransaction();

            $role->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Role deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error deleting role: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available modules and permissions
     */
    public function getAvailableModules(): JsonResponse
    {
        try {
            $modules = [
                ['key' => 'dashboard', 'name' => 'Dashboard', 'icon' => 'LayoutDashboard'],
                ['key' => 'analytics', 'name' => 'Analytics', 'icon' => 'BarChart3'],
                ['key' => 'land-owners', 'name' => 'Land Owners', 'icon' => 'Users'],
                ['key' => 'land-acquisition', 'name' => 'Land Acquisition', 'icon' => 'Building'],
                ['key' => 'project', 'name' => 'Property', 'icon' => 'Briefcase'],
                ['key' => 'property-type', 'name' => 'Property-Type', 'icon' => 'Buliding2'],
                ['key' => 'property-status', 'name' => 'Property Status', 'icon' => 'Building2'],
                ['key' => 'lifecycle', 'name' => 'Lifecycle', 'icon' => 'RotateCcw'],
                ['key' => 'employees', 'name' => 'Employee Management', 'icon' => 'IdCard'],
                ['key' => 'contractors', 'name' => 'Contractor Management', 'icon' => 'HardHat'],
                ['key' => 'assign-contractor', 'name' => 'Assign Contractor', 'icon' => 'UserPlus'],
                ['key' => 'vendor-type', 'name' => 'Vendor Types', 'icon' => 'Package'],
                ['key' => 'vendor', 'name' => 'Vendors', 'icon' => 'Truck'],
                ['key' => 'country', 'name' => 'Country', 'icon' => 'Globe'],
                ['key' => 'state', 'name' => 'State/Province', 'icon' => 'MapPin'],
                ['key' => 'city', 'name' => 'City', 'icon' => 'Building2'],
                ['key' => 'location', 'name' => 'Location', 'icon' => 'MapPin'],
                ['key' => 'language', 'name' => 'Language', 'icon' => 'Languages'],
                ['key' => 'currency', 'name' => 'Currency', 'icon' => 'DollarSign'],
                ['key' => 'customers', 'name' => 'Customers', 'icon' => 'UserCheck'],
                ['key' => 'orders', 'name' => 'Orders', 'icon' => 'ShoppingCart'],
                ['key' => 'components', 'name' => 'Components', 'icon' => 'Layers'],
                ['key' => 'reports', 'name' => 'Reports', 'icon' => 'FileText'],
                ['key' => 'role', 'name' => 'Role Management', 'icon' => 'Shield'],
                ['key' => 'word-assistant', 'name' => 'Word Assistant', 'icon' => 'Bot'],
                ['key' => 'settings', 'name' => 'Settings', 'icon' => 'Settings']
            ];

            $permissions = ['create', 'read', 'update', 'delete', 'export', 'manage', 'use'];

            return response()->json([
                'success' => true,
                'data' => [
                    'modules' => $modules,
                    'permissions' => $permissions
                ],
                'message' => 'Available modules and permissions retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving modules: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get role statistics
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $totalRoles = Role::count();
            $activeRoles = Role::active()->count();
            $inactiveRoles = Role::inactive()->count();
            $totalUsers = Role::sum('users_count');

            return response()->json([
                'success' => true,
                'data' => [
                    'total_roles' => $totalRoles,
                    'active_roles' => $activeRoles,
                    'inactive_roles' => $inactiveRoles,
                    'total_users' => $totalUsers,
                ],
                'message' => 'Role statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk update role status
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'role_ids' => 'required|array',
                'role_ids.*' => 'exists:roles,id',
                'status' => 'required|in:active,inactive',
            ]);

            DB::beginTransaction();

            Role::whereIn('id', $validatedData['role_ids'])
                ->update(['status' => $validatedData['status']]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Roles status updated successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Error updating roles status: ' . $e->getMessage()
            ], 500);
        }
    }
}

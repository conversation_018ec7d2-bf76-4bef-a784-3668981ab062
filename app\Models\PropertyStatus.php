<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PropertyStatus extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'color',
        'icon',
        'metadata',
        'status',
        'sort_order',
    ];

    protected $casts = [
        'metadata' => 'array',
        'sort_order' => 'integer',
    ];

    /**
     * Get projects that have this property status.
     */
    public function projects()
    {
        return $this->hasMany(Project::class, 'status', 'slug');
    }

    /**
     * Scope: Active property statuses
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope: Ordered property statuses
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Scope: Search property statuses
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('slug', 'like', "%{$search}%");
        });
    }

    /**
     * Get the property status with projects count
     */
    public function scopeWithProjectsCount($query)
    {
        return $query->withCount('projects');
    }

    /**
     * Accessor: Get the formatted color for UI
     */
    public function getColorWithOpacityAttribute()
    {
        // Convert hex to RGB with opacity for badges
        $hex = str_replace('#', '', $this->color);
        $r = hexdec(substr($hex, 0, 2));
        $g = hexdec(substr($hex, 2, 2));
        $b = hexdec(substr($hex, 4, 2));
        
        return [
            'bg' => "rgb({$r}, {$g}, {$b}, 0.1)",
            'text' => $this->color,
            'border' => "rgb({$r}, {$g}, {$b}, 0.3)",
        ];
    }

    /**
     * Boot method for model events
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($propertyStatus) {
            if (empty($propertyStatus->slug)) {
                $propertyStatus->slug = \Str::slug($propertyStatus->name);
            }
        });

        static::updating(function ($propertyStatus) {
            if ($propertyStatus->isDirty('name') && !$propertyStatus->isDirty('slug')) {
                $propertyStatus->slug = \Str::slug($propertyStatus->name);
            }
        });
    }
}

import React from 'react';
import {
  ContentModal,
  ContentModalContent,
  ContentModalHeader,
  ContentModalTitle,
  ContentModalOverlay,
} from '@/components/ui/content-modal';
import { Button } from '@/components/ui/button';
import { X, Download, ZoomIn, ZoomOut } from 'lucide-react';

const ImageModal = ({ isOpen, onClose, imageUrl, title, ownerName }) => {
  const [zoom, setZoom] = React.useState(1);

  const handleDownload = () => {
    if (imageUrl) {
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = `${ownerName || 'landowner'}_photo.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.25, 0.5));
  };

  React.useEffect(() => {
    if (!isOpen) {
      setZoom(1);
    }
  }, [isOpen]);

  if (!imageUrl) return null;

  return (
    <ContentModal open={isOpen} onOpenChange={onClose}>
      <ContentModalOverlay />
      <ContentModalContent className="max-w-4xl max-h-[90vh] p-0 overflow-hidden">
        <ContentModalHeader className="p-4 border-b">
          <div className="flex items-center justify-between">
            <ContentModalTitle>{title || `${ownerName}'s Photo`}</ContentModalTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleZoomOut}
                disabled={zoom <= 0.5}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-sm font-mono min-w-[3rem] text-center">
                {Math.round(zoom * 100)}%
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleZoomIn}
                disabled={zoom >= 3}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
              >
                <Download className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onClose}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </ContentModalHeader>
        
        <div className="flex-1 overflow-auto bg-gray-50 flex items-center justify-center p-4">
          <img
            src={imageUrl}
            alt={title || `${ownerName}'s Photo`}
            className="max-w-full max-h-full object-contain transition-transform duration-200"
            style={{ transform: `scale(${zoom})` }}
          />
        </div>
      </ContentModalContent>
    </ContentModal>
  );
};

export default ImageModal;

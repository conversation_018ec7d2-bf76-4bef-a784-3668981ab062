# Sidebar Menu Configuration Documentation
    
## Overview
This document explains how to make sidebar menu items visible in the Real Estate Management System. The system uses a role-based permission system combined with module availability to control which menu items appear for different users.

## System Architecture

### 1. Components Involved
- **Sidebar.jsx** - Main sidebar component that renders menu items
- **AuthContext.jsx** - Manages user permissions and module access
- **App.jsx** - Contains route definitions and permission guards
- **PermissionRoute.jsx** - Protects routes based on user permissions

### 2. Permission Flow    
```
User Login → Role Assignment → Module Permissions → Sidebar Visibility → Route Access
```
  
## Making a Sidebar Menu Item Visible

### Step 1: Add Navigation Item to Sidebar.jsx  
**File:** `resources/js/components/layout/Sidebar.jsx`

**Location:** Around line 228 in the `allNavigationItems` array

```jsx
const allNavigationItems = [
  // ... existing items
  { 
    nameKey: 'common.navigation.yourNewPage', 
    page: 'your-new-page', 
    icon: YourIcon, 
    moduleKey: 'your-module-key' 
  },
];
```

**Parameters:**
- `nameKey`: Translation key for the menu item text
- `page`: URL path (should match route in App.jsx)
- `icon`: Lucide React icon component
- `moduleKey`: Module identifier for permission checking

### Step 2: Add Icon Import (if new)
**Location:** Top of Sidebar.jsx (around line 10-30)

```jsx
import {
  // ... existing imports
  YourNewIcon
} from 'lucide-react';
```

### Step 3: Add Icon to Icon Map
**Location:** Around line 101 in the `getModuleIcon` function

```jsx
const iconMap = {
  // ... existing mappings
  'your-module-key': YourNewIcon,
};
```

### Step 4: Add to Navigation Section
**Location:** Around line 282-302 for section grouping

Choose appropriate section or create new one:

#### For Property Management:
```jsx
const propertyManagementNavigation = accessibleNavItems.filter(item => 
  ['project', 'property-amenity', 'property-type', 'your-module-key'].includes(item.moduleKey)
);
```

#### For Land Development:
```jsx
const landDevelopmentNavigation = accessibleNavItems.filter(item => 
  ['landowners', 'land-acquisition', 'contractors', 'your-module-key'].includes(item.moduleKey)
);
```

#### For Documents & Admin:
```jsx
const documentsNavigation = accessibleNavItems.filter(item => 
  ['role', 'settings', 'employees', 'customer', 'your-module-key'].includes(item.moduleKey)
);
```

### Step 5: Add Module to Core Modules (AuthContext.jsx)
**File:** `resources/js/contexts/AuthContext.jsx`
**Location:** Around line 119 in the `canAccessModule` function

```jsx
const coreModules = [
  'dashboard', 'settings', 'role', 'land-acquisition', 'landowners',
  'analytics', 'project', 'lifecycle', 'employees', 'contractors',
  'assign-contractor', 'assign-vendor', 'assign-employee', 'vendor-type',
  'vendor', 'property-amenity', 'property-type', 'customer', 'orders', 'components',
  'reports', 'word-assistant', 'country', 'language', 'currency',
  'your-module-key' // Add your module here
];
```

### Step 6: Add Fallback Module (AuthContext.jsx)
**Location:** Around line 49 in the `setFallbackModules` function

```jsx
const fallbackModules = [
  'dashboard',
  'land-acquisition',
  'role',
  'settings',
  'landowners',
  'customer',
  'your-module-key' // Add here
];

setModuleDetails({
  // ... existing modules
  'your-module-key': { 
    available: true, 
    name: 'Your Module Name', 
    key: 'your-module-key' 
  }
});
```

### Step 7: Add Route Definition (App.jsx)
**File:** `resources/js/components/App.jsx`
**Location:** Around line 77-100 in the Routes section

```jsx
<Route 
  path="/your-new-page" 
  element={
    <PermissionRoute requiredModule="your-module-key">
      <YourNewPage />
    </PermissionRoute>
  } 
/>
```

Don't forget to import your page component at the top:
```jsx
import YourNewPage from './dashboard/YourNewPage';
```

### Step 8: Add Translation Keys
**File:** `resources/js/lang/en.json` (and other language files)

```json
{
  "common": {
    "navigation": {
      "yourNewPage": "Your New Page",
      // ... other translations
    }
  }
}
```

## Database/Backend Configuration

### Step 9: Update Role Permissions (Backend)
You need to add the module to user roles in the database. This is typically done through:

1. **Role Management Interface** - Use the `/role` page to assign module permissions
2. **Database Seeder** - Add to default role permissions
3. **API Endpoint** - Create module permission assignment endpoint

### Step 10: Backend Route & Controller
Ensure you have the corresponding backend routes and controllers:

**routes/api.php:**
```php
Route::middleware('auth:sanctum')->group(function () {
    Route::apiResource('your-endpoint', YourController::class);
});
```

## Troubleshooting

### Menu Item Not Appearing

1. **Check Module Key Consistency**
   - Ensure the same `moduleKey` is used in all files
   - Check spelling and case sensitivity

2. **Verify User Permissions**
   - Use the "Refresh Permissions" button in the sidebar
   - Check user role has the module permission in database

3. **Check Core Modules List**
   - Ensure module is added to `coreModules` array in AuthContext.jsx

4. **Verify Route Definition**
   - Check App.jsx has the correct route path
   - Ensure PermissionRoute uses correct `requiredModule`

### "Coming Soon" Page Appears

This typically means:
1. Route is not defined in App.jsx
2. Component import is missing
3. Permission route is blocking access
4. Backend API endpoint doesn't exist

### Console Debugging

Add debug logging in AuthContext.jsx:
```jsx
console.log('User modules:', accessibleModules);
console.log('Available modules:', availableModules);
console.log('Can access module:', moduleKey, canAccessModule(moduleKey));
```

## Example: Adding Property Types Menu

Here's a complete example of how the Property Types menu was added:

### 1. Sidebar.jsx Changes:
```jsx
// Navigation item
{ 
  nameKey: 'common.navigation.propertyTypes', 
  page: 'property-type', 
  icon: Building, 
  moduleKey: 'property-type' 
},

// Icon mapping
'property-type': Building,

// Section grouping
const propertyManagementNavigation = accessibleNavItems.filter(item => 
  ['project', 'property-amenity', 'property-type'].includes(item.moduleKey)
);
```

### 2. AuthContext.jsx Changes:
```jsx
// Core modules
const coreModules = [
  // ... existing modules
  'property-type'
];

// Fallback modules
const fallbackModules = [
  // ... existing modules
  'property-type'
];
```

### 3. App.jsx Changes:
```jsx
import PropertyTypes from '../pages/PropertyTypes/PropertyTypes';

<Route 
  path="/property-type" 
  element={
    <PermissionRoute requiredModule="property-type">
      <PropertyTypes />
    </PermissionRoute>
  } 
/>
```

### 4. Translation:
```json
{
  "common": {
    "navigation": {
      "propertyTypes": "Property Types"
    }
  }
}
```

## Best Practices

1. **Consistent Naming**: Use kebab-case for module keys and page paths
2. **Icon Selection**: Choose appropriate Lucide React icons
3. **Section Grouping**: Group related modules in logical sections
4. **Translation Keys**: Use descriptive, nested translation keys
5. **Permission Testing**: Test with different user roles
6. **Error Handling**: Ensure proper error boundaries and fallbacks

## File Locations Summary

| File | Purpose | Key Sections |
|------|---------|--------------|
| `Sidebar.jsx` | Menu rendering | `allNavigationItems`, icon imports, section filters |
| `AuthContext.jsx` | Permission logic | `coreModules`, `setFallbackModules`, `canAccessModule` |
| `App.jsx` | Route definitions | `Routes` section, component imports |
| `lang/en.json` | Translations | `common.navigation` section |

## Notes

- Changes require frontend rebuild (`npm run dev` or `npm run build`)
- Database role permissions must be configured separately
- Test with different user roles to ensure proper access control
- Use browser dev tools to debug permission issues
- The system supports both Laravel module system and standalone pages

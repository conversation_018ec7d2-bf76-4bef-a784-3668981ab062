import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { getApiInfo, reinitializeApi } from '../services/landOwnerAPI';
import { getEnvironmentInfo, testApiConnectivity } from '../config/api';

const ApiDebugPanel = () => {
  const [apiInfo, setApiInfo] = useState(null);
  const [connectivityTests, setConnectivityTests] = useState({});
  const [loading, setLoading] = useState(false);

  const refreshApiInfo = () => {
    const info = getApiInfo();
    const envInfo = getEnvironmentInfo();
    setApiInfo({ ...info, ...envInfo });
  };

  const testConnectivity = async (url) => {
    setLoading(true);
    try {
      const result = await testApiConnectivity(url);
      setConnectivityTests(prev => ({
        ...prev,
        [url]: result
      }));
    } catch (error) {
      setConnectivityTests(prev => ({
        ...prev,
        [url]: { success: false, error: error.message }
      }));
    }
    setLoading(false);
  };

  const testAllUrls = async () => {
    setLoading(true);
    const urlsToTest = [
      'http://127.0.0.1:8000/api',
      'http://localhost:8000/api',
      'https://phplaravel-1312572-5651386.cloudwaysapps.com/api',
      `${window.location.protocol}//${window.location.host}/api`
    ];

    // Remove duplicates
    const uniqueUrls = [...new Set(urlsToTest)];

    for (const url of uniqueUrls) {
      await testConnectivity(url);
    }
    setLoading(false);
  };

  const handleReinitialize = async () => {
    setLoading(true);
    await reinitializeApi();
    refreshApiInfo();
    setLoading(false);
  };

  useEffect(() => {
    refreshApiInfo();
  }, []);

  if (!apiInfo) {
    return <div>Loading API info...</div>;
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">API Debug Panel</h3>
          <div className="flex gap-2">
            <Button 
              onClick={refreshApiInfo} 
              variant="outline" 
              size="sm"
            >
              Refresh Info
            </Button>
            <Button 
              onClick={handleReinitialize} 
              variant="outline" 
              size="sm"
              disabled={loading}
            >
              Reinitialize API
            </Button>
            <Button 
              onClick={testAllUrls} 
              variant="outline" 
              size="sm"
              disabled={loading}
            >
              Test All URLs
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Environment Information */}
        <div>
          <h4 className="font-medium mb-3">Environment Information</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Hostname:</span> {apiInfo.hostname}
            </div>
            <div>
              <span className="font-medium">Port:</span> {apiInfo.port || 'default'}
            </div>
            <div>
              <span className="font-medium">Protocol:</span> {apiInfo.protocol}
            </div>
            <div className="flex items-center gap-2">
              <span className="font-medium">Environment:</span>
              <Badge variant={apiInfo.isLocal ? 'secondary' : 'default'}>
                {apiInfo.isLocal ? 'Local Development' : 'Production'}
              </Badge>
            </div>
          </div>
        </div>

        <Separator />

        {/* API URLs */}
        <div>
          <h4 className="font-medium mb-3">API Configuration</h4>
          <div className="space-y-2 text-sm">
            <div>
              <span className="font-medium">Current API URL:</span>
              <Badge variant="default" className="ml-2">
                {apiInfo.currentUrl}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Original API URL:</span>
              <Badge variant="outline" className="ml-2">
                {apiInfo.originalUrl}
              </Badge>
            </div>
            <div>
              <span className="font-medium">Full Page URL:</span>
              <code className="text-xs bg-muted px-1 py-0.5 rounded ml-2">
                {apiInfo.href}
              </code>
            </div>
          </div>
        </div>

        <Separator />

        {/* Connectivity Tests */}
        <div>
          <h4 className="font-medium mb-3">Connectivity Tests</h4>
          {Object.keys(connectivityTests).length === 0 ? (
            <p className="text-sm text-muted-foreground">
              Click "Test All URLs" to check connectivity to different API endpoints.
            </p>
          ) : (
            <div className="space-y-2">
              {Object.entries(connectivityTests).map(([url, result]) => (
                <div key={url} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex-1">
                    <code className="text-xs">{url}</code>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={result.success ? 'default' : 'destructive'}>
                      {result.success ? 'Connected' : 'Failed'}
                    </Badge>
                    {result.data && (
                      <Badge variant="outline" className="text-xs">
                        {result.data.environment}
                      </Badge>
                    )}
                    {result.error && (
                      <span className="text-xs text-destructive">
                        {result.error}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Instructions */}
        <Separator />
        <div>
          <h4 className="font-medium mb-3">Usage Instructions</h4>
          <div className="text-sm text-muted-foreground space-y-2">
            <p><strong>Local Development:</strong> The system will automatically detect if you're running on localhost or 127.0.0.1 and use the appropriate local API URL.</p>
            <p><strong>Production:</strong> When deployed, it will automatically use your production domain's API endpoint.</p>
            <p><strong>Automatic Fallback:</strong> If the primary API URL fails, the system will automatically try fallback URLs and switch to a working one.</p>
            <p><strong>No Manual Configuration:</strong> You don't need to change any .env files - the system adapts automatically!</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ApiDebugPanel;

import React, { useState, useEffect } from 'react';
import projectVendorAPI from '../../services/projectVendorAPI';
import Swal from 'sweetalert2';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Textarea } from '../ui/textarea';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle 
} from '../ui/dialog';
import { Badge } from '../ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card';
import { 
  Search, 
  Plus, 
  Edit2, 
  Trash2, 
  Users, 
  Building2, 
  Check, 
  X, 
  Filter, 
  ChevronLeft, 
  ChevronRight,
  DollarSign,
  Calendar,
  UserCheck,
  FileText,
  Truck
} from 'lucide-react';

const AssignVendorPage = () => {
  const [assignments, setAssignments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState({});
  const [projects, setProjects] = useState([]);
  const [vendors, setVendors] = useState([]);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  
  // Modal states
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingAssignment, setEditingAssignment] = useState(null);
  
  // Form data
  const [formData, setFormData] = useState({
    project_id: '',
    vendor_id: '',
    start_date: '',
    end_date: '',
    status: 'active',
    contract_amount: '',
    notes: '',
    contract_documents: [],
    existing_documents: []
  });

  // Filters and pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [projectFilter, setProjectFilter] = useState('all');
  const [vendorFilter, setVendorFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');

  // Fetch assignments
  const fetchAssignments = async (page = 1) => {
    setLoading(true);
    try {
      const params = {
        page,
        per_page: perPage,
        search: searchTerm,
        project_id: projectFilter === 'all' ? '' : projectFilter,
        vendor_id: vendorFilter === 'all' ? '' : vendorFilter,
        status: statusFilter === 'all' ? '' : statusFilter,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      const response = await projectVendorAPI.getAssignments(params);
      setAssignments(response.data.data);
      setCurrentPage(response.data.current_page);
      setTotalPages(response.data.last_page);
    } catch (error) {
      console.error('Error fetching assignments:', error);
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to fetch vendor assignments',
        timer: 3000,
        showConfirmButton: false
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const response = await projectVendorAPI.getStatistics();
      setStatistics(response.data);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  // Fetch dropdown data
  const fetchDropdownData = async () => {
    try {
      const [projectsResponse, vendorsResponse] = await Promise.all([
        projectVendorAPI.getProjects(),
        projectVendorAPI.getVendors()
      ]);
      
      console.log('Projects response:', projectsResponse);
      console.log('Vendors response:', vendorsResponse);
      setProjects(projectsResponse.data);
      setVendors(vendorsResponse.data);
    } catch (error) {
      console.error('Error fetching dropdown data:', error);
    }
  };

  useEffect(() => {
    // Check authentication status
    const token = localStorage.getItem('auth_token');
    setIsAuthenticated(!!token);
    
    if (token) {
      fetchAssignments(1);
      fetchStatistics();
      fetchDropdownData();
    }
  }, [perPage, searchTerm, projectFilter, vendorFilter, statusFilter, sortBy, sortOrder]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    fetchAssignments(1);
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      project_id: '',
      vendor_id: '',
      start_date: '',
      end_date: '',
      status: 'active',
      contract_amount: '',
      notes: '',
      contract_documents: [],
      existing_documents: []
    });
  };

  // Handle add assignment
  const handleAddAssignment = async (e) => {
    e.preventDefault();
    
    if (!formData.project_id || !formData.vendor_id) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        text: 'Project and Vendor are required',
        timer: 3000,
        showConfirmButton: false
      });
      return;
    }
    
    try {
      const submitData = new FormData();
      
      // Append basic fields
      Object.keys(formData).forEach(key => {
        if (key !== 'contract_documents' && key !== 'existing_documents') {
          // Only append non-empty values to avoid validation issues
          const value = formData[key];
          if (value !== null && value !== undefined && value !== '') {
            submitData.append(key, value);
          }
        }
      });
      
      // Append files
      if (formData.contract_documents?.length > 0) {
        formData.contract_documents.forEach((file) => {
          submitData.append('contract_documents[]', file);
        });
      }

      console.log('Submitting form data:', Object.fromEntries(submitData));
      const response = await projectVendorAPI.createAssignment(submitData);
      console.log('Assignment creation response:', response);
      
      setShowAddModal(false);
      resetForm();
      fetchAssignments();
      fetchStatistics();
      
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: 'Vendor assigned to project successfully',
        timer: 3000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error creating assignment:', error);
      console.error('Error response:', error.response);
      
      let errorMessage = 'Failed to assign vendor to project';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.errors) {
        const errorMessages = Object.values(error.response.data.errors).flat();
        errorMessage = errorMessages.join(', ');
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: errorMessage,
        timer: 3000,
        showConfirmButton: false
      });
    }
  };

  // Handle edit assignment
  const handleEditAssignment = (assignment) => {
    setEditingAssignment(assignment);
    setFormData({
      project_id: assignment.project_id.toString(),
      vendor_id: assignment.vendor_id.toString(),
      start_date: assignment.start_date || '',
      end_date: assignment.end_date || '',
      status: assignment.status || 'active',
      contract_amount: assignment.contract_amount || '',
      notes: assignment.notes || '',
      contract_documents: [],
      existing_documents: assignment.contract_documents || []
    });
    setShowEditModal(true);
  };

  // Handle update assignment
  const handleUpdateAssignment = async (e) => {
    e.preventDefault();
    
    if (!formData.project_id || !formData.vendor_id) {
      Swal.fire({
        icon: 'error',
        title: 'Validation Error',
        text: 'Project and Vendor are required',
        timer: 3000,
        showConfirmButton: false
      });
      return;
    }
    
    try {
      // Check if we have files to upload
      const hasFiles = formData.contract_documents?.length > 0;
      
      if (hasFiles) {
        // Use FormData for file uploads
        const submitData = new FormData();
        
        // Append basic fields
        Object.keys(formData).forEach(key => {
          if (key !== 'contract_documents') {
            if (key === 'existing_documents') {
              submitData.append(key, JSON.stringify(formData[key]));
            } else {
              // Only append non-empty values to avoid validation issues
              const value = formData[key];
              if (value !== null && value !== undefined && value !== '') {
                submitData.append(key, value);
              }
            }
          }
        });
        
        // Append new files
        formData.contract_documents.forEach((file) => {
          submitData.append('contract_documents[]', file);
        });

        await projectVendorAPI.updateAssignment(editingAssignment.id, submitData);
      } else {
        // Use JSON for updates without files
        const submitData = {};
        
        Object.keys(formData).forEach(key => {
          if (key !== 'contract_documents') {
            if (key === 'existing_documents') {
              submitData[key] = formData[key];
            } else {
              // Only include non-empty values
              const value = formData[key];
              if (value !== null && value !== undefined && value !== '') {
                submitData[key] = value;
              }
            }
          }
        });

        await projectVendorAPI.updateAssignmentJSON(editingAssignment.id, submitData);
      }
      
      setShowEditModal(false);
      setEditingAssignment(null);
      resetForm();
      fetchAssignments();
      fetchStatistics();
      
      Swal.fire({
        icon: 'success',
        title: 'Success!',
        text: 'Assignment updated successfully',
        timer: 3000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error updating assignment:', error);
      
      let errorMessage = 'Failed to update assignment';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.errors) {
        const errorMessages = Object.values(error.response.data.errors).flat();
        errorMessage = errorMessages.join(', ');
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: errorMessage,
        timer: 3000,
        showConfirmButton: false
      });
    }
  };

  // Handle delete assignment
  const handleDeleteAssignment = async (assignment) => {
    const result = await Swal.fire({
      title: 'Delete Assignment',
      text: `Are you sure you want to delete the assignment for ${assignment.vendor?.name} on ${assignment.project?.project_name}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel'
    });

    if (result.isConfirmed) {
      try {
        await projectVendorAPI.deleteAssignment(assignment.id);
        fetchAssignments();
        fetchStatistics();
        
        Swal.fire({
          icon: 'success',
          title: 'Deleted!',
          text: 'Assignment deleted successfully',
          timer: 3000,
          showConfirmButton: false
        });
      } catch (error) {
        console.error('Error deleting assignment:', error);
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to delete assignment',
          timer: 3000,
          showConfirmButton: false
        });
      }
    }
  };

  // Handle file selection
  const handleFileChange = (e) => {
    const files = Array.from(e.target.files);
    setFormData({...formData, contract_documents: [...formData.contract_documents, ...files]});
  };

  // Remove selected file
  const removeFile = (index) => {
    const updatedFiles = formData.contract_documents.filter((_, i) => i !== index);
    setFormData({...formData, contract_documents: updatedFiles});
  };

  // Remove existing file
  const removeExistingFile = (index) => {
    const updatedFiles = formData.existing_documents.filter((_, i) => i !== index);
    setFormData({...formData, existing_documents: updatedFiles});
  };

  if (!isAuthenticated) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-700 mb-2">Authentication Required</h2>
          <p className="text-gray-500">Please log in to access this page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Assign Vendor</h1>
          <p className="text-gray-600">Manage vendor assignments to projects</p>
        </div>
        <Button onClick={() => setShowAddModal(true)} className="bg-blue-600 hover:bg-blue-700">
          <Plus className="w-4 h-4 mr-2" />
          Assign Vendor
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-800">Total Assignments</CardTitle>
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <Users className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900">{statistics.total_assignments || 0}</div>
            <p className="text-xs text-blue-600 mt-1">All vendor assignments</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-800">Active Assignments</CardTitle>
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
              <UserCheck className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">{statistics.active_assignments || 0}</div>
            <p className="text-xs text-green-600 mt-1">Currently active assignments</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-800">Projects with Vendors</CardTitle>
            <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
              <Building2 className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900">{statistics.projects_with_vendors || 0}</div>
            <p className="text-xs text-purple-600 mt-1">Projects with assigned vendors</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-800">Total Contract Value</CardTitle>
            <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
              <DollarSign className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-900">
              ${statistics.total_contract_value ? Number(statistics.total_contract_value).toLocaleString() : '0'}
            </div>
            <p className="text-xs text-orange-600 mt-1">Combined contract amounts</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="w-5 h-5 mr-2" />
            Filters & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
              <div>
                <Label htmlFor="search">Search</Label>
                <Input
                  id="search"
                  placeholder="Search projects or vendors..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              
              <div>
                <Label htmlFor="project-filter">Project</Label>
                <Select value={projectFilter} onValueChange={setProjectFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Projects" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Projects</SelectItem>
                    {projects.map((project) => (
                      <SelectItem key={project.id} value={project.id.toString()}>
                        {project.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="vendor-filter">Vendor</Label>
                <Select value={vendorFilter} onValueChange={setVendorFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Vendors" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Vendors</SelectItem>
                    {vendors.map((vendor) => (
                      <SelectItem key={vendor.id} value={vendor.id.toString()}>
                        {vendor.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="status-filter">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="terminated">Terminated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="per-page">Per Page</Label>
                <Select value={perPage.toString()} onValueChange={(value) => setPerPage(Number(value))}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex items-end">
                <Button type="submit" className="w-full">
                  <Search className="w-4 h-4 mr-2" />
                  Search
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Assignments Table */}
      <Card>
        <CardHeader>
          <CardTitle>Vendor Assignments</CardTitle>
          <CardDescription>Manage vendor assignments to projects</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full table-auto">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Project</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Vendor</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Contract Amount</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Start Date</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-700">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {assignments.map((assignment) => (
                      <tr key={assignment.id} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <Building2 className="w-4 h-4 mr-2 text-gray-400" />
                            <span className="font-medium">{assignment.project?.project_name}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <Truck className="w-4 h-4 mr-2 text-gray-400" />
                            <div>
                              <div className="font-medium">{assignment.vendor?.name}</div>
                              <div className="text-sm text-gray-500">{assignment.vendor?.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <Badge 
                            variant={assignment.status === 'active' ? 'default' : 'secondary'}
                            className={
                              assignment.status === 'active' 
                                ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                                : assignment.status === 'completed'
                                ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                                : assignment.status === 'terminated'
                                ? 'bg-red-100 text-red-800 hover:bg-red-200'
                                : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                            }
                          >
                            {assignment.status}
                          </Badge>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <DollarSign className="w-4 h-4 mr-1 text-gray-400" />
                            <span>{assignment.contract_amount ? Number(assignment.contract_amount).toLocaleString() : 'N/A'}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex items-center">
                            <Calendar className="w-4 h-4 mr-2 text-gray-400" />
                            <span>{assignment.start_date || 'Not set'}</span>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleEditAssignment(assignment)}
                              className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                            >
                              <Edit2 className="w-3 h-3" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleDeleteAssignment(assignment)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="w-3 h-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-between items-center mt-4">
                  <div className="text-sm text-gray-700">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchAssignments(currentPage - 1)}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="w-4 h-4" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchAssignments(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    >
                      Next
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Assignment Modal */}
      <Dialog open={showAddModal} onOpenChange={setShowAddModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Truck className="w-5 h-5 mr-2" />
              Assign Vendor to Project
            </DialogTitle>
            <DialogDescription>
              Create a new vendor assignment to a project.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAddAssignment} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="project_id">Project *</Label>
                <Select value={formData.project_id} onValueChange={(value) => setFormData({...formData, project_id: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Project" />
                  </SelectTrigger>
                  <SelectContent>
                    {projects.map((project) => (
                      <SelectItem key={project.id} value={project.id.toString()}>
                        {project.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="vendor_id">Vendor *</Label>
                <Select value={formData.vendor_id} onValueChange={(value) => setFormData({...formData, vendor_id: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Vendor" />
                  </SelectTrigger>
                  <SelectContent>
                    {vendors.map((vendor) => (
                      <SelectItem key={vendor.id} value={vendor.id.toString()}>
                        {vendor.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="start_date">Start Date</Label>
                <Input
                  id="start_date"
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => setFormData({...formData, start_date: e.target.value})}
                />
              </div>
              
              <div>
                <Label htmlFor="end_date">End Date</Label>
                <Input
                  id="end_date"
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => setFormData({...formData, end_date: e.target.value})}
                />
              </div>
              
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="terminated">Terminated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="contract_amount">Contract Amount</Label>
                <Input
                  id="contract_amount"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={formData.contract_amount}
                  onChange={(e) => setFormData({...formData, contract_amount: e.target.value})}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="notes">Notes</Label>
              <Textarea
                id="notes"
                placeholder="Additional notes..."
                value={formData.notes}
                onChange={(e) => setFormData({...formData, notes: e.target.value})}
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="contract_documents">Contract Documents</Label>
              <Input
                id="contract_documents"
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                onChange={handleFileChange}
              />
              {formData.contract_documents.length > 0 && (
                <div className="mt-2 space-y-1">
                  {formData.contract_documents.map((file, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <span className="text-sm">{file.name}</span>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowAddModal(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <Plus className="w-4 h-4 mr-2" />
                Assign Vendor
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Assignment Modal */}
      <Dialog open={showEditModal} onOpenChange={setShowEditModal}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              <Edit2 className="w-5 h-5 mr-2" />
              Edit Vendor Assignment
            </DialogTitle>
            <DialogDescription>
              Update vendor assignment details.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleUpdateAssignment} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit_project_id">Project *</Label>
                <Select value={formData.project_id} onValueChange={(value) => setFormData({...formData, project_id: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Project" />
                  </SelectTrigger>
                  <SelectContent>
                    {projects.map((project) => (
                      <SelectItem key={project.id} value={project.id.toString()}>
                        {project.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="edit_vendor_id">Vendor *</Label>
                <Select value={formData.vendor_id} onValueChange={(value) => setFormData({...formData, vendor_id: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select Vendor" />
                  </SelectTrigger>
                  <SelectContent>
                    {vendors.map((vendor) => (
                      <SelectItem key={vendor.id} value={vendor.id.toString()}>
                        {vendor.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="edit_start_date">Start Date</Label>
                <Input
                  id="edit_start_date"
                  type="date"
                  value={formData.start_date}
                  onChange={(e) => setFormData({...formData, start_date: e.target.value})}
                />
              </div>
              
              <div>
                <Label htmlFor="edit_end_date">End Date</Label>
                <Input
                  id="edit_end_date"
                  type="date"
                  value={formData.end_date}
                  onChange={(e) => setFormData({...formData, end_date: e.target.value})}
                />
              </div>
              
              <div>
                <Label htmlFor="edit_status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="terminated">Terminated</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="edit_contract_amount">Contract Amount</Label>
                <Input
                  id="edit_contract_amount"
                  type="number"
                  step="0.01"
                  placeholder="0.00"
                  value={formData.contract_amount}
                  onChange={(e) => setFormData({...formData, contract_amount: e.target.value})}
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="edit_notes">Notes</Label>
              <Textarea
                id="edit_notes"
                placeholder="Additional notes..."
                value={formData.notes}
                onChange={(e) => setFormData({...formData, notes: e.target.value})}
                rows={3}
              />
            </div>
            
            {/* Existing Documents */}
            {formData.existing_documents.length > 0 && (
              <div>
                <Label>Existing Documents</Label>
                <div className="mt-2 space-y-1">
                  {formData.existing_documents.map((doc, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <span className="text-sm">{doc}</span>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeExistingFile(index)}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}
            
            <div>
              <Label htmlFor="edit_contract_documents">Add New Documents</Label>
              <Input
                id="edit_contract_documents"
                type="file"
                multiple
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                onChange={handleFileChange}
              />
              {formData.contract_documents.length > 0 && (
                <div className="mt-2 space-y-1">
                  {formData.contract_documents.map((file, index) => (
                    <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                      <span className="text-sm">{file.name}</span>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeFile(index)}
                      >
                        <X className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setShowEditModal(false)}>
                Cancel
              </Button>
              <Button type="submit">
                <Check className="w-4 h-4 mr-2" />
                Update Assignment
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AssignVendorPage;

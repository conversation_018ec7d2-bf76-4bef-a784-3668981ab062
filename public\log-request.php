<?php
// This file will log all incoming requests to help debug the frontend issue
$logFile = 'debug-requests.log';

// Log the request details
$requestData = [
    'timestamp' => date('Y-m-d H:i:s'),
    'method' => $_SERVER['REQUEST_METHOD'],
    'uri' => $_SERVER['REQUEST_URI'],
    'headers' => getallheaders(),
    'body' => file_get_contents('php://input'),
    'post' => $_POST,
    'get' => $_GET,
    'files' => $_FILES
];

file_put_contents($logFile, "\n" . str_repeat('=', 50) . "\n" . json_encode($requestData, JSON_PRETTY_PRINT) . "\n", FILE_APPEND);

// Return a response so the frontend knows the request was logged
header('Content-Type: application/json');
echo json_encode([
    'logged' => true,
    'timestamp' => $requestData['timestamp']
]);
?>

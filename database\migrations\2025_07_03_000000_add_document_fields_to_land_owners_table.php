<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('land_owners', function (Blueprint $table) {
            // Document type and files
            $table->enum('document_type', ['nid', 'passport'])->nullable()->after('photo');
            $table->string('nid_front')->nullable()->after('document_type');
            $table->string('nid_back')->nullable()->after('nid_front');
            $table->string('passport_photo')->nullable()->after('nid_back');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('land_owners', function (Blueprint $table) {
            $table->dropColumn(['document_type', 'nid_front', 'nid_back', 'passport_photo']);
        });
    }
};

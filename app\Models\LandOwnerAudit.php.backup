<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class LandOwnerAudit extends Model
{
    use HasFactory;

    protected $fillable = [
        'land_owner_id',
        'user_id',
        'action',
        'event_type',
        'user_name',
        'user_email',
        'old_values',
        'new_values',
        'changed_fields',
        'ip_address',
        'user_agent',
        'source',
        'description',
        'metadata',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'changed_fields' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Relationship with LandOwner
     */
    public function landOwner()
    {
        return $this->belongsTo(LandOwner::class);
    }

    /**
     * Relationship with User (who made the change)
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get formatted action text
     */
    public function getActionTextAttribute(): string
    {
        return match($this->action) {
            'created' => 'Created land owner',
            'updated' => 'Updated land owner',
            'deleted' => 'Deleted land owner',
            default => ucfirst($this->action)
        };
    }

    /**
     * Get human readable timestamp
     */
    public function getFormattedDateAttribute(): string
    {
        return $this->created_at->format('Y-m-d H:i:s');
    }

    /**
     * Get the changes made in a readable format
     */
    public function getChangesDescriptionAttribute(): string
    {
        if (empty($this->changed_fields)) {
            return 'No specific changes tracked';
        }

        $changes = [];
        foreach ($this->changed_fields as $field) {
            $oldValue = $this->old_values[$field] ?? 'N/A';
            $newValue = $this->new_values[$field] ?? 'N/A';
            
            // Hide sensitive fields or show them as masked
            if (in_array($field, ['photo'])) {
                $changes[] = "{$field}: " . ($oldValue ? 'File updated' : 'File added');
            } else {
                $changes[] = "{$field}: '{$oldValue}' → '{$newValue}'";
            }
        }

        return implode(', ', $changes);
    }
}

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('land_addresses', function (Blueprint $table) {
            // Check if city column doesn't already exist (we already have city_text)
            if (!Schema::hasColumn('land_addresses', 'city')) {
                // Add city varchar field after city_text since city_id doesn't exist
                $table->string('city')->nullable()->after('city_text')->comment('City name as text');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('land_addresses', function (Blueprint $table) {
            // Remove city field
            $table->dropColumn('city');
        });
    }
};

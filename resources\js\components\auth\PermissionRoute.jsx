import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { Navigate } from 'react-router-dom';

const PermissionRoute = ({ 
  children, 
  requiredModule = null,
  requiredPermission = null,
  redirectTo = '/dashboard',
  showError = true 
}) => {
  const { hasModuleAccess, hasPermission, loading, isAuthenticated } = useAuth();

  // Still loading authentication data
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Not authenticated at all - this should be handled by ProtectedRoute
  if (!isAuthenticated) {
    return <Navigate to="/" replace />;
  }

  // Check module access if required
  if (requiredModule && !hasModuleAccess(requiredModule)) {
    if (showError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md mx-auto text-center">
            <div className="mb-4">
              <svg className="mx-auto h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
            <p className="text-gray-600 mb-6">
              You don't have permission to access this module. 
              {requiredModule && (
                <span className="block mt-2 text-sm text-gray-500">
                  Required module: <code className="bg-gray-100 px-1 py-0.5 rounded">{requiredModule}</code>
                </span>
              )}
            </p>
            <button 
              onClick={() => window.history.back()}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors mr-2"
            >
              Go Back
            </button>
            <button 
              onClick={() => window.location.href = redirectTo}
              className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      );
    }
    return <Navigate to={redirectTo} replace />;
  }

  // Check specific permission if required
  if (requiredModule && requiredPermission && !hasPermission(requiredModule, requiredPermission)) {
    if (showError) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md mx-auto text-center">
            <div className="mb-4">
              <svg className="mx-auto h-16 w-16 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Insufficient Permissions</h1>
            <p className="text-gray-600 mb-6">
              You don't have the required permission for this action.
              {requiredModule && requiredPermission && (
                <span className="block mt-2 text-sm text-gray-500">
                  Required: <code className="bg-gray-100 px-1 py-0.5 rounded">{requiredModule}.{requiredPermission}</code>
                </span>
              )}
            </p>
            <button 
              onClick={() => window.history.back()}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors mr-2"
            >
              Go Back
            </button>
            <button 
              onClick={() => window.location.href = redirectTo}
              className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 transition-colors"
            >
              Go to Dashboard
            </button>
          </div>
        </div>
      );
    }
    return <Navigate to={redirectTo} replace />;
  }

  // All checks passed, render the protected content
  return children;
};

export default PermissionRoute;

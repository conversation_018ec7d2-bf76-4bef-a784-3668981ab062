<?php

use Illuminate\Support\Facades\Route;
use Modules\LandOwners\Http\Controllers\LandOwnerController;

/*
|--------------------------------------------------------------------------
| Module API Routes
|--------------------------------------------------------------------------
|
| These routes are disabled as the main API routes are handled in 
| routes/api.php to maintain the existing API structure.
| All LandOwner API endpoints are defined in the main routes/api.php file.
|
*/

// Module API routes are disabled - using main routes/api.php instead
// Route::middleware(['auth:sanctum'])->prefix('v1')->group(function () {
//     Route::apiResource('landowners', LandOwnerController::class)->names('landowners');
// });

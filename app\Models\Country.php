<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Country extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'code',
        'iso_code',
        'capital',
        'currency',
        'phone_code',
        'continent',
        'population',
        'status'
    ];

    protected $casts = [
        'status' => 'string',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    public function scopeByContinent($query, $continent)
    {
        return $query->where('continent', $continent);
    }

    // Accessors
    public function getFormattedPopulationAttribute()
    {
        return number_format($this->population);
    }

    public function getFullNameAttribute()
    {
        return $this->name . ' (' . $this->code . ')';
    }

    /**
     * Relationship with States
     */
    public function states()
    {
        return $this->hasMany(State::class);
    }

    /**
     * Get active states for this country
     */
    public function activeStates()
    {
        return $this->hasMany(State::class)->where('status', 'active');
    }
}

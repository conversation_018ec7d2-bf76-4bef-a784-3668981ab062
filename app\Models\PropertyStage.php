<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class PropertyStage extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'color',
        'icon',
        'status',
        'sort_order',
        'metadata',
        'completion_percentage'
    ];

    protected $casts = [
        'metadata' => 'array',
        'sort_order' => 'integer',
        'completion_percentage' => 'decimal:2',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    protected $attributes = [
        'color' => '#3B82F6',
        'status' => 'active',
        'sort_order' => 0,
        'completion_percentage' => 0
    ];

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($stage) {
            if (empty($stage->slug)) {
                $stage->slug = Str::slug($stage->name);
            }
        });

        static::updating(function ($stage) {
            if ($stage->isDirty('name') && empty($stage->slug)) {
                $stage->slug = Str::slug($stage->name);
            }
        });
    }

    /**
     * Get projects that are in this property stage.
     */
    public function projects()
    {
        return $this->hasMany(Project::class, 'property_stage', 'slug');
    }

    /**
     * Scope: Active property stages
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope: Inactive property stages
     */
    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    /**
     * Scope: Ordered property stages
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    /**
     * Get the route key for the model.
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Get formatted completion percentage
     */
    public function getFormattedCompletionPercentageAttribute()
    {
        return number_format($this->completion_percentage, 1) . '%';
    }

    /**
     * Check if stage is active
     */
    public function isActive()
    {
        return $this->status === 'active';
    }

    /**
     * Get projects count for this stage
     */
    public function getProjectsCountAttribute()
    {
        return $this->projects()->count();
    }
}

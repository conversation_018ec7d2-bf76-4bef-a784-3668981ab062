<?php

namespace App\Http\Controllers;

use App\Models\PropertyType;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PropertyTypeController extends Controller
{
    /**
     * Display a listing of the property types
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = PropertyType::query();

            // Search functionality
            if ($request->has('search') && !empty($request->search)) {
                $searchTerm = $request->search;
                $query->where(function ($q) use ($searchTerm) {
                    $q->where('name', 'like', "%{$searchTerm}%")
                      ->orWhere('description', 'like', "%{$searchTerm}%");
                });
            }

            // Status filter
            if ($request->has('status') && in_array($request->status, ['active', 'inactive'])) {
                $query->where('status', $request->status);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'sort_order');
            $sortDirection = $request->get('sort_direction', 'asc');
            
            if (in_array($sortBy, ['name', 'status', 'sort_order', 'created_at'])) {
                $query->orderBy($sortBy, $sortDirection);
            }

            // Add secondary sort by name if not already sorting by name
            if ($sortBy !== 'name') {
                $query->orderBy('name', 'asc');
            }

            // Pagination
            $perPage = $request->get('per_page', 15);
            $propertyTypes = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $propertyTypes,
                'message' => 'Property types retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property types',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created property type
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:property_types,name',
                'description' => 'nullable|string',
                'icon' => 'nullable|string|max:255',
                'color' => 'nullable|string|max:7',
                'features' => 'nullable|array',
                'base_price_multiplier' => 'nullable|numeric|min:0',
                'status' => 'nullable|in:active,inactive',
                'sort_order' => 'nullable|integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            
            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = Str::slug($data['name']);
            }

            // Set defaults
            $data['status'] = $data['status'] ?? 'active';
            $data['sort_order'] = $data['sort_order'] ?? 0;
            $data['icon'] = $data['icon'] ?? 'Building';
            $data['color'] = $data['color'] ?? '#6366f1';
            $data['base_price_multiplier'] = $data['base_price_multiplier'] ?? 1.00;

            $propertyType = PropertyType::create($data);

            return response()->json([
                'success' => true,
                'data' => $propertyType,
                'message' => 'Property type created successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create property type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified property type
     */
    public function show(PropertyType $propertyType): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => $propertyType,
                'message' => 'Property type retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property type'
            ], 500);
        }
    }

    /**
     * Update the specified property type
     */
    public function update(Request $request, PropertyType $propertyType): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|max:255|unique:property_types,name,' . $propertyType->id,
                'description' => 'nullable|string',
                'icon' => 'nullable|string|max:255',
                'color' => 'nullable|string|max:7',
                'features' => 'nullable|array',
                'base_price_multiplier' => 'nullable|numeric|min:0',
                'status' => 'nullable|in:active,inactive',
                'sort_order' => 'nullable|integer|min:0'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();
            
            // Update slug if name changed
            if (isset($data['name']) && $data['name'] !== $propertyType->name) {
                $data['slug'] = Str::slug($data['name']);
            }

            $propertyType->update($data);

            return response()->json([
                'success' => true,
                'data' => $propertyType->fresh(),
                'message' => 'Property type updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update property type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified property type
     */
    public function destroy(PropertyType $propertyType): JsonResponse
    {
        try {
            $propertyType->delete();

            return response()->json([
                'success' => true,
                'message' => 'Property type deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete property type'
            ], 500);
        }
    }

    /**
     * Toggle property type status
     */
    public function toggleStatus(PropertyType $propertyType): JsonResponse
    {
        try {
            $newStatus = $propertyType->status === 'active' ? 'inactive' : 'active';
            $propertyType->update(['status' => $newStatus]);

            return response()->json([
                'success' => true,
                'message' => 'Property type status updated successfully',
                'data' => $propertyType->fresh()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update property type status'
            ], 500);
        }
    }

    /**
     * Get property types for dropdown
     */
    public function dropdown(): JsonResponse
    {
        try {
            $propertyTypes = PropertyType::active()
                ->ordered()
                ->select('id', 'name', 'slug', 'icon', 'color')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $propertyTypes,
                'message' => 'Property types for dropdown retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property types for dropdown'
            ], 500);
        }
    }
}

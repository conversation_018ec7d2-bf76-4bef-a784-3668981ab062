import React, { useState, useEffect } from 'react';
import { X } from 'lucide-react';
import propertyTypeAPI from '../../services/propertyTypeAPI';
import { showAlert } from '../../utils/sweetAlert';

const PropertyTypeModal = ({ isOpen, onClose, onSuccess, mode = 'create', propertyType = null }) => {
    const [loading, setLoading] = useState(false);
    const [formData, setFormData] = useState({
        name: '',
        description: '',
        icon: 'Building',
        color: '#6366f1',
        features: [],
        base_price_multiplier: 1.00,
        status: 'active',
        sort_order: 0
    });
    const [featureInput, setFeatureInput] = useState('');
    const [errors, setErrors] = useState({});

    // Predefined icons and colors
    const iconOptions = [
        'Building', 'Home', 'Factory', 'Store', 'Hotel', 'School', 'MapPin', 'Building2'
    ];

    const colorOptions = [
        '#6366f1', '#10b981', '#3b82f6', '#f59e0b', '#dc2626', '#8b5cf6', '#059669', '#7c3aed', '#0891b2'
    ];

    useEffect(() => {
        if (mode !== 'create' && propertyType) {
            setFormData({
                name: propertyType.name || '',
                description: propertyType.description || '',
                icon: propertyType.icon || 'Building',
                color: propertyType.color || '#6366f1',
                features: propertyType.features || [],
                base_price_multiplier: propertyType.base_price_multiplier || 1.00,
                status: propertyType.status || 'active',
                sort_order: propertyType.sort_order || 0
            });
        } else {
            // Reset form for create mode
            setFormData({
                name: '',
                description: '',
                icon: 'Building',
                color: '#6366f1',
                features: [],
                base_price_multiplier: 1.00,
                status: 'active',
                sort_order: 0
            });
        }
        setErrors({});
        setFeatureInput('');
    }, [mode, propertyType, isOpen]);

    const handleInputChange = (e) => {
        const { name, value, type } = e.target;
        setFormData(prev => ({
            ...prev,
            [name]: type === 'number' ? parseFloat(value) || 0 : value
        }));
        
        // Clear error for this field
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: '' }));
        }
    };

    const handleAddFeature = () => {
        if (featureInput.trim() && !formData.features.includes(featureInput.trim())) {
            setFormData(prev => ({
                ...prev,
                features: [...prev.features, featureInput.trim()]
            }));
            setFeatureInput('');
        }
    };

    const handleRemoveFeature = (index) => {
        setFormData(prev => ({
            ...prev,
            features: prev.features.filter((_, i) => i !== index)
        }));
    };

    const handleFeatureKeyPress = (e) => {
        if (e.key === 'Enter') {
            e.preventDefault();
            handleAddFeature();
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setErrors({});

        try {
            let response;
            
            if (mode === 'create') {
                response = await propertyTypeAPI.create(formData);
            } else if (mode === 'edit') {
                response = await propertyTypeAPI.update(propertyType.id, formData);
            }

            if (response.success) {
                onSuccess();
            }
        } catch (error) {
            console.error('Failed to save property type:', error);
            
            if (error.response?.status === 422 && error.response?.data?.errors) {
                setErrors(error.response.data.errors);
            } else {
                showAlert('error', 'Error', 'Failed to save property type');
            }
        } finally {
            setLoading(false);
        }
    };

    if (!isOpen) return null;

    const isViewMode = mode === 'view';
    const modalTitle = mode === 'create' ? 'Add Property Type' : 
                     mode === 'edit' ? 'Edit Property Type' : 'Property Type Details';

    return (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
            <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
                {/* Header */}
                <div className="flex items-center justify-between pb-4 border-b">
                    <h3 className="text-lg font-semibold text-gray-900">
                        {modalTitle}
                    </h3>
                    <button
                        onClick={onClose}
                        className="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                        <X className="h-6 w-6" />
                    </button>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit} className="mt-4 space-y-6">
                    {/* Basic Information */}
                    <div className="grid grid-cols-1 gap-6">
                        {/* Name */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Name *
                            </label>
                            <input
                                type="text"
                                name="name"
                                value={formData.name}
                                onChange={handleInputChange}
                                disabled={isViewMode}
                                className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                    isViewMode ? 'bg-gray-50' : ''
                                } ${errors.name ? 'border-red-500' : ''}`}
                                placeholder="Enter property type name"
                            />
                            {errors.name && (
                                <p className="mt-1 text-sm text-red-600">{errors.name[0]}</p>
                            )}
                        </div>

                        {/* Description */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Description
                            </label>
                            <textarea
                                name="description"
                                value={formData.description}
                                onChange={handleInputChange}
                                disabled={isViewMode}
                                rows={3}
                                className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                    isViewMode ? 'bg-gray-50' : ''
                                } ${errors.description ? 'border-red-500' : ''}`}
                                placeholder="Enter property type description"
                            />
                            {errors.description && (
                                <p className="mt-1 text-sm text-red-600">{errors.description[0]}</p>
                            )}
                        </div>
                    </div>

                    {/* Visual Settings */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        {/* Icon */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Icon
                            </label>
                            <select
                                name="icon"
                                value={formData.icon}
                                onChange={handleInputChange}
                                disabled={isViewMode}
                                className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                    isViewMode ? 'bg-gray-50' : ''
                                }`}
                            >
                                {iconOptions.map(icon => (
                                    <option key={icon} value={icon}>{icon}</option>
                                ))}
                            </select>
                        </div>

                        {/* Color */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Color
                            </label>
                            <div className="flex items-center gap-2">
                                <input
                                    type="color"
                                    name="color"
                                    value={formData.color}
                                    onChange={handleInputChange}
                                    disabled={isViewMode}
                                    className="w-12 h-10 border border-gray-300 rounded cursor-pointer disabled:cursor-not-allowed"
                                />
                                <div className="flex flex-wrap gap-1">
                                    {colorOptions.map(color => (
                                        <button
                                            key={color}
                                            type="button"
                                            onClick={() => !isViewMode && setFormData(prev => ({ ...prev, color }))}
                                            disabled={isViewMode}
                                            className={`w-6 h-6 rounded border-2 ${
                                                formData.color === color ? 'border-gray-800' : 'border-gray-300'
                                            } ${isViewMode ? 'cursor-not-allowed' : 'cursor-pointer hover:border-gray-800'}`}
                                            style={{ backgroundColor: color }}
                                        />
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Features */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                            Features
                        </label>
                        {!isViewMode && (
                            <div className="flex gap-2 mb-2">
                                <input
                                    type="text"
                                    value={featureInput}
                                    onChange={(e) => setFeatureInput(e.target.value)}
                                    onKeyPress={handleFeatureKeyPress}
                                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    placeholder="Add a feature and press Enter"
                                />
                                <button
                                    type="button"
                                    onClick={handleAddFeature}
                                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors"
                                >
                                    Add
                                </button>
                            </div>
                        )}
                        
                        {formData.features.length > 0 && (
                            <div className="flex flex-wrap gap-2">
                                {formData.features.map((feature, index) => (
                                    <span
                                        key={index}
                                        className="inline-flex items-center gap-1 bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm"
                                    >
                                        {feature}
                                        {!isViewMode && (
                                            <button
                                                type="button"
                                                onClick={() => handleRemoveFeature(index)}
                                                className="ml-1 text-blue-600 hover:text-blue-800"
                                            >
                                                ×
                                            </button>
                                        )}
                                    </span>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Settings */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        

                        {/* Sort Order */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Sort Order
                            </label>
                            <input
                                type="number"
                                name="sort_order"
                                value={formData.sort_order}
                                onChange={handleInputChange}
                                disabled={isViewMode}
                                min="0"
                                className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                    isViewMode ? 'bg-gray-50' : ''
                                } ${errors.sort_order ? 'border-red-500' : ''}`}
                            />
                            {errors.sort_order && (
                                <p className="mt-1 text-sm text-red-600">{errors.sort_order[0]}</p>
                            )}
                        </div>

                        {/* Status */}
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Status
                            </label>
                            <select
                                name="status"
                                value={formData.status}
                                onChange={handleInputChange}
                                disabled={isViewMode}
                                className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                                    isViewMode ? 'bg-gray-50' : ''
                                }`}
                            >
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>

                    {/* Preview */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h4 className="text-sm font-medium text-gray-700 mb-2">Preview</h4>
                        <div className="flex items-center gap-3">
                            <div 
                                className="w-10 h-10 rounded-lg flex items-center justify-center text-white"
                                style={{ backgroundColor: formData.color }}
                            >
                                <span className="text-sm font-medium">
                                    {formData.icon || '🏢'}
                                </span>
                            </div>
                            <div>
                                <h5 className="font-medium text-gray-900">
                                    {formData.name || 'Property Type Name'}
                                </h5>
                                <p className="text-sm text-gray-600">
                                    {formData.description || 'Property type description'}
                                </p>
                                <div className="flex items-center gap-2 mt-1">
                                    <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                        formData.status === 'active'
                                            ? 'bg-green-100 text-green-800'
                                            : 'bg-red-100 text-red-800'
                                    }`}>
                                        {formData.status}
                                    </span>
                                    <span className="text-xs text-gray-500">
                                        {formData.base_price_multiplier}x multiplier
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Actions */}
                    <div className="flex items-center justify-end gap-3 pt-4 border-t">
                        <button
                            type="button"
                            onClick={onClose}
                            className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md transition-colors"
                        >
                            {isViewMode ? 'Close' : 'Cancel'}
                        </button>
                        
                        {!isViewMode && (
                            <button
                                type="submit"
                                disabled={loading}
                                className="bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white px-6 py-2 rounded-md transition-colors flex items-center gap-2"
                            >
                                {loading && (
                                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                )}
                                {mode === 'create' ? 'Create Property Type' : 'Update Property Type'}
                            </button>
                        )}
                    </div>
                </form>
            </div>
        </div>
    );
};

export default PropertyTypeModal;

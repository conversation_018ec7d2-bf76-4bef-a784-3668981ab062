<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('land_owners', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('father_name')->nullable();
            $table->string('mother_name')->nullable();
            $table->text('address');
            $table->string('phone')->nullable();
            $table->string('nid_number')->nullable()->unique();
            $table->string('email')->nullable();
            $table->string('photo')->nullable();
            $table->decimal('ownership_percentage', 5, 2)->nullable();
            $table->decimal('cash_received', 15, 2)->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['name']);
            $table->index(['nid_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('land_owners');
    }
};

import axiosInstance from '../config/api';

const projectVendorAPI = {
  // Get all project vendor assignments with filtering and pagination
  getAssignments: async (params = {}) => {
    try {
      const response = await axiosInstance.get('/project-vendors', {
        params: {
          page: params.page || 1,
          per_page: params.per_page || 10,
          search: params.search || '',
          project_id: params.project_id || '',
          vendor_id: params.vendor_id || '',
          status: params.status || '',
          sort_by: params.sort_by || 'created_at',
          sort_order: params.sort_order || 'desc'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching project vendor assignments:', error);
      throw error;
    }
  },

  // Get assignment by ID
  getAssignment: async (id) => {
    try {
      const response = await axiosInstance.get(`/project-vendors/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching assignment:', error);
      throw error;
    }
  },

  // Create new assignment
  createAssignment: async (formData) => {
    try {
      const response = await axiosInstance.post('/project-vendors', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error creating assignment:', error);
      throw error;
    }
  },

  // Update assignment
  updateAssignment: async (id, formData) => {
    try {
      const response = await axiosInstance.put(`/project-vendors/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error updating assignment:', error);
      throw error;
    }
  },

  // Update assignment with JSON (no files)
  updateAssignmentJSON: async (id, data) => {
    try {
      const response = await axiosInstance.put(`/project-vendors/${id}`, data, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Error updating assignment:', error);
      throw error;
    }
  },

  // Delete assignment
  deleteAssignment: async (id) => {
    try {
      const response = await axiosInstance.delete(`/project-vendors/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting assignment:', error);
      throw error;
    }
  },

  // Get statistics
  getStatistics: async () => {
    try {
      const response = await axiosInstance.get('/project-vendors-statistics');
      return response.data;
    } catch (error) {
      console.error('Error fetching statistics:', error);
      throw error;
    }
  },

  // Get projects dropdown
  getProjects: async () => {
    try {
      const response = await axiosInstance.get('/project-vendors-projects');
      return response.data;
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw error;
    }
  },

  // Get vendors dropdown
  getVendors: async () => {
    try {
      const response = await axiosInstance.get('/project-vendors-vendors');
      return response.data;
    } catch (error) {
      console.error('Error fetching vendors:', error);
      throw error;
    }
  }
};

export default projectVendorAPI;

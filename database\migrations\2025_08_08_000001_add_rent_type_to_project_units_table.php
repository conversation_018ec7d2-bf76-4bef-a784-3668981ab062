<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('project_units', function (Blueprint $table) {
            $table->string('rent_type', 50)->nullable()->after('unit_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('project_units', function (Blueprint $table) {
            $table->dropColumn('rent_type');
        });
    }
};

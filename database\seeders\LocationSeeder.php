<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Location;
use App\Models\State;
use App\Models\Country;

class LocationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get some existing countries and states to create locations
        $bangladesh = Country::where('name', 'Bangladesh')->first();
        $usa = Country::where('name', 'United States')->first();
        
        if ($bangladesh) {
            // Get some states from Bangladesh
            $dhaka = State::where('country_id', $bangladesh->id)->where('name', 'like', '%Dhaka%')->first();
            $chittagong = State::where('country_id', $bangladesh->id)->where('name', 'like', '%Chittagong%')->first();
            
            if ($dhaka) {
                Location::create([
                    'name' => 'Gulshan',
                    'state_id' => $dhaka->id,
                    'country_id' => $bangladesh->id,
                  
                    'status' => 'active'
                ]);
                
                Location::create([
                    'name' => 'Dhanmondi',
                    'state_id' => $dhaka->id,
                    'country_id' => $bangladesh->id,
                  
                    'status' => 'active'
                ]);
                
                Location::create([
                    'name' => 'Uttara',
                    'state_id' => $dhaka->id,
                    'country_id' => $bangladesh->id,
                   
                    'status' => 'active'
                ]);
            }
            
            if ($chittagong) {
                Location::create([
                    'name' => 'Agrabad',
                    'state_id' => $chittagong->id,
                    'country_id' => $bangladesh->id,
                    
                    'status' => 'active'
                ]);
            }
        }
        
        if ($usa) {
            // Get some states from USA
            $california = State::where('country_id', $usa->id)->where('name', 'like', '%California%')->first();
            $texas = State::where('country_id', $usa->id)->where('name', 'like', '%Texas%')->first();
            
            if ($california) {
                Location::create([
                    'name' => 'Los Angeles',
                    'state_id' => $california->id,
                    'country_id' => $usa->id,
                  
                    'status' => 'active'
                ]);
                
                Location::create([
                    'name' => 'San Francisco',
                    'state_id' => $california->id,
                    'country_id' => $usa->id,
                  
                    'status' => 'active'
                ]);
            }
            
            if ($texas) {
                Location::create([
                    'name' => 'Houston',
                    'state_id' => $texas->id,
                    'country_id' => $usa->id,
                  
                    'status' => 'active'
                ]);
            }
        }
        
        // Add some generic locations if no specific countries/states found
        $firstCountry = Country::first();
        $firstState = State::first();
        
        if ($firstCountry && $firstState) {
            Location::create([
                'name' => 'Downtown',
                'state_id' => $firstState->id,
                'country_id' => $firstCountry->id,
               
                'status' => 'active'
            ]);
            
            Location::create([
                'name' => 'Suburbs',
                'state_id' => $firstState->id,
                'country_id' => $firstCountry->id,
             
                'status' => 'active'
            ]);
        }
    }
}

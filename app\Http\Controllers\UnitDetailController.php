<?php

namespace App\Http\Controllers;

use App\Models\UnitDetail;
use App\Models\Project;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

class UnitDetailController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $projectId = $request->get('project_id');
        
        $query = UnitDetail::with('project');
        
        if ($projectId) {
            $query->where('project_id', $projectId);
        }
        
        $unitDetails = $query->orderBy('created_at', 'desc')->get();
        
        return response()->json([
            'status' => 'success',
            'data' => $unitDetails
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'unit_name' => 'nullable|string|max:255',
            'unit_type' => 'nullable|string|max:255',
            'status' => ['nullable', Rule::in(['available', 'sold', 'reserved', 'under_construction'])]
        ]);

        $unitDetail = UnitDetail::create($validated);
        $unitDetail->load('project');

        return response()->json([
            'status' => 'success',
            'message' => 'Unit detail created successfully',
            'data' => $unitDetail
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(UnitDetail $unitDetail)
    {
        $unitDetail->load('project');
        
        return response()->json([
            'status' => 'success',
            'data' => $unitDetail
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, UnitDetail $unitDetail)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'unit_name' => 'nullable|string|max:255',
            'unit_type' => 'nullable|string|max:255',
            'status' => ['nullable', Rule::in(['available', 'sold', 'reserved', 'under_construction'])]
        ]);

        $unitDetail->update($validated);
        $unitDetail->load('project');

        return response()->json([
            'status' => 'success',
            'message' => 'Unit detail updated successfully',
            'data' => $unitDetail
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(UnitDetail $unitDetail)
    {
        $unitDetail->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Unit detail deleted successfully'
        ]);
    }
}

import Swal from 'sweetalert2';

/**
 * Global SweetAlert2 configuration with elevated z-index
 * This ensures all SweetAlert2 instances appear above all other content
 */

// Set global defaults for all SweetAlert2 instances
const originalFire = Swal.fire;

// Override the fire method to apply global z-index configuration
Swal.fire = function(options = {}) {
  // Default configuration with high z-index
  const defaultConfig = {
    heightAuto: false,
    customClass: {
      container: 'swal2-z-index-high',
      backdrop: 'swal2-backdrop-z-index-high',
      popup: 'swal2-z-index-high',
      ...options.customClass
    },
    didOpen: (popup) => {
      // Apply inline z-index as backup to ensure maximum priority
      const container = popup.parentElement;
      if (container) {
        container.style.zIndex = '99999';
        container.style.position = 'relative';
      }
      
      const backdrop = document.querySelector('.swal2-backdrop');
      if (backdrop) {
        backdrop.style.zIndex = '99998';
      }
      
      popup.style.zIndex = '99999';
      popup.style.position = 'relative';

      // Call original didOpen if provided
      if (options.didOpen && typeof options.didOpen === 'function') {
        options.didOpen(popup);
      }
    }
  };

  // Merge user options with default config
  const mergedOptions = {
    ...defaultConfig,
    ...options,
    customClass: {
      ...defaultConfig.customClass,
      ...options.customClass
    }
  };

  return originalFire.call(this, mergedOptions);
};

// Override the mixin method as well for toast notifications
const originalMixin = Swal.mixin;
Swal.mixin = function(options = {}) {
  const defaultMixinConfig = {
    heightAuto: false,
    customClass: {
      container: 'swal2-z-index-high',
      backdrop: 'swal2-backdrop-z-index-high',
      popup: 'swal2-z-index-high',
      ...options.customClass
    },
    didOpen: (popup) => {
      // Apply inline z-index as backup
      const container = popup.parentElement;
      if (container) {
        container.style.zIndex = '99999';
        container.style.position = 'relative';
      }
      
      const backdrop = document.querySelector('.swal2-backdrop');
      if (backdrop) {
        backdrop.style.zIndex = '99998';
      }
      
      popup.style.zIndex = '99999';
      popup.style.position = 'relative';

      // Call original didOpen if provided
      if (options.didOpen && typeof options.didOpen === 'function') {
        options.didOpen(popup);
      }
    }
  };

  // Merge user options with default config
  const mergedOptions = {
    ...defaultMixinConfig,
    ...options,
    customClass: {
      ...defaultMixinConfig.customClass,
      ...options.customClass
    }
  };

  return originalMixin.call(this, mergedOptions);
};

// Export the configured Swal for explicit imports
export default Swal;

// Also make it available globally
window.Swal = Swal;

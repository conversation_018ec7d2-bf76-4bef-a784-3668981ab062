import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from '@/hooks/useTranslation';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Textarea
} from '@/components/ui/textarea';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  MapPin,
  Globe,
  Navigation,
  ChevronLeft,
  ChevronRight,
  Eye,
  EyeOff,
  Download,
  Building2
} from 'lucide-react';
import Swal from 'sweetalert2';
import { locationAPI } from '../../services/locationAPI';
import { stateAPI } from '../../services/stateAPI';
import { countryAPI } from '../../services/countryAPI';

const LocationPage = () => {
  const { t } = useTranslation();
  
  // Add error boundary for this component
  const [componentError, setComponentError] = useState(null);
  
  // Add fallback for translation function
  const translate = (key) => {
    try {
      return t ? t(key) : key;
    } catch (error) {
      console.warn('Translation error for key:', key);
      return key;
    }
  };

  // Add error handling
  React.useEffect(() => {
    const handleError = (error) => {
      setComponentError(error.message);
    };
    
    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (componentError) {
    return (
      <div className="p-6">
        <h2 className="text-2xl font-bold mb-4">Location Management</h2>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">Error: {componentError}</p>
          <button 
            onClick={() => setComponentError(null)} 
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
  
  // State management
  const [locations, setLocations] = useState([]);
  const [states, setStates] = useState([]);
  const [countries, setCountries] = useState([]);
  const [statesLoading, setStatesLoading] = useState(false);
  const [countriesLoading, setCountriesLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedState, setSelectedState] = useState('all');
  const [selectedCountry, setSelectedCountry] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage] = useState(15);
  
  // Dialog state
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingLocation, setEditingLocation] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    state_id: '',
    country_id: '',
    status: 'active'
  });
  const [formErrors, setFormErrors] = useState({});
  
  // Statistics
  const [statistics, setStatistics] = useState({
    total_locations: 0,
    active_locations: 0,
    inactive_locations: 0,
    locations_by_state: [],
    locations_by_country: []
  });

  // Load data on component mount
  useEffect(() => {
    console.log('LocationPage: Component mounted, loading data...');
    try {
      loadLocations();
      loadStates();
      loadCountries();
      loadStatistics();
    } catch (error) {
      console.error('LocationPage: Error in useEffect:', error);
      setComponentError(error.message);
    }
  }, [currentPage, searchTerm, selectedState, selectedCountry, selectedStatus]);

  // Load locations
  const loadLocations = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        per_page: itemsPerPage,
        search: searchTerm,
        state_id: selectedState === 'all' ? '' : selectedState,
        country_id: selectedCountry === 'all' ? '' : selectedCountry,
        status: selectedStatus === 'all' ? '' : selectedStatus
      };
      
      // Use the public dropdown endpoint since it's working
      let response = null;
      let endpoint = '';
      
      try {
        console.log('LocationPage: Using public locations dropdown endpoint...');
        endpoint = 'getLocationsDropdown';
        response = await locationAPI.getLocationsDropdown(params);
        console.log('LocationPage: Locations dropdown response:', response);
      } catch (dropdownError) {
        console.error('LocationPage: Dropdown endpoint failed:', dropdownError);
        throw dropdownError;
      }
      
      // Handle the response structure safely
      if (response && response.success && response.data) {
        let filteredData = response.data || [];
        
        // Apply client-side filtering for search and status since the dropdown might not support all filters
        if (searchTerm) {
          filteredData = filteredData.filter(location => 
            location.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            location.state?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            location.country?.name.toLowerCase().includes(searchTerm.toLowerCase())
          );
        }
        
        if (selectedStatus !== 'all') {
          filteredData = filteredData.filter(location => location.status === selectedStatus);
        }
        
        if (selectedState !== 'all') {
          filteredData = filteredData.filter(location => location.state_id === parseInt(selectedState));
        }
        
        if (selectedCountry !== 'all') {
          filteredData = filteredData.filter(location => location.country_id === parseInt(selectedCountry));
        }
        
        // Apply pagination manually
        const startIndex = (currentPage - 1) * itemsPerPage;
        const endIndex = startIndex + itemsPerPage;
        const paginatedData = filteredData.slice(startIndex, endIndex);
        
        setLocations(paginatedData);
        setTotalPages(Math.ceil(filteredData.length / itemsPerPage));
        setTotalItems(filteredData.length);
        
        console.log(`LocationPage: Loaded ${paginatedData.length} locations (${filteredData.length} total) using ${endpoint}`);
      } else {
        console.warn('LocationPage: Response not successful:', response);
        setLocations([]);
        setTotalPages(1);
        setTotalItems(0);
      }
      setError(null);
    } catch (err) {
      setError('Failed to load locations');
      console.error('LocationPage: Error loading locations:', err);
      console.error('LocationPage: Error details:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      });
      setLocations([]);
      setTotalPages(1);
      setTotalItems(0);
    } finally {
      setLoading(false);
    }
  };

  // Load states for dropdown
  const loadStates = async () => {
    try {
      setStatesLoading(true);
      console.log('LocationPage: Loading states...');
      
      // Try multiple endpoints as fallback
      let response = null;
      let endpoint = '';
      
      try {
        // First try the public endpoint
        endpoint = 'public.getStatesDropdown';
        console.log('LocationPage: Trying public states endpoint...');
        response = await stateAPI.public.getStatesDropdown();
      } catch (publicError) {
        console.warn('LocationPage: Public states endpoint failed:', publicError);
        
        try {
          // Fallback to authenticated endpoint
          endpoint = 'getStatesDropdown';
          console.log('LocationPage: Trying authenticated states endpoint...');
          response = await stateAPI.getStatesDropdown();
        } catch (authError) {
          console.warn('LocationPage: Authenticated states endpoint failed:', authError);
          throw authError;
        }
      }
      
      console.log('LocationPage: States response from', endpoint, ':', response);
      
      if (response && response.success && response.data) {
        setStates(response.data || []);
        console.log('LocationPage: States loaded successfully:', response.data.length, 'states');
      } else {
        console.warn('LocationPage: States response not successful:', response);
        setStates([]);
      }
    } catch (err) {
      console.error('Error loading states:', err);
      setStates([]);
    } finally {
      setStatesLoading(false);
    }
  };

  // Load countries for dropdown
  const loadCountries = async () => {
    try {
      setCountriesLoading(true);
      console.log('LocationPage: Loading countries...');
      
      // Try multiple endpoints as fallback
      let response = null;
      let endpoint = '';
      
      try {
        // First try the public endpoint
        endpoint = 'public.getCountriesDropdown';
        console.log('LocationPage: Trying public countries endpoint...');
        response = await countryAPI.public.getCountriesDropdown();
      } catch (publicError) {
        console.warn('LocationPage: Public countries endpoint failed:', publicError);
        
        try {
          // Fallback to authenticated endpoint
          endpoint = 'getCountriesDropdown';
          console.log('LocationPage: Trying authenticated countries endpoint...');
          response = await countryAPI.getCountriesDropdown();
        } catch (authError) {
          console.warn('LocationPage: Authenticated countries endpoint failed:', authError);
          throw authError;
        }
      }
      
      console.log('LocationPage: Countries response from', endpoint, ':', response);
      
      if (response && response.success && response.data) {
        setCountries(response.data || []);
        console.log('LocationPage: Countries loaded successfully:', response.data.length, 'countries');
      } else {
        console.warn('LocationPage: Countries response not successful:', response);
        setCountries([]);
      }
    } catch (err) {
      console.error('Error loading countries:', err);
      setCountries([]);
    } finally {
      setCountriesLoading(false);
    }
  };

  // Load statistics
  const loadStatistics = async () => {
    try {
      const response = await locationAPI.getStatistics();
      if (response && response.success && response.data) {
        setStatistics({
          total_locations: response.data.total_locations || 0,
          active_locations: response.data.active_locations || 0,
          inactive_locations: response.data.inactive_locations || 0,
          locations_by_state: response.data.locations_by_state || [],
          locations_by_country: response.data.locations_by_country || []
        });
      }
    } catch (err) {
      console.error('Error loading statistics:', err);
      // If statistics fail, calculate basic stats from the locations data
      if (locations.length > 0) {
        const activeCount = locations.filter(loc => loc.status === 'active').length;
        const inactiveCount = locations.filter(loc => loc.status === 'inactive').length;
        setStatistics({
          total_locations: locations.length,
          active_locations: activeCount,
          inactive_locations: inactiveCount,
          locations_by_state: [],
          locations_by_country: []
        });
      } else {
        setStatistics({
          total_locations: 0,
          active_locations: 0,
          inactive_locations: 0,
          locations_by_state: [],
          locations_by_country: []
        });
      }
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormErrors({});

    try {
      // Clean data - convert empty strings to null for optional fields
      const cleanData = {
        name: formData.name?.trim() || '',
        state_id: formData.state_id || null,
        country_id: formData.country_id || null,
        status: formData.status || 'active'
      };
    
      // Basic validation
      if (!cleanData.name.trim()) {
        Swal.fire({
          icon: 'warning',
          title: 'Validation Error',
          text: 'Location name is required.',
          confirmButtonText: 'OK'
        });
        return;
      }

      if (!cleanData.state_id) {
        Swal.fire({
          icon: 'warning',
          title: 'Validation Error',
          text: 'State/Province is required.',
          confirmButtonText: 'OK'
        });
        return;
      }
      
      console.log('LocationPage: Cleaned form data:', cleanData);
      
      let response;
      if (editingLocation) {
        response = await locationAPI.updateLocation(editingLocation.id, cleanData);
        Swal.fire({
          icon: 'success',
          title: 'Success',
          text: 'Location updated successfully'
        });
      } else {
        response = await locationAPI.createLocation(cleanData);
        Swal.fire({
          icon: 'success',
          title: 'Success',
          text: 'Location created successfully'
        });
      }
      
      setIsDialogOpen(false);
      resetForm();
      loadLocations();
      loadStatistics();
    } catch (err) {
      console.error('LocationPage: Submit error:', err);
      console.error('LocationPage: Error response:', err.response?.data);
      console.error('LocationPage: Error status:', err.response?.status);
      
      if (err.response?.status === 401) {
        Swal.fire({
          icon: 'error',
          title: 'Authentication Error',
          text: 'You are not authenticated. Please log in again.'
        });
      } else if (err.response?.status === 403) {
        Swal.fire({
          icon: 'error',
          title: 'Permission Error',
          text: 'You do not have permission to manage locations. Contact your administrator.'
        });
      } else if (err.response?.data?.errors) {
        setFormErrors(err.response.data.errors);
        console.log('LocationPage: Validation errors:', err.response.data.errors);
        
        // Show validation errors in a more user-friendly way
        const errorMessages = Object.entries(err.response.data.errors)
          .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
          .join('\n');
        
        Swal.fire({
          icon: 'error',
          title: 'Validation Errors',
          text: `Please fix the following errors:\n\n${errorMessages}`,
          confirmButtonText: 'OK'
        });
      } else {
        const errorMessage = err.response?.data?.message || err.message || 'Unknown error occurred';
        const message = editingLocation ? 'Failed to update location' : 'Failed to create location';
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: `${message}: ${errorMessage}`
        });
      }
    }
  };

  // Handle delete
  const handleDelete = async (location) => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: 'This will permanently delete the location.',
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Delete',
      cancelButtonText: 'Cancel'
    });

    if (result.isConfirmed) {
      try {
        await locationAPI.deleteLocation(location.id);
        Swal.fire({
          icon: 'success',
          title: 'Success',
          text: 'Location deleted successfully'
        });
        loadLocations();
        loadStatistics();
      } catch (err) {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: 'Failed to delete location'
        });
      }
    }
  };

  // Handle edit
  const handleEdit = (location) => {
    setEditingLocation(location);
    setFormData({
      name: location.name,
      state_id: location.state_id?.toString() || '',
      country_id: location.country_id?.toString() || '',
      status: location.status
    });
    setIsDialogOpen(true);
    // Ensure states and countries are loaded when dialog opens
    if (states.length === 0) {
      console.log('LocationPage: No states loaded, loading now...');
      loadStates();
    }
    if (countries.length === 0) {
      console.log('LocationPage: No countries loaded, loading now...');
      loadCountries();
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (location) => {
    try {
      await locationAPI.toggleStatus(location.id);
      loadLocations();
      loadStatistics();
    } catch (err) {
      Swal.fire({
        icon: 'error',
        title: 'Error',
        text: 'Failed to toggle location status'
      });
    }
  };

  // Handle add new
  const handleAddNew = () => {
    resetForm();
    setEditingLocation(null);
    setIsDialogOpen(true);
    // Ensure states and countries are loaded
    if (states.length === 0) {
      loadStates();
    }
    if (countries.length === 0) {
      loadCountries();
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      state_id: '',
      country_id: '',
      status: 'active'
    });
    setFormErrors({});
  };

  // Handle search
  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1); // Reset to first page on search
  };

  // Get visible page numbers for pagination
  const getVisiblePageNumbers = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  // Handle country change in form (update states based on country)
  const handleCountryChange = async (countryId) => {
    setFormData({...formData, country_id: countryId, state_id: ''});
    
    if (countryId) {
      try {
        const response = await stateAPI.public.getStatesByCountry(countryId);
        if (response.success && response.data) {
          setStates(response.data);
        }
      } catch (err) {
        console.error('Error loading states for country:', err);
        // Fallback to loading all states
        loadStates();
      }
    } else {
      loadStates(); // Load all states if no country selected
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-8 bg-gray-200 rounded w-48"></div>
            <div className="h-4 bg-gray-200 rounded w-64 mt-2"></div>
          </div>
          <div className="h-10 bg-gray-200 rounded w-32"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {[1, 2, 3, 4].map(i => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-6 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Location Management</h2>
          <p className="text-muted-foreground">Manage cities, districts, and other locations</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleAddNew} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Location
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-800">Total Locations</CardTitle>
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <Building2 className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900">{statistics.total_locations}</div>
            <p className="text-xs text-blue-600 mt-1">All registered locations</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-800">Active Locations</CardTitle>
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
              <Eye className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">{statistics.active_locations}</div>
            <p className="text-xs text-green-600 mt-1">Currently active locations</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-800">Inactive Locations</CardTitle>
            <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
              <EyeOff className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-900">{statistics.inactive_locations}</div>
            <p className="text-xs text-red-600 mt-1">Not currently active</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search locations..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="pl-8"
              />
            </div>
            <Select value={selectedCountry} onValueChange={setSelectedCountry}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Countries</SelectItem>
                {countries.map(country => (
                  <SelectItem key={country.id} value={country.id.toString()}>
                    {country.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedState} onValueChange={setSelectedState}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by state" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All States</SelectItem>
                {states.map(state => (
                  <SelectItem key={state.id} value={state.id.toString()}>
                    {state.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Data Table */}
      <Card>
        <CardHeader>
          <CardTitle>Locations ({totalItems} total)</CardTitle>
          <CardDescription>
            Showing {Math.min((currentPage - 1) * itemsPerPage + 1, totalItems)} to{' '}
            {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} locations
          </CardDescription>
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-center py-8">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={loadLocations} variant="outline">
                Try Again
              </Button>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>State/Province</TableHead>
                    <TableHead>Country</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {locations.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex flex-col items-center gap-3">
                          <Building2 className="h-12 w-12 text-gray-400" />
                          <p className="text-gray-600">No locations found</p>
                          <p className="text-sm text-gray-500">
                            {searchTerm || selectedCountry !== 'all' || selectedState !== 'all' || selectedStatus !== 'all'
                              ? 'Try adjusting your filters'
                              : 'Get started by adding your first location'
                            }
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    locations.map((location) => (
                      <TableRow key={location.id}>
                        <TableCell className="font-medium">{location.name}</TableCell>
                        <TableCell>{location.state?.name || 'N/A'}</TableCell>
                        <TableCell>{location.country?.name || 'N/A'}</TableCell>
                       
                        <TableCell>
                          <Badge
                            variant={location.status === 'active' ? 'default' : 'secondary'}
                            className={location.status === 'active' ? 'bg-green-100 text-green-800' : ''}
                          >
                            {location.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEdit(location)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleToggleStatus(location)}>
                                {location.status === 'active' ? (
                                  <>
                                    <EyeOff className="mr-2 h-4 w-4" />
                                    Deactivate
                                  </>
                                ) : (
                                  <>
                                    <Eye className="mr-2 h-4 w-4" />
                                    Activate
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => handleDelete(location)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-4">
                  <p className="text-sm text-muted-foreground">
                    Showing {Math.min((currentPage - 1) * itemsPerPage + 1, totalItems)} to{' '}
                    {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} results
                  </p>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <div className="flex gap-1">
                      {getVisiblePageNumbers().map((pageNumber, index) => {
                        if (pageNumber === '...') {
                          return <span key={index} className="px-2">...</span>;
                        }
                        return (
                          <Button
                            key={pageNumber}
                            variant={currentPage === pageNumber ? "default" : "outline"}
                            size="sm"
                            onClick={() => setCurrentPage(pageNumber)}
                          >
                            {pageNumber}
                          </Button>
                        );
                      })}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>
              {editingLocation ? 'Edit Location' : 'Add Location'}
            </DialogTitle>
            <DialogDescription>
              {editingLocation ? 'Edit the location information' : 'Add a new location to the system'}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Location Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className={formErrors.name ? 'border-red-500' : ''}
                />
                {formErrors.name && (
                  <p className="text-sm text-red-600 mt-1">{formErrors.name[0]}</p>
                )}
              </div>
              <div>
                <Label htmlFor="country_id">Country *</Label>
                <Select value={formData.country_id} onValueChange={handleCountryChange}>
                  <SelectTrigger className={formErrors.country_id ? 'border-red-500' : ''}>
                    <SelectValue placeholder={countriesLoading ? "Loading countries..." : "Select country"} />
                  </SelectTrigger>
                  <SelectContent>
                    {countriesLoading ? (
                      <SelectItem value="loading" disabled>Loading countries...</SelectItem>
                    ) : countries.length === 0 ? (
                      <SelectItem value="no-data" disabled>No countries available</SelectItem>
                    ) : (
                      countries.map(country => (
                        <SelectItem key={country.id} value={country.id.toString()}>
                          {country.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {formErrors.country_id && (
                  <p className="text-sm text-red-600 mt-1">{formErrors.country_id[0]}</p>
                )}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="state_id">State/Province *</Label>
                <Select value={formData.state_id} onValueChange={(value) => setFormData({...formData, state_id: value})}>
                  <SelectTrigger className={formErrors.state_id ? 'border-red-500' : ''}>
                    <SelectValue placeholder={statesLoading ? "Loading states..." : "Select state"} />
                  </SelectTrigger>
                  <SelectContent>
                    {statesLoading ? (
                      <SelectItem value="loading" disabled>Loading states...</SelectItem>
                    ) : states.length === 0 ? (
                      <SelectItem value="no-data" disabled>No states available</SelectItem>
                    ) : (
                      states.map(state => (
                        <SelectItem key={state.id} value={state.id.toString()}>
                          {state.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
                {formErrors.state_id && (
                  <p className="text-sm text-red-600 mt-1">{formErrors.state_id[0]}</p>
                )}
              </div>
              <div>
                <Label htmlFor="status">Status *</Label>
                <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
           
           
            
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                {editingLocation ? 'Update Location' : 'Create Location'}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default LocationPage;

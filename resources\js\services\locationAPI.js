import axios from 'axios';

// Determine the correct API base URL
const getApiBaseUrl = () => {
  // In development with <PERSON><PERSON> serve
  if (window.location.port === '5173') {
    return 'http://localhost:8000/api';
  }
  // In production or <PERSON><PERSON> served directly
  return '/api';
};

const API_BASE_URL = getApiBaseUrl();

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Location API methods
export const locationAPI = {
  // Get all locations
  getLocations: async (params = {}) => {
    try {
      const response = await api.get('/locations', { params });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get single location
  getLocation: async (id) => {
    try {
      const response = await api.get(`/locations/${id}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Create new location
  createLocation: async (locationData) => {
    try {
      const response = await api.post('/locations', locationData);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Update location
  updateLocation: async (id, locationData) => {
    try {
      const response = await api.put(`/locations/${id}`, locationData);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Delete location
  deleteLocation: async (id) => {
    try {
      const response = await api.delete(`/locations/${id}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Toggle location status
  toggleStatus: async (id) => {
    try {
      const response = await api.patch(`/locations/${id}/toggle-status`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get locations by state
  getLocationsByState: async (stateId, params = {}) => {
    try {
      const response = await api.get(`/locations/state/${stateId}`, { params });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get locations by country
  getLocationsByCountry: async (countryId, params = {}) => {
    try {
      const response = await api.get(`/locations/country/${countryId}`, { params });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get locations for dropdown
  getLocationsDropdown: async (params = {}) => {
    try {
      const response = await api.get('/locations-dropdown', { params });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Search locations
  searchLocations: async (params = {}) => {
    try {
      const response = await api.get('/locations-search', { params });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get statistics
  getStatistics: async () => {
    try {
      const response = await api.get('/locations-statistics');
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Public API methods (no authentication required)
  public: {
    // Get locations dropdown (public)
    getLocationsDropdown: async (params = {}) => {
      try {
        const response = await axios.get(`${API_BASE_URL}/locations-dropdown`, { params });
        return response.data;
      } catch (error) {
        throw error.response?.data || error.message;
      }
    },

    // Get locations by state (public)
    getLocationsByState: async (stateId, params = {}) => {
      try {
        const response = await axios.get(`${API_BASE_URL}/locations/state/${stateId}`, { params });
        return response.data;
      } catch (error) {
        throw error.response?.data || error.message;
      }
    },

    // Get locations by country (public)
    getLocationsByCountry: async (countryId, params = {}) => {
      try {
        const response = await axios.get(`${API_BASE_URL}/locations/country/${countryId}`, { params });
        return response.data;
      } catch (error) {
        throw error.response?.data || error.message;
      }
    }
  }
};

export default locationAPI;

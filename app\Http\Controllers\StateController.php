<?php

namespace App\Http\Controllers;

use App\Models\State;
use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class StateController extends Controller
{
    /**
     * Display a listing of the states.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = State::with('country:id,name,code');

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('code', 'LIKE', "%{$search}%")
                      ->orWhere('type', 'LIKE', "%{$search}%")
                      ->orWhere('capital', 'LIKE', "%{$search}%")
                      ->orWhereHas('country', function ($countryQuery) use ($search) {
                          $countryQuery->where('name', 'LIKE', "%{$search}%");
                      });
                });
            }

            // Filter by country
            if ($request->has('country_id') && $request->country_id) {
                $query->where('country_id', $request->country_id);
            }

            // Filter by status
            if ($request->has('status') && $request->status) {
                $query->where('status', $request->status);
            }

            // Filter by type
            if ($request->has('type') && $request->type) {
                $query->where('type', $request->type);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'name');
            $sortOrder = $request->get('sort_order', 'asc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 15);
            $states = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $states->items(),
                'pagination' => [
                    'current_page' => $states->currentPage(),
                    'last_page' => $states->lastPage(),
                    'per_page' => $states->perPage(),
                    'total' => $states->total()
                ],
                'message' => 'States retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving states: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created state in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // Log the incoming request data for debugging
            \Log::info('StateController: Creating state', [
                'user_id' => auth()->id(),
                'request_data' => $request->all(),
                'headers' => $request->headers->all()
            ]);

            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'code' => 'nullable|string|max:10',
                'type' => 'required|string|in:state,province,territory,region,district,federal_district',
                'country_id' => 'required|exists:countries,id',
               
                'status' => 'required|in:active,inactive'
            ]);

            \Log::info('StateController: Validation passed', ['validated_data' => $validatedData]);

            // Check for unique combination of name and country
            $existingState = State::where('name', $validatedData['name'])
                ->where('country_id', $validatedData['country_id'])
                ->first();

            if ($existingState) {
                return response()->json([
                    'success' => false,
                    'message' => 'A state with this name already exists in the selected country',
                    'errors' => ['name' => ['This state name already exists in the selected country']]
                ], 422);
            }

            $state = State::create($validatedData);
            $state->load('country:id,name,code');

            \Log::info('StateController: State created successfully', ['state_id' => $state->id, 'state_name' => $state->name]);

            return response()->json([
                'success' => true,
                'data' => $state,
                'message' => 'State created successfully'
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
            \Log::warning('StateController: Validation failed', [
                'errors' => $e->errors(),
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            \Log::error('StateController: Error creating state', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Error creating state: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified state.
     */
    public function show(State $state): JsonResponse
    {
        try {
            $state->load('country:id,name,code,continent');
            
            return response()->json([
                'success' => true,
                'data' => $state,
                'message' => 'State retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving state: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified state in storage.
     */
    public function update(Request $request, State $state): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'code' => 'nullable|string|max:10',
                'type' => 'required|string|in:state,province,territory,region,district,federal_district',
                'country_id' => 'required|exists:countries,id',
                'capital' => 'nullable|string|max:255',
                'timezone' => 'nullable|string|max:100',
                'latitude' => 'nullable|numeric|between:-90,90',
                'longitude' => 'nullable|numeric|between:-180,180',
                'description' => 'nullable|string|max:1000',
                'status' => 'required|in:active,inactive'
            ]);

            // Check for unique combination of name and country (excluding current state)
            $existingState = State::where('name', $validatedData['name'])
                ->where('country_id', $validatedData['country_id'])
                ->where('id', '!=', $state->id)
                ->first();

            if ($existingState) {
                return response()->json([
                    'success' => false,
                    'message' => 'A state with this name already exists in the selected country',
                    'errors' => ['name' => ['This state name already exists in the selected country']]
                ], 422);
            }

            $state->update($validatedData);
            $state->load('country:id,name,code');

            return response()->json([
                'success' => true,
                'data' => $state,
                'message' => 'State updated successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating state: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified state from storage.
     */
    public function destroy(State $state): JsonResponse
    {
        try {
            $stateName = $state->name;
            $state->delete();

            return response()->json([
                'success' => true,
                'message' => "State '{$stateName}' deleted successfully"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting state: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get states by country for dropdown (public access)
     */
    public function byCountry(Request $request, $countryId): JsonResponse
    {
        try {
            $query = State::select('id', 'name', 'code', 'type', 'capital')
                ->where('country_id', $countryId)
                ->where('status', 'active');

            // Search functionality for dropdown
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('code', 'LIKE', "%{$search}%")
                      ->orWhere('capital', 'LIKE', "%{$search}%");
                });
            }

            $states = $query->orderBy('name', 'asc')->get();

            return response()->json([
                'success' => true,
                'data' => $states,
                'total' => $states->count(),
                'message' => 'States retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving states: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get states for dropdown (public access)
     */
    public function dropdown(Request $request): JsonResponse
    {
        try {
            $query = State::select('id', 'name', 'code', 'type', 'country_id')
                ->with('country:id,name,code')
                ->where('status', 'active');

            // Search functionality for dropdown
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('code', 'LIKE', "%{$search}%")
                      ->orWhereHas('country', function ($countryQuery) use ($search) {
                          $countryQuery->where('name', 'LIKE', "%{$search}%");
                      });
                });
            }

            // Filter by country if provided
            if ($request->has('country_id') && $request->country_id) {
                $query->where('country_id', $request->country_id);
            }

            // Limit results for dropdown
            $limit = $request->get('limit', 100);
            $states = $query->orderBy('name', 'asc')->limit($limit)->get();

            return response()->json([
                'success' => true,
                'data' => $states,
                'total' => $states->count(),
                'message' => 'States retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving states: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get statistics for states.
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $statistics = [
                'total_states' => State::count(),
                'active_states' => State::where('status', 'active')->count(),
                'inactive_states' => State::where('status', 'inactive')->count(),
                'states_by_country' => State::select('country_id')
                    ->with('country:id,name')
                    ->get()
                    ->groupBy('country_id')
                    ->map(function ($states) {
                        return [
                            'country' => $states->first()->country->name,
                            'count' => $states->count()
                        ];
                    })
                    ->values(),
                'states_by_type' => State::select('type', DB::raw('count(*) as count'))
                    ->groupBy('type')
                    ->get()
                    ->pluck('count', 'type')
            ];

            return response()->json([
                'success' => true,
                'data' => $statistics,
                'message' => 'Statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle state status.
     */
    public function toggleStatus(State $state): JsonResponse
    {
        try {
            $state->status = $state->status === 'active' ? 'inactive' : 'active';
            $state->save();

            return response()->json([
                'success' => true,
                'data' => $state,
                'message' => "State status updated to {$state->status}"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating state status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Public search for states (no authentication required)
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $query = State::select('id', 'name', 'code', 'type', 'capital', 'country_id')
                ->with('country:id,name,code')
                ->where('status', 'active');

            // Search functionality
            if ($request->has('q') && $request->q) {
                $search = $request->q;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('code', 'LIKE', "%{$search}%")
                      ->orWhere('type', 'LIKE', "%{$search}%")
                      ->orWhere('capital', 'LIKE', "%{$search}%")
                      ->orWhereHas('country', function ($countryQuery) use ($search) {
                          $countryQuery->where('name', 'LIKE', "%{$search}%");
                      });
                });
            }

            // Pagination for search results
            $perPage = $request->get('per_page', 20);
            $states = $query->orderBy('name', 'asc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $states->items(),
                'pagination' => [
                    'current_page' => $states->currentPage(),
                    'last_page' => $states->lastPage(),
                    'per_page' => $states->perPage(),
                    'total' => $states->total()
                ],
                'message' => 'States search completed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error searching states: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Public index method for states (no authentication required)
     * Supports pagination and filtering for public access
     */
    public function publicIndex(Request $request): JsonResponse
    {
        try {
            $query = State::select('id', 'name', 'code', 'type', 'capital', 'country_id', 'status', 'created_at', 'updated_at')
                ->with('country:id,name,code')
                ->where('status', 'active'); // Only show active states for public access

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('code', 'LIKE', "%{$search}%")
                      ->orWhere('type', 'LIKE', "%{$search}%")
                      ->orWhere('capital', 'LIKE', "%{$search}%")
                      ->orWhereHas('country', function ($countryQuery) use ($search) {
                          $countryQuery->where('name', 'LIKE', "%{$search}%");
                      });
                });
            }

            // Filter by country
            if ($request->has('country_id') && $request->country_id) {
                $query->where('country_id', $request->country_id);
            }

            // Filter by type
            if ($request->has('type') && $request->type) {
                $query->where('type', $request->type);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'name');
            $sortOrder = $request->get('sort_order', 'asc');
            
            // Only allow sorting by safe columns
            $allowedSortColumns = ['name', 'code', 'type', 'capital', 'created_at', 'updated_at'];
            if (!in_array($sortBy, $allowedSortColumns)) {
                $sortBy = 'name';
            }
            
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 15);
            $perPage = min($perPage, 100); // Limit max per page for public endpoint
            $states = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $states->items(),
                'pagination' => [
                    'current_page' => $states->currentPage(),
                    'last_page' => $states->lastPage(),
                    'per_page' => $states->perPage(),
                    'total' => $states->total()
                ],
                'message' => 'States retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving states: ' . $e->getMessage()
            ], 500);
        }
    }
}

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  X, 
  Download, 
  ZoomIn, 
  ZoomOut,
  FileText,
  ExternalLink,
  RotateCw
} from 'lucide-react';

const DocumentModal = ({ 
  isOpen, 
  onClose, 
  document,
  title = "Document Viewer"
}) => {
  const [zoom, setZoom] = useState(1);
  const [rotation, setRotation] = useState(0);

  const handleDownload = () => {
    if (document?.url) {
      const link = document.createElement('a');
      link.href = document.url;
      link.download = document.filename || 'document';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 0.25, 3));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 0.25, 0.5));
  };

  const handleRotate = () => {
    setRotation(prev => (prev + 90) % 360);
  };

  const resetView = () => {
    setZoom(1);
    setRotation(0);
  };

  React.useEffect(() => {
    if (!isOpen) {
      resetView();
    } else if (document?.url) {
      // Log when modal opens with a document
      console.log('DocumentModal opened with document:', document);
    }
  }, [isOpen, document]);

  if (!document?.url) return null;

  const isPDF = document.url.toLowerCase().includes('.pdf');
  const isImage = /\.(jpg|jpeg|png|gif|webp|bmp)$/i.test(document.url);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[95vh] p-0 overflow-hidden">
        <DialogHeader className="p-4 border-b bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <DialogTitle className="text-lg font-semibold flex items-center gap-2">
                <FileText className="h-5 w-5" />
                {title}
              </DialogTitle>
              <DialogDescription className="mt-1">
                <div className="flex items-center gap-2 flex-wrap">
                  <span>{document.filename || 'Document'}</span>
                  {document.fileSize && (
                    <Badge variant="outline" className="text-xs">
                      {document.fileSize}
                    </Badge>
                  )}
                  {document.fileType && (
                    <Badge variant="outline" className="text-xs">
                      {document.fileType.toUpperCase()}
                    </Badge>
                  )}
                </div>
              </DialogDescription>
            </div>
            
            <div className="flex items-center space-x-2">
              {isImage && (
                <>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleZoomOut}
                    disabled={zoom <= 0.5}
                    title="Zoom Out"
                  >
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <span className="text-sm font-mono min-w-[3rem] text-center">
                    {Math.round(zoom * 100)}%
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleZoomIn}
                    disabled={zoom >= 3}
                    title="Zoom In"
                  >
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRotate}
                    title="Rotate"
                  >
                    <RotateCw className="h-4 w-4" />
                  </Button>
                </>
              )}
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(document.url, '_blank')}
                title="Open in new tab"
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
                title="Download"
              >
                <Download className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={onClose}
                title="Close"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </DialogHeader>
        
        <div className="flex-1 overflow-auto bg-gray-100 flex items-center justify-center p-4" style={{ minHeight: '70vh' }}>
          {isPDF ? (
            <div className="w-full h-full min-h-[70vh] border rounded-lg overflow-hidden bg-white">
              <iframe
                src={document.url}
                className="w-full h-full min-h-[70vh]"
                title={document.filename || 'Document'}
                style={{ border: 'none' }}
                onLoad={() => console.log('PDF loaded successfully')}
                onError={() => console.error('Failed to load PDF:', document.url)}
              />
            </div>
          ) : isImage ? (
            <div className="max-w-full max-h-full overflow-auto">
              <img
                src={document.url}
                alt={document.filename || 'Document'}
                className="max-w-none transition-all duration-200 cursor-zoom-in"
                style={{ 
                  transform: `scale(${zoom}) rotate(${rotation}deg)`,
                  transformOrigin: 'center center'
                }}
                onLoad={() => console.log('Image loaded successfully')}
                onError={(e) => {
                  console.error('Failed to load image:', document.url);
                  e.target.style.display = 'none';
                  e.target.nextElementSibling.style.display = 'flex';
                }}
              />
              <div className="hidden flex-col items-center justify-center text-gray-500 p-8">
                <FileText className="w-16 h-16 mb-4" />
                <p className="text-lg font-medium">Unable to preview this image</p>
                <p className="text-sm">Click "Open in new tab" to view the document</p>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center text-gray-500 p-8">
              <FileText className="w-16 h-16 mb-4" />
              <p className="text-lg font-medium">Document Preview</p>
              <p className="text-sm mb-4">This document type cannot be previewed inline</p>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => window.open(document.url, '_blank')}
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  Open in new tab
                </Button>
                <Button
                  variant="outline"
                  onClick={handleDownload}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  Download
                </Button>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentModal;

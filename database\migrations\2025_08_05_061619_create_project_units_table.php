<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_units', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->string('unit_number', 50);
            $table->string('unit_type', 50);
            $table->integer('floor_number')->nullable();
            $table->decimal('area_sqft', 10, 2)->nullable();
            $table->integer('bedrooms')->nullable();
            $table->integer('bathrooms')->nullable();
            $table->decimal('rent_price', 12, 2)->nullable();
            $table->decimal('sell_price', 12, 2)->nullable();
            $table->decimal('lease_price', 12, 2)->nullable();
            $table->string('currency', 3)->default('USD');
            $table->string('status', 50)->default('available');
            $table->text('description')->nullable();
            $table->json('features')->nullable();
            $table->boolean('is_available')->default(true);
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
            
            $table->unique(['project_id', 'unit_number']);
            $table->index(['unit_type', 'status']);
            $table->index(['floor_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_units');
    }
};

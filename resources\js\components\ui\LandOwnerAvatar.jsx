import React, { useState } from 'react';
import { Users } from 'lucide-react';

const LandOwnerAvatar = ({ 
  photoUrl, 
  ownerName, 
  size = 'md', 
  onClick, 
  className = '' 
}) => {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  // Helper function to get full image URL
  const getImageUrl = (photoPath) => {
    if (!photoPath) return null;
    
    // If it's already a full URL, return as is
    if (photoPath.startsWith('http://') || photoPath.startsWith('https://')) {
      return photoPath;
    }
    
    // If it starts with /storage, use the base URL
    if (photoPath.startsWith('/storage')) {
      return `${window.location.protocol}//${window.location.host}${photoPath}`;
    }
    
    // If it's just a filename or relative path, prepend the storage URL
    return `${window.location.protocol}//${window.location.host}/storage/landowners/${photoPath}`;
  };

  // Size configurations
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-20 h-20'
  };

  const iconSizes = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6', 
    lg: 'h-8 w-8',
    xl: 'h-10 w-10'
  };

  const fullImageUrl = getImageUrl(photoUrl);
  const hasValidImage = fullImageUrl && !imageError;

  const handleImageLoad = () => {
    setImageLoading(false);
    console.log('✅ Image loaded successfully:', fullImageUrl);
  };

  const handleImageError = (e) => {
    setImageLoading(false);
    setImageError(true);
    console.error('❌ Image failed to load:', e.target.src);
  };

  return (
    <div className={`flex items-center justify-center ${className}`}>
      {hasValidImage ? (
        <div className="relative">
          {imageLoading && (
            <div className={`${sizeClasses[size]} rounded-full border-2 border-gray-200 bg-gray-100 flex items-center justify-center animate-pulse`}>
              <div className="w-3 h-3 bg-gray-400 rounded-full animate-bounce"></div>
            </div>
          )}
          <img
            src={fullImageUrl}
            alt={`${ownerName}'s photo`}
            className={`${sizeClasses[size]} rounded-full object-cover border-2 border-gray-200 hover:border-blue-400 transition-all duration-200 cursor-pointer shadow-sm hover:shadow-md ${imageLoading ? 'hidden' : 'block'}`}
            onClick={() => onClick && onClick(photoUrl, ownerName)}
            onLoad={handleImageLoad}
            onError={handleImageError}
            title={`Click to view ${ownerName}'s photo`}
          />
        </div>
      ) : (
        <div 
          className={`${sizeClasses[size]} rounded-full border-2 border-gray-200 bg-gray-100 flex items-center justify-center text-gray-500 hover:bg-gray-200 transition-colors`}
          title={photoUrl ? "Failed to load image" : "No photo available"}
        >
          <Users className={iconSizes[size]} />
        </div>
      )}
    </div>
  );
};

export default LandOwnerAvatar;

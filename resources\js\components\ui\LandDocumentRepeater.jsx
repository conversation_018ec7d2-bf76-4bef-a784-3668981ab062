import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import FileUpload from '@/components/ui/FileUpload';
import DocumentModal from '@/components/ui/DocumentModal';
import { 
  Plus, 
  Trash2, 
  FileText, 
  Upload,
  Eye,
  Download
} from 'lucide-react';

// Helper function to get file URLs
const getFileUrl = (filePath) => {
  if (!filePath) return '';
  
  // Check if it's already a full URL
  if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
    return filePath;
  }
  
  // Get base URL from current location
  const baseUrl = `${window.location.protocol}//${window.location.host}`;
  
  // Ensure the path starts with /
  const cleanPath = filePath.startsWith('/') ? filePath : `/${filePath}`;
  
  return `${baseUrl}${cleanPath}`;
};

const LandDocumentRepeater = ({ 
  documents = [], 
  existingDocuments = [],
  onChange, 
  onDeleteExisting,
  disabled = false 
}) => {
  const [documentList, setDocumentList] = useState(documents);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);

  // Ensure existingDocuments is always an array
  const safeExistingDocuments = Array.isArray(existingDocuments) ? existingDocuments : [];

  const addDocument = () => {
    const newDocument = {
      id: Date.now(), // Temporary ID for new documents
      document_name: '',
      document_file: null,
      description: '',
      isNew: true
    };
    
    const updatedList = [...documentList, newDocument];
    setDocumentList(updatedList);
    onChange(updatedList);
  };

  const removeDocument = (index) => {
    const updatedList = documentList.filter((_, i) => i !== index);
    setDocumentList(updatedList);
    onChange(updatedList);
  };

  const updateDocument = (index, field, value) => {
    console.log('📄 LandDocumentRepeater: updateDocument called', {
      index,
      field,
      value: field === 'document_file' ? (value instanceof File ? `File(${value.name})` : value) : value,
      valueType: typeof value,
      isFile: value instanceof File
    });
    
    const updatedList = [...documentList];
    updatedList[index] = {
      ...updatedList[index],
      [field]: value
    };
    
    console.log('📄 LandDocumentRepeater: Updated document list:', updatedList);
    setDocumentList(updatedList);
    onChange(updatedList);
  };

  const handleViewDocument = (document) => {
    const documentData = {
      url: getFileUrl(document.document_file_path),
      filename: document.original_filename || document.document_name,
      fileSize: document.formatted_file_size,
      fileType: document.file_type
    };
    
    setSelectedDocument(documentData);
    setShowDocumentModal(true);
  };

  const getFileUrl = (filePath) => {
    if (!filePath) return '';
    
    if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
      return filePath;
    }
    
    if (filePath.startsWith('/landowners/')) {
      return `${window.location.protocol}//${window.location.host}${filePath}`;
    }
    
    return `${window.location.protocol}//${window.location.host}/landowners/land-documents/${filePath}`;
  };

  React.useEffect(() => {
    setDocumentList(documents);
  }, [documents]);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <Label className="text-base font-medium">Land Documents</Label>
          <p className="text-sm text-gray-500 mt-1">
            Upload documents related to this land acquisition (deeds, surveys, etc.)
          </p>
        </div>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={addDocument}
          disabled={disabled}
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Document
        </Button>
      </div>

      {/* Existing Documents Section */}
      {safeExistingDocuments && safeExistingDocuments.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-sm font-medium text-gray-700">Existing Documents</h4>
            <Badge variant="outline" className="text-xs">
              {safeExistingDocuments.length} existing
            </Badge>
          </div>
          {safeExistingDocuments.map((document, index) => (
            <Card key={`existing-${document.id}`} className="border-blue-200 bg-blue-50/30">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <FileText className="h-4 w-4 text-blue-600" />
                    {document.document_name}
                    <Badge variant="secondary" className="text-xs bg-blue-100 text-blue-700">
                      Existing
                    </Badge>
                  </CardTitle>
                  {onDeleteExisting && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => onDeleteExisting(document.id)}
                      disabled={disabled}
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {document.description && (
                  <p className="text-sm text-gray-600">{document.description}</p>
                )}
                <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
                  <div className="flex items-center space-x-3">
                    <FileText className="h-8 w-8 text-blue-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {document.original_filename || document.document_name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {document.formatted_file_size || 'Unknown size'} • 
                        {document.file_type?.toUpperCase() || 'Unknown type'}
                      </p>
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(getFileUrl(document.document_file_path), '_blank')}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const link = document.createElement('a');
                        link.href = getFileUrl(document.document_file_path);
                        link.download = document.original_filename || document.document_name;
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);
                      }}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* New Documents Section */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h4 className="text-sm font-medium text-gray-700">
            {existingDocuments?.length > 0 ? 'Add New Documents' : 'Documents'}
          </h4>
          {documentList.length > 0 && (
            <Badge variant="outline" className="text-xs">
              {documentList.length} new
            </Badge>
          )}
        </div>

      {documentList.length === 0 && safeExistingDocuments.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">No documents added yet</p>
            <p className="text-sm text-gray-400 mt-1">Click "Add Document" to upload files</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {/* Existing Documents */}
          {safeExistingDocuments.map((document, index) => (
            <Card key={`existing-${document.id}`} className="border-blue-200 bg-blue-50">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <FileText className="h-4 w-4 text-blue-600" />
                    {document.document_name}
                    <Badge variant="default" className="text-xs bg-blue-600">Existing</Badge>
                  </CardTitle>
                  {onDeleteExisting && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={() => onDeleteExisting(document.id)}
                      disabled={disabled}
                    >
                      <Trash2 className="h-4 w-4 text-red-500" />
                    </Button>
                  )}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Description */}
                {document.description && (
                  <div>
                    <Label>Description</Label>
                    <p className="text-sm text-gray-600 bg-white p-2 rounded border">
                      {document.description}
                    </p>
                  </div>
                )}

                {/* File Display */}
                <div>
                  <Label>Document File</Label>
                  <div className="flex items-center justify-between p-3 bg-white rounded-lg border">
                    <div className="flex items-center space-x-3">
                      <FileText className="h-8 w-8 text-blue-500" />
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {document.original_filename || document.document_name}
                        </p>
                        <p className="text-xs text-gray-500">
                          {document.formatted_file_size || 'Unknown size'} • 
                          {document.file_type?.toUpperCase() || 'Unknown type'}
                        </p>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(getFileUrl(document.document_file_path), '_blank')}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          const link = document.createElement('a');
                          link.href = getFileUrl(document.document_file_path);
                          link.download = document.original_filename || document.document_name;
                          document.body.appendChild(link);
                          link.click();
                          document.body.removeChild(link);
                        }}
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          {/* New Documents */}
          {documentList.map((document, index) => (
            <Card key={document.id || index}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <CardTitle className="text-sm font-medium flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Document {index + 1}
                    {document.isNew && (
                      <Badge variant="secondary" className="text-xs">New</Badge>
                    )}
                  </CardTitle>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeDocument(index)}
                    disabled={disabled}
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Document Name */}
                <div>
                  <Label htmlFor={`doc_name_${index}`}>Document Name *</Label>
                  <Input
                    id={`doc_name_${index}`}
                    type="text"
                    placeholder="e.g., Land Deed, Survey Report, etc."
                    value={document.document_name || ''}
                    onChange={(e) => updateDocument(index, 'document_name', e.target.value)}
                    disabled={disabled}
                    required
                  />
                </div>

                {/* Description */}
                <div>
                  <Label htmlFor={`doc_desc_${index}`}>Description</Label>
                  <Input
                    id={`doc_desc_${index}`}
                    type="text"
                    placeholder="Brief description of the document..."
                    value={document.description || ''}
                    onChange={(e) => updateDocument(index, 'description', e.target.value)}
                    disabled={disabled}
                  />
                </div>

                {/* File Upload */}
                <div>
                  <Label>Document File *</Label>
                  {document.isNew || !document.document_file_path ? (
                    <FileUpload
                      value={document.document_file}
                      onChange={(file) => updateDocument(index, 'document_file', file)}
                      label=""
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.gif,.webp"
                      placeholder="Upload document file (PDF, DOC, Images)"
                      disabled={disabled}
                      maxSize={10 * 1024 * 1024} // 10MB
                    />
                  ) : (
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border">
                      <div className="flex items-center space-x-3">
                        <FileText className="h-8 w-8 text-blue-500" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {document.original_filename || document.document_name}
                          </p>
                          <p className="text-xs text-gray-500">
                            {document.formatted_file_size || 'Unknown size'} • 
                            {document.file_type?.toUpperCase() || 'Unknown type'}
                          </p>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => window.open(getFileUrl(document.document_file_path), '_blank')}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const link = document.createElement('a');
                            link.href = getFileUrl(document.document_file_path);
                            link.download = document.original_filename || document.document_name;
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                          }}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => updateDocument(index, 'document_file', null)}
                        >
                          <Upload className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
      </div>

      {/* Document Modal */}
      {showDocumentModal && selectedDocument && (
        <DocumentModal
          isOpen={showDocumentModal}
          onClose={() => setShowDocumentModal(false)}
          document={selectedDocument}
        />
      )}
    </div>
  );
};

export default LandDocumentRepeater;

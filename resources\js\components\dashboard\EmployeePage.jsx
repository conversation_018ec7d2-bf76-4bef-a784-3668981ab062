import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import StandardModal from "@/components/ui/StandardModal";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Plus, Search, Edit, Trash2, Eye, EyeOff, MoreHorizontal, Users, 
  IdCard, Phone, Mail, Calendar, MapPin, DollarSign, Filter,
  Upload, Download, ChevronLeft, ChevronRight, UserX, User<PERSON>he<PERSON>,
  Building, AlertTriangle, CheckCircle
} from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useTranslation } from '../TranslationProvider';
import { useAuth } from '../../contexts/AuthContext';
import { showAlert } from '@/utils/sweetAlert';
import Swal from 'sweetalert2';
import employeeAPI from '../../services/employeeAPI';

const EmployeePage = () => {
  const { hasModuleAccess } = useAuth();
  const { t } = useTranslation();
  
  // State management
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [designationFilter, setDesignationFilter] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState(null);
  const [selectedEmployees, setSelectedEmployees] = useState([]);
  const [statistics, setStatistics] = useState({});
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [perPage, setPerPage] = useState(10);
  
  // Form data
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    designation: '',
    phone: '',
    email: '',
    status: 'active',
    date_of_joining: '',
    address: '',
    salary: '',
    photo: null
  });

  // Check permissions
  if (!hasModuleAccess('employees')) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Access Denied</h3>
          <p className="text-gray-500">You don't have permission to access employee management.</p>
        </div>
      </div>
    );
  }

  // Fetch employees
  const fetchEmployees = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        per_page: perPage,
        search: searchTerm,
        designation: designationFilter
      };
      
      // Only add status filter if it's not "all"
      if (statusFilter && statusFilter !== 'all') {
        params.status = statusFilter;
      }
      
      const response = await employeeAPI.getEmployees(params);
      setEmployees(response.data.data);
      setCurrentPage(response.data.current_page);
      setTotalPages(response.data.last_page);
    } catch (error) {
      console.error('Error fetching employees:', error);
      showAlert.error('Error', 'Failed to fetch employees');
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const response = await employeeAPI.getStatistics();
      setStatistics(response.data);
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  useEffect(() => {
    fetchEmployees();
    fetchStatistics();
  }, [currentPage, perPage]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchEmployees();
  };

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle file upload
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    setFormData(prev => ({
      ...prev,
      photo: file
    }));
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      first_name: '',
      last_name: '',
      designation: '',
      phone: '',
      email: '',
      status: 'active',
      date_of_joining: '',
      address: '',
      salary: '',
      photo: null
    });
  };

  // Handle add employee
  const handleAddEmployee = async (e) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.first_name.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'First name is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.designation.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Designation is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.email.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Email is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.phone.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Phone is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    
    try {
      const formDataToSend = new FormData();
      
      // Combine first_name and last_name into name
      const fullName = `${formData.first_name} ${formData.last_name}`.trim();
      formDataToSend.append('name', fullName);
      
      // Add other fields
      Object.keys(formData).forEach(key => {
        if (key !== 'first_name' && key !== 'last_name' && formData[key] !== null && formData[key] !== '') {
          formDataToSend.append(key, formData[key]);
        }
      });

      await employeeAPI.createEmployee(formDataToSend);
      
      // Close modal and reset form first
      setShowAddModal(false);
      resetForm();
      fetchEmployees();
      fetchStatistics();
      
      // Show success message
      Swal.fire({
        title: 'Success!',
        text: 'Employee created successfully',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error creating employee:', error);
      Swal.fire({
        title: 'Error!',
        text: error.response?.data?.message || 'Failed to create employee',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
    }
  };

  // Handle edit employee
  const handleEditEmployee = (employee) => {
    setEditingEmployee(employee);
    
    // Split the name into first and last name
    const nameParts = employee.name ? employee.name.trim().split(' ') : ['', ''];
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';
    
    setFormData({
      first_name: firstName,
      last_name: lastName,
      designation: employee.designation || '',
      phone: employee.phone || '',
      email: employee.email || '',
      status: employee.status || 'active',
      date_of_joining: employee.date_of_joining ? employee.date_of_joining.split('T')[0] : '',
      address: employee.address || '',
      salary: employee.salary || '',
      photo: null
    });
    setShowEditModal(true);
  };

  // Handle update employee
  const handleUpdateEmployee = async (e) => {
    e.preventDefault();
    
    // Validate required fields
    if (!formData.first_name.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'First name is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.designation.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Designation is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.email.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Email is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    if (!formData.phone.trim()) {
      Swal.fire({
        title: 'Validation Error',
        text: 'Phone is required',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
      return;
    }
    
    try {
      const formDataToSend = new FormData();
      
      // Combine first_name and last_name into name
      const fullName = `${formData.first_name} ${formData.last_name}`.trim();
      formDataToSend.append('name', fullName);
      
      // Add other fields
      Object.keys(formData).forEach(key => {
        if (key !== 'first_name' && key !== 'last_name' && formData[key] !== null && formData[key] !== '') {
          formDataToSend.append(key, formData[key]);
        }
      });

      // Log the data being sent for debugging
      console.log('Sending employee update request:', {
        employeeId: editingEmployee.id,
        formDataEntries: Array.from(formDataToSend.entries())
      });

      await employeeAPI.updateEmployee(editingEmployee.id, formDataToSend);
      
      // Close modal and reset form first
      setShowEditModal(false);
      setEditingEmployee(null);
      resetForm();
      fetchEmployees();
      fetchStatistics();
      
      // Show success message
      Swal.fire({
        title: 'Success!',
        text: 'Employee updated successfully',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error updating employee:', error);
      console.error('Error response:', error.response?.data);
      console.error('Error status:', error.response?.status);
      console.error('Error headers:', error.response?.headers);
      
      // Show more detailed error message
      let errorMessage = 'Failed to update employee';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.response?.data?.errors) {
        const errorMessages = Object.values(error.response.data.errors).flat();
        errorMessage = errorMessages.join(', ');
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      Swal.fire({
        title: 'Error!',
        text: errorMessage,
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
    }
  };

  // Handle delete employee
  const handleDeleteEmployee = async (employee) => {
    console.log('Delete employee button clicked for:', employee.name, 'ID:', employee.id);
    
    try {
      // Use direct Swal.fire for confirmation modal
      const result = await Swal.fire({
        title: 'Delete Employee',
        text: `Are you sure you want to delete ${employee.name}? This action cannot be undone.`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel'
      });

      console.log('Employee deletion confirmation result:', result);

      if (!result.isConfirmed) {
        console.log('User cancelled employee deletion');
        return;
      }

      console.log('User confirmed employee deletion, proceeding...');
      await employeeAPI.deleteEmployee(employee.id);
      fetchEmployees();
      fetchStatistics();
      
      Swal.fire({
        title: 'Deleted!',
        text: 'Employee has been deleted successfully.',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    } catch (error) {
      console.error('Error deleting employee:', error);
      Swal.fire({
        title: 'Error!',
        text: error.response?.data?.message || 'Failed to delete employee',
        icon: 'error',
        confirmButtonColor: '#ef4444'
      });
    }
  };

  // Handle status toggle
  const handleStatusToggle = async (employee) => {
    const newStatus = employee.status === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? 'activate' : 'deactivate';
    
    const result = await showAlert.confirm(
      `${action.charAt(0).toUpperCase() + action.slice(1)} Employee`,
      `Are you sure you want to ${action} ${employee.name}?`,
      action.charAt(0).toUpperCase() + action.slice(1),
      'Cancel'
    );

    if (result.isConfirmed) {
      try {
        await employeeAPI.bulkStatusUpdate({
          employee_ids: [employee.id],
          status: newStatus
        });
        showAlert.success('Success', `Employee ${action}d successfully`);
        fetchEmployees();
        fetchStatistics();
      } catch (error) {
        console.error('Error updating employee status:', error);
        showAlert.error('Error', 'Failed to update employee status');
      }
    }
  };

  // Handle checkbox change for bulk operations
  const handleCheckboxChange = (employeeId, checked) => {
    if (checked) {
      setSelectedEmployees(prev => [...prev, employeeId]);
    } else {
      setSelectedEmployees(prev => prev.filter(id => id !== employeeId));
    }
  };

  // Handle bulk status update
  const handleBulkStatusUpdate = async (status) => {
    if (selectedEmployees.length === 0) {
      showAlert.warning('Warning', 'Please select employees to update');
      return;
    }

    const action = status === 'active' ? 'activate' : 'deactivate';
    const result = await showAlert.confirm(
      `Bulk ${action.charAt(0).toUpperCase() + action.slice(1)}`,
      `Are you sure you want to ${action} ${selectedEmployees.length} employees?`,
      `${action.charAt(0).toUpperCase() + action.slice(1)} All`,
      'Cancel'
    );

    if (result.isConfirmed) {
      try {
        await employeeAPI.bulkStatusUpdate({
          employee_ids: selectedEmployees,
          status: status
        });
        showAlert.success('Success', `Employees ${action}d successfully`);
        setSelectedEmployees([]);
        fetchEmployees();
        fetchStatistics();
      } catch (error) {
        console.error('Error updating employees:', error);
        showAlert.error('Error', 'Failed to update employees');
      }
    }
  };

  // Get status color
  const getStatusColor = (status) => {
    return status === 'active' 
      ? 'bg-green-100 text-green-800' 
      : 'bg-red-100 text-red-800';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Employee Management</h1>
          <p className="text-muted-foreground">
            Manage company employees and their information
          </p>
        </div>
        <Button onClick={() => setShowAddModal(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Employee
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-800">Total Employees</CardTitle>
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <Users className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900">{statistics.total_employees || 0}</div>
            <p className="text-xs text-blue-600 mt-1">All registered employees</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-800">Active Employees</CardTitle>
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
              <CheckCircle className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">{statistics.active_employees || 0}</div>
            <p className="text-xs text-green-600 mt-1">Currently working employees</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-800">Inactive Employees</CardTitle>
            <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
              <UserX className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-900">{statistics.inactive_employees || 0}</div>
            <p className="text-xs text-red-600 mt-1">Not currently active</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-800">Recent Joinings</CardTitle>
            <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
              <Calendar className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900">{statistics.recent_joinings || 0}</div>
            <p className="text-xs text-purple-600 mt-1">Last 30 days</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            Search & Filter
          </CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSearch} className="space-y-4">
            <div className="grid gap-4 md:grid-cols-4">
              <div>
                <Label htmlFor="search">Search</Label>
                <Input
                  id="search"
                  placeholder="Search by name, email, phone..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="status-filter">Status</Label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="designation-filter">Designation</Label>
                <Input
                  id="designation-filter"
                  placeholder="Filter by designation"
                  value={designationFilter}
                  onChange={(e) => setDesignationFilter(e.target.value)}
                />
              </div>
              <div className="flex items-end">
                <Button type="submit" className="w-full">
                  <Search className="mr-2 h-4 w-4" />
                  Search
                </Button>
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Bulk Actions */}
      {selectedEmployees.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <span className="text-sm text-muted-foreground">
                {selectedEmployees.length} employee(s) selected
              </span>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkStatusUpdate('active')}
                >
                  <UserCheck className="mr-2 h-4 w-4" />
                  Activate
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBulkStatusUpdate('inactive')}
                >
                  <UserX className="mr-2 h-4 w-4" />
                  Deactivate
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Employees Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IdCard className="h-5 w-5" />
            Employees List
          </CardTitle>
          <CardDescription>
            Manage all company employees
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedEmployees.length === employees.length && employees.length > 0}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedEmployees(employees.map(emp => emp.id));
                            } else {
                              setSelectedEmployees([]);
                            }
                          }}
                        />
                      </TableHead>
                      <TableHead>Employee</TableHead>
                      <TableHead>Contact</TableHead>
                      <TableHead>Position</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Joining Date</TableHead>
                      <TableHead>Salary</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {employees.map((employee) => (
                      <TableRow key={employee.id}>
                        <TableCell>
                          <Checkbox
                            checked={selectedEmployees.includes(employee.id)}
                            onCheckedChange={(checked) => handleCheckboxChange(employee.id, checked)}
                          />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                              {employee.photo ? (
                                <img 
                                  src={employee.photo_url} 
                                  alt={employee.name}
                                  className="h-10 w-10 rounded-full object-cover"
                                />
                              ) : (
                                <span className="text-sm font-medium text-blue-600">
                                  {employee.name.charAt(0)}
                                </span>
                              )}
                            </div>
                            <div>
                              <div className="font-medium">{employee.name}</div>
                              <div className="text-sm text-muted-foreground">ID: {employee.id}</div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center gap-2">
                              <Mail className="h-3 w-3 text-muted-foreground" />
                              <span className="text-sm">{employee.email}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Phone className="h-3 w-3 text-muted-foreground" />
                              <span className="text-sm">{employee.phone}</span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Building className="h-4 w-4 text-muted-foreground" />
                            <span>{employee.designation}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={`${getStatusColor(employee.status)} border-0`}>
                            {employee.status === 'active' ? (
                              <CheckCircle className="mr-1 h-3 w-3" />
                            ) : (
                              <UserX className="mr-1 h-3 w-3" />
                            )}
                            {employee.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {employee.date_of_joining ? (
                            <div className="flex items-center gap-2">
                              <Calendar className="h-3 w-3 text-muted-foreground" />
                              <span className="text-sm">
                                {new Date(employee.date_of_joining).toLocaleDateString()}
                              </span>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell>
                          {employee.salary ? (
                            <div className="flex items-center gap-2">
                              <DollarSign className="h-3 w-3 text-muted-foreground" />
                              <span className="text-sm">
                                ${Number(employee.salary).toLocaleString()}
                              </span>
                            </div>
                          ) : (
                            <span className="text-muted-foreground">-</span>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleEditEmployee(employee)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </DropdownMenuItem>
                              {employee.status === 'active' ? (
                                <DropdownMenuItem
                                  onClick={() => handleStatusToggle(employee)}
                                  className="text-orange-600"
                                >
                                  <UserX className="mr-2 h-4 w-4" />
                                  Deactivate
                                </DropdownMenuItem>
                              ) : (
                                <DropdownMenuItem
                                  onClick={() => handleStatusToggle(employee)}
                                  className="text-green-600"
                                >
                                  <UserCheck className="mr-2 h-4 w-4" />
                                  Activate
                                </DropdownMenuItem>
                              )}
                              <DropdownMenuItem
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  console.log('Employee delete dropdown clicked!', employee.id);
                                  handleDeleteEmployee(employee);
                                }}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between space-x-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Page {currentPage} of {totalPages}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage - 1)}
                      disabled={currentPage <= 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(currentPage + 1)}
                      disabled={currentPage >= totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Add Employee Modal */}
      <StandardModal 
        showModal={showAddModal} 
        closeModal={() => setShowAddModal(false)}
        modalMode="create"
        title="Add New Employee"
        icon={Plus}
        maxWidth="max-w-2xl"
      >
        <div className="text-center pb-4 border-b border-gray-200 dark:border-gray-700">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
            <IdCard className="w-8 h-8 text-white" />
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Create a new employee record with their basic information.
          </p>
        </div>

        <form onSubmit={handleAddEmployee} className="space-y-6">
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="first_name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">First Name *</Label>
                <Input
                  id="first_name"
                  value={formData.first_name}
                  onChange={(e) => handleInputChange('first_name', e.target.value)}
                  required
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                />
              </div>
              <div>
                <Label htmlFor="last_name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Last Name</Label>
                <Input
                  id="last_name"
                  value={formData.last_name}
                  onChange={(e) => handleInputChange('last_name', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="designation" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Designation *</Label>
                <Input
                  id="designation"
                  value={formData.designation}
                  onChange={(e) => handleInputChange('designation', e.target.value)}
                  required
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email *</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    required
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone *</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    required
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                  />
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                  <SelectTrigger className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="date_of_joining" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date of Joining</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="date_of_joining"
                    type="date"
                    value={formData.date_of_joining}
                    onChange={(e) => handleInputChange('date_of_joining', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                  />
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="salary" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Salary</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="salary"
                    type="number"
                    step="0.01"
                    value={formData.salary}
                    onChange={(e) => handleInputChange('salary', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="photo" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Photo</Label>
                <Input
                  id="photo"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="address" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 text-gray-400 w-4 h-4" />
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  rows={3}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md resize-none"
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => setShowAddModal(false)}
              className="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-xl font-medium transition-all duration-200 shadow-sm hover:shadow-md"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              <Plus className="mr-2 h-4 w-4 inline" />
              Add Employee
            </button>
          </div>
        </form>
      </StandardModal>

      {/* Edit Employee Modal */}
      <StandardModal 
        showModal={showEditModal} 
        closeModal={() => setShowEditModal(false)}
        modalMode="edit"
        title="Edit Employee"
        icon={Edit}
        maxWidth="max-w-2xl"
      >
        <div className="text-center pb-4 border-b border-gray-200 dark:border-gray-700">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
            <IdCard className="w-8 h-8 text-white" />
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            Update employee information.
          </p>
        </div>

        <form onSubmit={handleUpdateEmployee} className="space-y-6">
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-first-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">First Name *</Label>
                <Input
                  id="edit-first-name"
                  value={formData.first_name}
                  onChange={(e) => handleInputChange('first_name', e.target.value)}
                  required
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                />
              </div>
              <div>
                <Label htmlFor="edit-last-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Last Name</Label>
                <Input
                  id="edit-last-name"
                  value={formData.last_name}
                  onChange={(e) => handleInputChange('last_name', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                />
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-designation" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Designation *</Label>
                <Input
                  id="edit-designation"
                  value={formData.designation}
                  onChange={(e) => handleInputChange('designation', e.target.value)}
                  required
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                />
              </div>
              <div>
                <Label htmlFor="edit-email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Email *</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="edit-email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    required
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                  />
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Phone *</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="edit-phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    required
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="edit-status" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                  <SelectTrigger className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-date_of_joining" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Date of Joining</Label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="edit-date_of_joining"
                    type="date"
                    value={formData.date_of_joining}
                    onChange={(e) => handleInputChange('date_of_joining', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                  />
                </div>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="edit-salary" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Salary</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    id="edit-salary"
                    type="number"
                    step="0.01"
                    value={formData.salary}
                    onChange={(e) => handleInputChange('salary', e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="edit-photo" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Photo</Label>
                <Input
                  id="edit-photo"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                />
                {editingEmployee?.photo && (
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Current photo will be replaced if new file is selected
                  </p>
                )}
              </div>
            </div>
            <div>
              <Label htmlFor="edit-address" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Address</Label>
              <div className="relative">
                <MapPin className="absolute left-3 top-3 text-gray-400 w-4 h-4" />
                <Textarea
                  id="edit-address"
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  rows={3}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md resize-none"
                />
              </div>
            </div>
          </div>
          <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={() => setShowEditModal(false)}
              className="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-xl font-medium transition-all duration-200 shadow-sm hover:shadow-md"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            >
              <Edit className="mr-2 h-4 w-4 inline" />
              Update Employee
            </button>
          </div>
        </form>
      </StandardModal>
    </div>
  );
};

export default EmployeePage;

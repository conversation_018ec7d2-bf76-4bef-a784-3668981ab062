<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('land_owners', function (Blueprint $table) {
            // Add document_type column after photo column
            $table->enum('document_type', ['nid', 'passport'])->nullable()->after('photo');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('land_owners', function (Blueprint $table) {
            $table->dropColumn('document_type');
        });
    }
};

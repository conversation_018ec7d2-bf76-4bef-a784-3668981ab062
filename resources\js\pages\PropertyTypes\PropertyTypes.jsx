import React, { useState, useEffect } from 'react';
import { 
    Plus, 
    Search,
    Filter,
    Eye,
    Edit,
    Trash2,
    Power
} from 'lucide-react';
import PropertyTypeModal from './PropertyTypeModal';
import propertyTypeAPI from '../../services/propertyTypeAPI';
import { useAuth } from '../../contexts/AuthContext';
import { showAlert } from '../../utils/sweetAlert';

const PropertyTypes = () => {
    const { user, hasPermission } = useAuth();
    const [propertyTypes, setPropertyTypes] = useState([]);
    const [loading, setLoading] = useState(true);
    const [pagination, setPagination] = useState({});
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('');
    const [sortBy, setSortBy] = useState('sort_order');
    const [sortDirection, setSortDirection] = useState('asc');
    
    // Modal states
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalMode, setModalMode] = useState('create'); // 'create', 'view', 'edit'
    const [selectedPropertyType, setSelectedPropertyType] = useState(null);

    // Load property types
    const loadPropertyTypes = async (page = 1) => {
        try {
            setLoading(true);
            const params = {
                page,
                search: searchTerm,
                status: statusFilter,
                sort_by: sortBy,
                sort_direction: sortDirection,
                per_page: 15
            };

            const response = await propertyTypeAPI.getAll(params);
            
            if (response.success) {
                setPropertyTypes(response.data.data);
                setPagination(response.data);
            }
        } catch (error) {
            console.error('Failed to load property types:', error);
            showAlert('error', 'Error', 'Failed to load property types');
        } finally {
            setLoading(false);
        }
    };

    // Effects
    useEffect(() => {
        // Set document title
        document.title = 'Property Types - Real Estate Management';
        loadPropertyTypes();
    }, [searchTerm, statusFilter, sortBy, sortDirection]);

    // Handlers
    const handleSearch = (e) => {
        setSearchTerm(e.target.value);
    };

    const handleStatusFilter = (status) => {
        setStatusFilter(status);
    };

    const handleSort = (field) => {
        if (sortBy === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortBy(field);
            setSortDirection('asc');
        }
    };

    const handleCreate = () => {
        setSelectedPropertyType(null);
        setModalMode('create');
        setIsModalOpen(true);
    };

    const handleView = (propertyType) => {
        setSelectedPropertyType(propertyType);
        setModalMode('view');
        setIsModalOpen(true);
    };

    const handleEdit = (propertyType) => {
        setSelectedPropertyType(propertyType);
        setModalMode('edit');
        setIsModalOpen(true);
    };

    const handleDelete = async (propertyType) => {
        if (!confirm(`Are you sure you want to delete "${propertyType.name}"?`)) {
            return;
        }

        try {
            const response = await propertyTypeAPI.delete(propertyType.id);
            
            if (response.success) {
                showAlert('success', 'Success', 'Property type deleted successfully');
                loadPropertyTypes();
            }
        } catch (error) {
            console.error('Failed to delete property type:', error);
            showAlert('error', 'Error', 'Failed to delete property type');
        }
    };

    const handleToggleStatus = async (propertyType) => {
        try {
            const response = await propertyTypeAPI.toggleStatus(propertyType.id);
            
            if (response.success) {
                showAlert('success', 'Success', 'Property type status updated successfully');
                loadPropertyTypes();
            }
        } catch (error) {
            console.error('Failed to update property type status:', error);
            showAlert('error', 'Error', 'Failed to update property type status');
        }
    };

    const handleModalSuccess = () => {
        setIsModalOpen(false);
        loadPropertyTypes();
        showAlert(
            'success',
            'Success', 
            modalMode === 'create' 
                ? 'Property type created successfully' 
                : 'Property type updated successfully'
        );
    };

    if (loading && propertyTypes.length === 0) {
        return (
            <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
            </div>
        );
    }

    return (
        <>
            <div className="space-y-6">
                {/* Header */}
                <div className="flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-semibold text-gray-900">Property Types</h1>
                        <p className="text-gray-600">Manage property type configurations</p>
                    </div>
                    
                    {hasPermission('property-type', 'create') && (
                        <button
                            onClick={handleCreate}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 transition-colors"
                        >
                            <Plus className="h-4 w-4" />
                            Add Property Type
                        </button>
                    )}
                </div>

                {/* Search and Filters */}
                <div className="bg-white p-4 rounded-lg shadow border">
                    <div className="flex flex-wrap gap-4 items-center">
                        {/* Search */}
                        <div className="flex-1 min-w-64">
                            <div className="relative">
                                <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                                <input
                                    type="text"
                                    placeholder="Search property types..."
                                    value={searchTerm}
                                    onChange={handleSearch}
                                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                />
                            </div>
                        </div>

                        {/* Status Filter */}
                        <div className="flex items-center gap-2">
                            <Filter className="h-5 w-5 text-gray-400" />
                            <select
                                value={statusFilter}
                                onChange={(e) => handleStatusFilter(e.target.value)}
                                className="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                </div>

                {/* Property Types Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {propertyTypes.map((propertyType) => (
                        <div key={propertyType.id} className="bg-white rounded-lg shadow border hover:shadow-lg transition-shadow">
                            {/* Header */}
                            <div 
                                className="p-4 border-b"
                                style={{ backgroundColor: `${propertyType.color}10` }}
                            >
                                <div className="flex items-center gap-3">
                                    <div 
                                        className="w-10 h-10 rounded-lg flex items-center justify-center text-white"
                                        style={{ backgroundColor: propertyType.color }}
                                    >
                                        <span className="text-sm font-medium">
                                            {propertyType.icon || '🏢'}
                                        </span>
                                    </div>
                                    <div className="flex-1">
                                        <h3 className="font-semibold text-gray-900">
                                            {propertyType.name}
                                        </h3>
                                        <div className="flex items-center gap-2">
                                            <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                                                propertyType.status === 'active'
                                                    ? 'bg-green-100 text-green-800'
                                                    : 'bg-red-100 text-red-800'
                                            }`}>
                                                {propertyType.status}
                                            </span>
                                            <span className="text-xs text-gray-500">
                                                #{propertyType.sort_order}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Content */}
                            <div className="p-4">
                                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                                    {propertyType.description || 'No description available'}
                                </p>

                                {propertyType.features && propertyType.features.length > 0 && (
                                    <div className="mb-3">
                                        <div className="flex flex-wrap gap-1">
                                            {propertyType.features.slice(0, 3).map((feature, index) => (
                                                <span 
                                                    key={index}
                                                    className="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded"
                                                >
                                                    {feature}
                                                </span>
                                            ))}
                                            {propertyType.features.length > 3 && (
                                                <span className="text-xs text-gray-500">
                                                    +{propertyType.features.length - 3} more
                                                </span>
                                            )}
                                        </div>
                                    </div>
                                )}

                              
                            </div>

                            {/* Actions */}
                            <div className="px-4 pb-4">
                                <div className="flex items-center gap-2">
                                    <button
                                        onClick={() => handleView(propertyType)}
                                        className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-2 px-3 rounded-lg text-sm flex items-center justify-center gap-1 transition-colors"
                                    >
                                        <Eye className="h-4 w-4" />
                                        View
                                    </button>

                                    {hasPermission('property-type', 'update') && (
                                        <button
                                            onClick={() => handleEdit(propertyType)}
                                            className="flex-1 bg-blue-100 hover:bg-blue-200 text-blue-700 py-2 px-3 rounded-lg text-sm flex items-center justify-center gap-1 transition-colors"
                                        >
                                            <Edit className="h-4 w-4" />
                                            Edit
                                        </button>
                                    )}

                                    {hasPermission('property-type', 'update') && (
                                        <button
                                            onClick={() => handleToggleStatus(propertyType)}
                                            className={`p-2 rounded-lg transition-colors ${
                                                propertyType.status === 'active'
                                                    ? 'bg-red-100 hover:bg-red-200 text-red-700'
                                                    : 'bg-green-100 hover:bg-green-200 text-green-700'
                                            }`}
                                            title={propertyType.status === 'active' ? 'Deactivate' : 'Activate'}
                                        >
                                            <Power className="h-4 w-4" />
                                        </button>
                                    )}

                                    {hasPermission('property-type', 'delete') && (
                                        <button
                                            onClick={() => handleDelete(propertyType)}
                                            className="p-2 bg-red-100 hover:bg-red-200 text-red-700 rounded-lg transition-colors"
                                            title="Delete"
                                        >
                                            <Trash2 className="h-4 w-4" />
                                        </button>
                                    )}
                                </div>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Empty State */}
                {!loading && propertyTypes.length === 0 && (
                    <div className="text-center py-12">
                        <div className="mx-auto h-12 w-12 text-gray-400">
                            🏢
                        </div>
                        <h3 className="mt-2 text-sm font-medium text-gray-900">No property types found</h3>
                        <p className="mt-1 text-sm text-gray-500">
                            {searchTerm || statusFilter 
                                ? 'Try adjusting your search criteria.' 
                                : 'Get started by creating a new property type.'
                            }
                        </p>
                        {hasPermission('property-type', 'create') && !searchTerm && !statusFilter && (
                            <div className="mt-6">
                                <button
                                    onClick={handleCreate}
                                    className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2 mx-auto transition-colors"
                                >
                                    <Plus className="h-4 w-4" />
                                    Add Property Type
                                </button>
                            </div>
                        )}
                    </div>
                )}

                {/* Pagination */}
                {pagination.last_page > 1 && (
                    <div className="flex justify-center">
                        <div className="flex items-center gap-2">
                            <button
                                onClick={() => loadPropertyTypes(pagination.current_page - 1)}
                                disabled={pagination.current_page <= 1}
                                className="px-3 py-1 border rounded disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Previous
                            </button>
                            <span className="px-3 py-1">
                                Page {pagination.current_page} of {pagination.last_page}
                            </span>
                            <button
                                onClick={() => loadPropertyTypes(pagination.current_page + 1)}
                                disabled={pagination.current_page >= pagination.last_page}
                                className="px-3 py-1 border rounded disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                Next
                            </button>
                        </div>
                    </div>
                )}

                {/* Modal */}
                {isModalOpen && (
                    <PropertyTypeModal
                        isOpen={isModalOpen}
                        onClose={() => setIsModalOpen(false)}
                        onSuccess={handleModalSuccess}
                        mode={modalMode}
                        propertyType={selectedPropertyType}
                    />
                )}
            </div>
        </>
    );
};

export default PropertyTypes;

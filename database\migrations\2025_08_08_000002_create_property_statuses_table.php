<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('property_statuses', function (Blueprint $table) {
            $table->id();
            $table->string('name', 100)->unique();
            $table->string('slug', 100)->unique();
            $table->text('description')->nullable();
            $table->string('color', 7)->default('#3B82F6'); // Hex color code
            $table->string('icon', 50)->nullable(); // Icon name for UI
            $table->json('metadata')->nullable(); // Additional configuration
            $table->enum('status', ['active', 'inactive'])->default('active');
            $table->integer('sort_order')->default(0);
            $table->timestamps();

            // Indexes
            $table->index(['status', 'sort_order']);
            $table->index('slug');
        });

        // Insert default property statuses
        DB::table('property_statuses')->insert([
            [
                'name' => 'Planning',
                'slug' => 'planning',
                'description' => 'Property is in planning phase',
                'color' => '#6B7280',
                'icon' => 'ClipboardList',
                'status' => 'active',
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Under Construction',
                'slug' => 'under_construction',
                'description' => 'Property is currently under construction',
                'color' => '#F59E0B',
                'icon' => 'Hammer',
                'status' => 'active',
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Completed',
                'slug' => 'completed',
                'description' => 'Property construction is completed',
                'color' => '#10B981',
                'icon' => 'CheckCircle',
                'status' => 'active',
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Sold',
                'slug' => 'sold',
                'description' => 'Property has been sold',
                'color' => '#EF4444',
                'icon' => 'DollarSign',
                'status' => 'active',
                'sort_order' => 4,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Rented',
                'slug' => 'rented',
                'description' => 'Property is currently rented',
                'color' => '#8B5CF6',
                'icon' => 'Key',
                'status' => 'active',
                'sort_order' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Under Maintenance',
                'slug' => 'maintenance',
                'description' => 'Property is under maintenance',
                'color' => '#F97316',
                'icon' => 'Wrench',
                'status' => 'active',
                'sort_order' => 6,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('property_statuses');
    }
};

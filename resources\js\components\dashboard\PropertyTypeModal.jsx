import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { X, Loader2 } from 'lucide-react';
import propertyTypeAPI from '../../services/propertyTypeAPI';
import { showAlert } from '../../utils/alertUtils';

const PropertyTypeModal = ({ isOpen, onClose, onSuccess, propertyType, mode }) => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_active: true,
    sort_order: 0
  });
  const [errors, setErrors] = useState({});

  // Initialize form data when modal opens or propertyType changes
  useEffect(() => {
    if (isOpen) {
      if (propertyType && (mode === 'edit' || mode === 'view')) {
        setFormData({
          name: propertyType.name || '',
          description: propertyType.description || '',
          is_active: propertyType.is_active ?? true,
          sort_order: propertyType.sort_order ?? 0
        });
      } else {
        setFormData({
          name: '',
          description: '',
          is_active: true,
          sort_order: 0
        });
      }
      setErrors({});
    }
  }, [isOpen, propertyType, mode]);

  // Handle input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (mode === 'view') return;

    setLoading(true);
    setErrors({});

    try {
      let response;
      if (mode === 'create') {
        response = await propertyTypeAPI.create(formData);
      } else {
        response = await propertyTypeAPI.update(propertyType.id, formData);
      }

      if (response.success) {
        showAlert('success', 'Success', response.message);
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving property type:', error);
      
      if (error.errors) {
        setErrors(error.errors);
      } else {
        showAlert('error', 'Error', error.message || `Failed to ${mode} property type`);
      }
    } finally {
      setLoading(false);
    }
  };

  const isReadOnly = mode === 'view';

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-md max-h-[90vh] flex flex-col overflow-hidden">
        {/* Modal Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
          <div>
            <h3 className="text-lg font-semibold">
              {mode === 'create' ? 'Add New Property Type' : 
               mode === 'edit' ? 'Edit Property Type' : 'View Property Type'}
            </h3>
            <p className="text-blue-100 text-sm">
              {mode === 'create' ? 'Create a new property type category' :
               mode === 'edit' ? 'Update property type information' : 'View property type details'}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="flex-1 overflow-y-auto p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Name */}
            <div className="space-y-2">
              <Label htmlFor="name">Property Type Name *</Label>
              <Input
                id="name"
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                placeholder="e.g., Residential, Commercial, Industrial"
                disabled={isReadOnly}
                className={errors.name ? 'border-red-500' : ''}
              />
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name[0]}</p>
              )}
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Brief description of this property type"
                rows={3}
                disabled={isReadOnly}
                className={errors.description ? 'border-red-500' : ''}
              />
              {errors.description && (
                <p className="text-sm text-red-600">{errors.description[0]}</p>
              )}
            </div>

            {/* Sort Order */}
            <div className="space-y-2">
              <Label htmlFor="sort_order">Sort Order</Label>
              <Input
                id="sort_order"
                type="number"
                min="0"
                value={formData.sort_order}
                onChange={(e) => handleInputChange('sort_order', parseInt(e.target.value) || 0)}
                placeholder="0"
                disabled={isReadOnly}
                className={errors.sort_order ? 'border-red-500' : ''}
              />
              <p className="text-xs text-gray-500">
                Lower numbers appear first in lists
              </p>
              {errors.sort_order && (
                <p className="text-sm text-red-600">{errors.sort_order[0]}</p>
              )}
            </div>

            {/* Active Status */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_active"
                checked={formData.is_active}
                onCheckedChange={(checked) => handleInputChange('is_active', checked)}
                disabled={isReadOnly}
              />
              <Label htmlFor="is_active" className="text-sm">
                Active (visible in property creation forms)
              </Label>
            </div>

            {mode === 'view' && (
              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <h4 className="font-medium text-gray-900">Additional Information</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Slug:</span>
                    <p className="font-mono">{propertyType?.slug}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Status:</span>
                    <p className={propertyType?.is_active ? 'text-green-600' : 'text-red-600'}>
                      {propertyType?.is_active ? 'Active' : 'Inactive'}
                    </p>
                  </div>
                  <div>
                    <span className="text-gray-500">Created:</span>
                    <p>{new Date(propertyType?.created_at).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Updated:</span>
                    <p>{new Date(propertyType?.updated_at).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>
            )}
          </form>
        </div>

        {/* Modal Footer */}
        <div className="flex justify-end space-x-2 p-6 border-t bg-gray-50">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onClose}
            disabled={loading}
          >
            {mode === 'view' ? 'Close' : 'Cancel'}
          </Button>
          {mode !== 'view' && (
            <Button 
              type="submit" 
              onClick={handleSubmit}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              {mode === 'create' ? 'Create Property Type' : 'Update Property Type'}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default PropertyTypeModal;

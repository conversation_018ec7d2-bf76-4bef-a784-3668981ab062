<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Contractor;

class Contractor<PERSON>eeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $contractors = [
            [
                'name' => 'ABC Construction Company',
                'phone' => '+**********',
                'trade_license' => 'TL-001-2024',
                'email' => '<EMAIL>',
                'address' => '123 Construction Ave, Building District, City 12345',
                'status' => 'active',
                'created_by' => 1,
                'updated_by' => 1,
            ],
            [
                'name' => 'Elite Building Solutions',
                'phone' => '+**********',
                'trade_license' => 'TL-002-2024',
                'email' => '<EMAIL>',
                'address' => '456 Builder Street, Industrial Zone, City 12346',
                'status' => 'active',
                'created_by' => 1,
                'updated_by' => 1,
            ],
            [
                'name' => 'Premium Contractors Ltd',
                'phone' => '+**********',
                'trade_license' => 'TL-003-2024',
                'email' => '<EMAIL>',
                'address' => '789 Developer Road, Commercial Area, City 12347',
                'status' => 'active',
                'created_by' => 1,
                'updated_by' => 1,
            ],
            [
                'name' => 'Modern Infrastructure Inc',
                'phone' => '+**********',
                'trade_license' => 'TL-004-2024',
                'email' => '<EMAIL>',
                'address' => '321 Infrastructure Blvd, Tech City, City 12348',
                'status' => 'active',
                'created_by' => 1,
                'updated_by' => 1,
            ],
            [
                'name' => 'Green Build Co',
                'phone' => '+**********',
                'trade_license' => 'TL-005-2024',
                'email' => '<EMAIL>',
                'address' => '654 Eco Street, Green District, City 12349',
                'status' => 'inactive',
                'created_by' => 1,
                'updated_by' => 1,
            ],
            [
                'name' => 'Reliable Construction Services',
                'phone' => '+**********',
                'trade_license' => 'TL-006-2024',
                'email' => '<EMAIL>',
                'address' => '987 Service Lane, Business Park, City 12350',
                'status' => 'active',
                'created_by' => 1,
                'updated_by' => 1,
            ]
        ];

        foreach ($contractors as $contractor) {
            Contractor::create($contractor);
        }
    }
}

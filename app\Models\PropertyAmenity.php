<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class PropertyAmenity extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'description',
        'icon',
        'category',
        'is_active',
        'sort_order',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime'
    ];

    protected $attributes = [
        'is_active' => true,
        'sort_order' => 0
    ];

    // Relationships
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }

    // Accessors
    public function getStatusTextAttribute()
    {
        return $this->is_active ? 'Active' : 'Inactive';
    }

    // Constants for categories
    const CATEGORIES = [
        'basic' => 'Basic Amenities',
        'recreational' => 'Recreational',
        'security' => 'Security',
        'parking' => 'Parking & Transportation',
        'utilities' => 'Utilities',
        'maintenance' => 'Maintenance',
        'other' => 'Other'
    ];

    // Common amenity icons
    const COMMON_ICONS = [
        'swimming-pool' => 'Swimming Pool',
        'gym' => 'Gymnasium',
        'parking' => 'Parking',
        'security' => 'Security',
        'garden' => 'Garden',
        'playground' => 'Playground',
        'elevator' => 'Elevator',
        'power-backup' => 'Power Backup',
        'water-supply' => 'Water Supply',
        'wifi' => 'WiFi',
        'clubhouse' => 'Clubhouse',
        'cctv' => 'CCTV',
        'intercom' => 'Intercom',
        'maintenance' => 'Maintenance'
    ];
}

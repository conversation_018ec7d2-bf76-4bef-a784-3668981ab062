<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\State;
use App\Models\Country;
use Illuminate\Support\Facades\DB;

class BangladeshStatesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get Bangladesh
        $bangladesh = Country::where('code', 'BD')->first();
        
        if (!$bangladesh) {
            $this->command->warn('Bangladesh not found. Please ensure the country exists first.');
            return;
        }

        // Bangladesh divisions (administrative divisions)
        $states = [
            [
                'name' => 'Dhaka',
                'code' => 'DH',
                'type' => 'division',
                'country_id' => $bangladesh->id,
                'capital' => 'Dhaka',
                'timezone' => 'Asia/Dhaka',
                'latitude' => 23.8103,
                'longitude' => 90.4125,
                'description' => 'Capital division of Bangladesh',
                'status' => 'active'
            ],
            [
                'name' => 'Chittagong',
                'code' => 'CG',
                'type' => 'division',
                'country_id' => $bangladesh->id,
                'capital' => 'Chittagong',
                'timezone' => 'Asia/Dhaka',
                'latitude' => 22.3569,
                'longitude' => 91.7830,
                'description' => 'Port city division',
                'status' => 'active'
            ],
            [
                'name' => 'Rajshahi',
                'code' => 'RJ',
                'type' => 'division',
                'country_id' => $bangladesh->id,
                'capital' => 'Rajshahi',
                'timezone' => 'Asia/Dhaka',
                'latitude' => 24.3745,
                'longitude' => 88.6042,
                'description' => 'Northern division',
                'status' => 'active'
            ],
            [
                'name' => 'Khulna',
                'code' => 'KH',
                'type' => 'division',
                'country_id' => $bangladesh->id,
                'capital' => 'Khulna',
                'timezone' => 'Asia/Dhaka',
                'latitude' => 22.8456,
                'longitude' => 89.5403,
                'description' => 'Southwestern division',
                'status' => 'active'
            ],
            [
                'name' => 'Barisal',
                'code' => 'BS',
                'type' => 'division',
                'country_id' => $bangladesh->id,
                'capital' => 'Barisal',
                'timezone' => 'Asia/Dhaka',
                'latitude' => 22.7010,
                'longitude' => 90.3535,
                'description' => 'Southern division',
                'status' => 'active'
            ],
            [
                'name' => 'Sylhet',
                'code' => 'SY',
                'type' => 'division',
                'country_id' => $bangladesh->id,
                'capital' => 'Sylhet',
                'timezone' => 'Asia/Dhaka',
                'latitude' => 24.8949,
                'longitude' => 91.8687,
                'description' => 'Northeastern division',
                'status' => 'active'
            ],
            [
                'name' => 'Rangpur',
                'code' => 'RP',
                'type' => 'division',
                'country_id' => $bangladesh->id,
                'capital' => 'Rangpur',
                'timezone' => 'Asia/Dhaka',
                'latitude' => 25.7439,
                'longitude' => 89.2752,
                'description' => 'Northern division',
                'status' => 'active'
            ],
            [
                'name' => 'Mymensingh',
                'code' => 'MY',
                'type' => 'division',
                'country_id' => $bangladesh->id,
                'capital' => 'Mymensingh',
                'timezone' => 'Asia/Dhaka',
                'latitude' => 24.7471,
                'longitude' => 90.4203,
                'description' => 'Central-northern division',
                'status' => 'active'
            ]
        ];

        foreach ($states as $stateData) {
            // Check if state already exists
            $existingState = State::where('name', $stateData['name'])
                ->where('country_id', $stateData['country_id'])
                ->first();

            if (!$existingState) {
                State::create($stateData);
                $this->command->info("Created state: {$stateData['name']}");
            } else {
                $this->command->info("State already exists: {$stateData['name']}");
            }
        }

        $this->command->info('Bangladesh states seeding completed!');
    }
}

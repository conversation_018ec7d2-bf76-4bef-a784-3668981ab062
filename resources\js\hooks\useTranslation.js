import { useState, useEffect } from 'react';
import { translator } from '../services/translationService.js';

/**
 * React hook for using translations
 */
export const useTranslation = () => {
  const [isLoaded, setIsLoaded] = useState(translator.isReady());
  const [currentLanguage, setCurrentLanguage] = useState(translator.getCurrentLanguage());

  useEffect(() => {
    // Initialize translations if not already loaded
    if (!translator.isReady()) {
      translator.init().then(() => {
        setIsLoaded(true);
        setCurrentLanguage(translator.getCurrentLanguage());
      });
    }

    // Listen for language changes
    const handleLanguageChange = (event) => {
      setCurrentLanguage(event.detail.language);
    };

    const handleTranslationsLoaded = (event) => {
      setIsLoaded(true);
      setCurrentLanguage(event.detail.language);
    };

    window.addEventListener('languageChanged', handleLanguageChange);
    window.addEventListener('translationsLoaded', handleTranslationsLoaded);

    return () => {
      window.removeEventListener('languageChanged', handleLanguageChange);
      window.removeEventListener('translationsLoaded', handleTranslationsLoaded);
    };
  }, []);

  /**
   * Translation function
   */
  const t = (key, params = {}) => {
    return translator.t(key, params);
  };

  /**
   * Change language
   */
  const changeLanguage = async (languageCode) => {
    await translator.changeLanguage(languageCode);
    setCurrentLanguage(languageCode);
  };

  /**
   * Format date
   */
  const formatDate = (date, options = {}) => {
    return translator.formatDate(date, options);
  };

  /**
   * Format number
   */
  const formatNumber = (number, options = {}) => {
    return translator.formatNumber(number, options);
  };

  /**
   * Format currency
   */
  const formatCurrency = (amount, currency = 'USD') => {
    return translator.formatCurrency(amount, currency);
  };

  /**
   * Get available languages
   */
  const getAvailableLanguages = async () => {
    return await translator.getAvailableLanguages();
  };

  return {
    t,
    isLoaded,
    currentLanguage,
    changeLanguage,
    formatDate,
    formatNumber,
    formatCurrency,
    getAvailableLanguages
  };
};

export default useTranslation;

<?php

namespace App\Http\Controllers;

use App\Models\VendorType;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class VendorTypeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = VendorType::with(['creator', 'updater']);

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where('name', 'like', "%{$searchTerm}%");
        }

        // Status filter
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        
        $allowedSortFields = ['name', 'status', 'created_at'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Pagination
        $perPage = $request->get('per_page', 10);
        $vendorTypes = $query->paginate($perPage);

        return response()->json([
            'status' => 'success',
            'data' => $vendorTypes
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:vendor_types,name',
            'status' => 'sometimes|in:active,inactive'
        ]);

        DB::beginTransaction();

        try {
            // Set the creator
            $validated['created_by'] = auth()->id();

            $vendorType = VendorType::create($validated);
            $vendorType->load(['creator', 'updater']);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Vendor type created successfully',
                'data' => $vendorType
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create vendor type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(VendorType $vendorType)
    {
        $vendorType->load(['creator', 'updater']);
        
        return response()->json([
            'status' => 'success',
            'data' => $vendorType
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, VendorType $vendorType)
    {
        $validated = $request->validate([
            'name' => [
                'required',
                'string',
                'max:255',
                Rule::unique('vendor_types')->ignore($vendorType->id)
            ],
            'status' => 'sometimes|in:active,inactive'
        ]);

        DB::beginTransaction();

        try {
            // Set the updater
            $validated['updated_by'] = auth()->id();

            $vendorType->update($validated);
            $vendorType->load(['creator', 'updater']);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Vendor type updated successfully',
                'data' => $vendorType
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update vendor type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(VendorType $vendorType)
    {
        DB::beginTransaction();

        try {
            $vendorType->delete();

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Vendor type deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete vendor type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get vendor types for dropdown
     */
    public function dropdown()
    {
        $vendorTypes = VendorType::where('status', 'active')
                                ->select('id', 'name')
                                ->orderBy('name')
                                ->get();

        return response()->json([
            'status' => 'success',
            'data' => $vendorTypes
        ]);
    }

    /**
     * Get vendor type statistics
     */
    public function statistics()
    {
        $stats = [
            'total_vendor_types' => VendorType::count(),
            'active_vendor_types' => VendorType::where('status', 'active')->count(),
            'inactive_vendor_types' => VendorType::where('status', 'inactive')->count(),
        ];

        return response()->json([
            'status' => 'success',
            'data' => $stats
        ]);
    }

    /**
     * Bulk status update
     */
    public function bulkStatusUpdate(Request $request)
    {
        $validated = $request->validate([
            'vendor_type_ids' => 'required|array',
            'vendor_type_ids.*' => 'exists:vendor_types,id',
            'status' => 'required|in:active,inactive'
        ]);

        $updatedCount = VendorType::whereIn('id', $validated['vendor_type_ids'])
                                 ->update([
                                     'status' => $validated['status'],
                                     'updated_by' => auth()->id()
                                 ]);

        return response()->json([
            'status' => 'success',
            'message' => "Successfully updated {$updatedCount} vendor types",
            'data' => ['updated_count' => $updatedCount]
        ]);
    }
}

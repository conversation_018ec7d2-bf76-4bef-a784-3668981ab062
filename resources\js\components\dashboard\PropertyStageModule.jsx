import React, { useEffect, useState } from 'react';
import axios from 'axios';

const PropertyStageModule = () => {
  const [stages, setStages] = useState([]);
  const [form, setForm] = useState({ name: '', description: '' });
  const [editingId, setEditingId] = useState(null);

  // Fetch all property stages
  useEffect(() => {
    fetchStages();
  }, []);

  const fetchStages = async () => {
    const res = await axios.get('/api/property-stages');
    setStages(res.data);
  };

  const handleChange = (e) => {
    setForm({ ...form, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (editingId) {
      await axios.put(`/api/property-stages/${editingId}`, form);
    } else {
      await axios.post('/api/property-stages', form);
    }
    setForm({ name: '', description: '' });
    setEditingId(null);
    fetchStages();
  };

  const handleEdit = (stage) => {
    setForm({ name: stage.name, description: stage.description });
    setEditingId(stage.id);
  };

  const handleDelete = async (id) => {
    await axios.delete(`/api/property-stages/${id}`);
    fetchStages();
  };

  return (
    <div>
      <h2>Property Stages</h2>
      <form onSubmit={handleSubmit} style={{ marginBottom: 20 }}>
        <input
          name="name"
          value={form.name}
          onChange={handleChange}
          placeholder="Name"
          required
        />
        <input
          name="description"
          value={form.description}
          onChange={handleChange}
          placeholder="Description"
        />
        <button type="submit">{editingId ? 'Update' : 'Add'} Stage</button>
        {editingId && <button type="button" onClick={() => { setForm({ name: '', description: '' }); setEditingId(null); }}>Cancel</button>}
      </form>
      <table border="1" cellPadding="8">
        <thead>
          <tr>
            <th>Name</th>
            <th>Description</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {stages.map(stage => (
            <tr key={stage.id}>
              <td>{stage.name}</td>
              <td>{stage.description}</td>
              <td>
                <button onClick={() => handleEdit(stage)}>Edit</button>
                <button onClick={() => handleDelete(stage.id)}>Delete</button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default PropertyStageModule;

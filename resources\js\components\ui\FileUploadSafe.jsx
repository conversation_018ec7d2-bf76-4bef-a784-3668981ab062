import React, { useState, useRef, useEffect } from 'react';
import { Upload, X, Eye, FileText, Image, File } from 'lucide-react';

// Safe file type checking utilities
const safeFileChecks = {
  isFile: (obj) => {
    try {
      return obj && typeof obj === 'object' && obj.constructor && obj.constructor.name === 'File';
    } catch {
      return false;
    }
  },
  
  isImageFile: (file) => {
    try {
      if (safeFileChecks.isFile(file)) {
        return file.type && file.type.startsWith('image/');
      }
      if (typeof file === 'string') {
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp', '.svg'];
        return imageExtensions.some(ext => file.toLowerCase().endsWith(ext));
      }
      return false;
    } catch {
      return false;
    }
  },
  
  getFileType: (file) => {
    try {
      if (safeFileChecks.isFile(file)) {
        return file.type || 'unknown';
      }
      if (typeof file === 'string') {
        if (file.toLowerCase().includes('.pdf')) return 'application/pdf';
        if (file.toLowerCase().includes('.doc')) return 'application/document';
        if (file.toLowerCase().includes('.jpg') || file.toLowerCase().includes('.jpeg')) return 'image/jpeg';
        if (file.toLowerCase().includes('.png')) return 'image/png';
      }
      return 'unknown';
    } catch {
      return 'unknown';
    }
  }
};

const FileUpload = ({ 
  value, 
  onChange, 
  label = "File", 
  accept = "*/*",
  maxSize = 10 * 1024 * 1024, // 10MB default
  placeholder = "Upload a file",
  className = "",
  disabled = false,
  allowPreview = true
}) => {
  const [preview, setPreview] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef(null);

  // Initialize state when value changes (for editing existing records)
  useEffect(() => {
    console.log('📁 FileUpload: Value changed:', value);
    try {
      if (value && typeof value === 'string') {
        // For existing files (string URLs), don't set selectedFile
        setSelectedFile(null);
        setPreview(null);
      } else if (safeFileChecks.isFile(value)) {
        // For newly selected files
        setSelectedFile(value);
        if (safeFileChecks.isImageFile(value)) {
          const reader = new FileReader();
          reader.onload = (e) => setPreview(e.target.result);
          reader.readAsDataURL(value);
        }
      } else {
        // No file
        setSelectedFile(null);
        setPreview(null);
      }
    } catch (error) {
      console.error('FileUpload initialization error:', error);
      setSelectedFile(null);
      setPreview(null);
    }
  }, [value]);

  // Helper function to get full file URL
  const getFileUrl = (filePath) => {
    if (!filePath) return null;
    
    try {
      // If it's already a full URL, return as is
      if (filePath.startsWith('http://') || filePath.startsWith('https://')) {
        return filePath;
      }
      
      // If it starts with /landowners, use the base URL
      if (filePath.startsWith('/landowners/')) {
        return `${window.location.protocol}//${window.location.host}${filePath}`;
      }
      
      // If it starts with /project-docs, use the base URL
      if (filePath.startsWith('/project-docs/')) {
        return `${window.location.protocol}//${window.location.host}${filePath}`;
      }
      
      // If it starts with /storage, use the base URL
      if (filePath.startsWith('/storage/')) {
        return `${window.location.protocol}//${window.location.host}${filePath}`;
      }
      
      // For project documents, assume they are in projects directory
      if (filePath.includes('project-docs/')) {
        return `${window.location.protocol}//${window.location.host}/storage/${filePath}`;
      }
      
      // For land owner documents, assume they are in landowners/documents directory
      return `${window.location.protocol}//${window.location.host}/landowners/documents/${filePath}`;
    } catch (error) {
      console.error('Error generating file URL:', error);
      return null;
    }
  };

  // Get file icon based on type
  const getFileIcon = (file) => {
    try {
      if (safeFileChecks.isImageFile(file)) {
        return <Image className="h-8 w-8 text-blue-500" />;
      }
      
      const fileType = safeFileChecks.getFileType(file);
      
      if (fileType.includes('pdf')) {
        return <FileText className="h-8 w-8 text-red-500" />;
      }
      if (fileType.includes('word') || fileType.includes('document')) {
        return <FileText className="h-8 w-8 text-blue-600" />;
      }
      
      return <File className="h-8 w-8 text-gray-500" />;
    } catch (error) {
      console.error('Error getting file icon:', error);
      return <File className="h-8 w-8 text-gray-500" />;
    }
  };

  const handleFileSelect = (file) => {
    if (!file) return;

    try {
      console.log('📁 FileUpload: File selected', {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      });

      // Validate file size
      if (file.size > maxSize) {
        console.error('❌ File too large:', file.size, 'bytes, max:', maxSize);
        alert(`File size must be less than ${Math.round(maxSize / (1024 * 1024))}MB`);
        return;
      }

      console.log('✅ File validation passed');

      // Create preview for images only
      if (safeFileChecks.isImageFile(file)) {
        const reader = new FileReader();
        reader.onload = (e) => {
          console.log('📷 Preview created successfully');
          setPreview(e.target.result);
        };
        reader.onerror = (e) => {
          console.error('❌ Error creating preview:', e);
        };
        reader.readAsDataURL(file);
      } else {
        setPreview(null); // No preview for non-image files
      }

      // Store the file for form submission
      setSelectedFile(file);
      console.log('📤 FileUpload: Calling onChange with file object:', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        isFile: safeFileChecks.isFile(file)
      });
      onChange(file); // Pass the file object to parent
    } catch (error) {
      console.error('Error selecting file:', error);
      alert('Error selecting file. Please try again.');
    }
  };

  const handleFileChange = (event) => {
    try {
      console.log('📁 FileUpload: handleFileChange triggered');
      const file = event.target.files[0];
      if (file) {
        console.log('📁 FileUpload: File selected from input:', file.name);
        handleFileSelect(file);
      }
      // Clear the input value to allow selecting the same file again if needed
      event.target.value = '';
    } catch (error) {
      console.error('Error handling file change:', error);
    }
  };

  const handleButtonClick = () => {
    try {
      if (disabled) return;
      console.log('📁 FileUpload: Button clicked, opening file picker');
      fileInputRef.current?.click();
    } catch (error) {
      console.error('Error opening file picker:', error);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (!disabled) {
      setDragOver(true);
    }
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
    
    try {
      if (disabled) return;

      console.log('📁 FileUpload: File dropped');
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        console.log('📁 FileUpload: Processing dropped file:', files[0].name);
        handleFileSelect(files[0]);
      }
    } catch (error) {
      console.error('Error handling file drop:', error);
    }
  };

  const handleRemove = () => {
    try {
      console.log('📁 FileUpload: Remove file called');
      setPreview(null);
      setSelectedFile(null);
      onChange(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error) {
      console.error('Error removing file:', error);
    }
  };

  const handleViewFile = () => {
    try {
      if (selectedFile && safeFileChecks.isImageFile(selectedFile)) {
        // For image files, show preview
        if (preview) {
          window.open(preview, '_blank');
        }
      } else if (typeof value === 'string') {
        // For existing files, open the URL
        const fileUrl = getFileUrl(value);
        if (fileUrl) {
          window.open(fileUrl, '_blank');
        }
      }
    } catch (error) {
      console.error('Error viewing file:', error);
    }
  };

  // Determine what to display
  const hasFile = selectedFile || (value && typeof value === 'string');
  const displayFile = selectedFile || value;
  
  try {
    console.log('📁 FileUpload: Render state:', {
      hasFile,
      selectedFile: selectedFile?.name,
      value: typeof value === 'string' ? value : safeFileChecks.isFile(value) ? value.name : value,
      displayFile: safeFileChecks.isFile(displayFile) ? displayFile.name : displayFile
    });
  } catch (error) {
    console.error('Error logging render state:', error);
  }
  
  return (
    <div className={`space-y-2 ${className}`}>
      {label && <label className="block text-sm font-medium text-gray-700">{label}</label>}
      
      {!hasFile ? (
        // Upload area
        <div
          className={`
            relative border-2 border-dashed rounded-lg p-6 text-center transition-colors
            ${dragOver ? 'border-primary bg-primary/5' : 'border-gray-300'}
            ${disabled ? 'opacity-50' : 'hover:border-gray-400'}
          `}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            ref={fileInputRef}
            type="file"
            onChange={handleFileChange}
            accept={accept}
            className="hidden"
            disabled={disabled}
          />
          
          <Upload className="mx-auto h-12 w-12 text-gray-400" />
          <p className="mt-2 text-sm text-gray-600">{placeholder}</p>
          <p className="text-xs text-gray-500 mt-1">
            Max size: {Math.round(maxSize / (1024 * 1024))}MB
          </p>
          
          <button
            type="button"
            className="mt-4 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            onClick={handleButtonClick}
            disabled={disabled}
          >
            <Upload className="mr-2 h-4 w-4" />
            Choose File
          </button>
        </div>
      ) : (
        // File preview/info
        <div className="border rounded-lg p-4 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getFileIcon(displayFile)}
              <div>
                <p className="text-sm font-medium text-gray-900">
                  {selectedFile ? selectedFile.name : (typeof value === 'string' ? value.split('/').pop() : 'File')}
                </p>
                {selectedFile && (
                  <p className="text-xs text-gray-500">
                    {Math.round(selectedFile.size / 1024)} KB • {selectedFile.type || 'Unknown type'}
                  </p>
                )}
              </div>
            </div>
            
            <div className="flex space-x-2">
              {allowPreview && (safeFileChecks.isImageFile(displayFile) || typeof value === 'string') && (
                <button
                  type="button"
                  className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  onClick={handleViewFile}
                  disabled={disabled}
                >
                  <Eye className="h-4 w-4" />
                </button>
              )}
              <button
                type="button"
                className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                onClick={handleRemove}
                disabled={disabled}
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          </div>
          
          {/* Image preview */}
          {preview && safeFileChecks.isImageFile(displayFile) && (
            <div className="mt-3">
              <img
                src={preview}
                alt="Preview"
                className="max-w-full h-32 object-cover rounded border"
              />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default FileUpload;

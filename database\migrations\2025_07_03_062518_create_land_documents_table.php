<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('land_documents', function (Blueprint $table) {
            $table->id();
            $table->foreignId('land_acquisition_id')->constrained('land_acquisitions')->onDelete('cascade');
            $table->string('document_name');
            $table->string('document_file_path');
            $table->string('original_filename')->nullable();
            $table->string('file_type')->nullable();
            $table->integer('file_size')->nullable();
            $table->text('description')->nullable();
            $table->foreignId('uploaded_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
            
            $table->index(['land_acquisition_id']);
            $table->index(['document_name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('land_documents');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('unit_details', function (Blueprint $table) {
            $table->integer('floor_number')->nullable()->after('status');
            $table->string('unit_number')->nullable()->after('floor_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('unit_details', function (Blueprint $table) {
            $table->dropColumn(['floor_number', 'unit_number']);
        });
    }
};

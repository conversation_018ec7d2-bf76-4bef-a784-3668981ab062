<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('land_owner_audits', function (Blueprint $table) {
            // Add missing columns that the LandOwnerAudit model expects
            if (!Schema::hasColumn('land_owner_audits', 'event_type')) {
                $table->string('event_type')->nullable()->after('action');
            }
            if (!Schema::hasColumn('land_owner_audits', 'user_email')) {
                $table->string('user_email')->nullable()->after('user_name');
            }
            if (!Schema::hasColumn('land_owner_audits', 'changed_fields')) {
                $table->json('changed_fields')->nullable()->after('new_values');
            }
            if (!Schema::hasColumn('land_owner_audits', 'source')) {
                $table->string('source')->nullable()->after('user_agent');
            }
            if (!Schema::hasColumn('land_owner_audits', 'description')) {
                $table->text('description')->nullable()->after('source');
            }
            if (!Schema::hasColumn('land_owner_audits', 'metadata')) {
                $table->json('metadata')->nullable()->after('description');
            }
            
            // Remove the 'environment' column if it exists (not used by the model)
            if (Schema::hasColumn('land_owner_audits', 'environment')) {
                $table->dropColumn('environment');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('land_owner_audits', function (Blueprint $table) {
            // Remove the columns we added
            $table->dropColumn([
                'event_type',
                'user_email',
                'changed_fields',
                'source',
                'description',
                'metadata'
            ]);
            
            // Add back environment column if needed
            $table->string('environment')->nullable();
        });
    }
};

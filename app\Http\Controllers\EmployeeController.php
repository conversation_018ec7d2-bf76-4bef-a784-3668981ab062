<?php

namespace App\Http\Controllers;

use App\Models\Employee;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class EmployeeController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $query = Employee::with(['creator', 'updater']);

        // Search functionality
        if ($request->has('search') && !empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('name', 'like', "%{$searchTerm}%")
                  ->orWhere('designation', 'like', "%{$searchTerm}%")
                  ->orWhere('email', 'like', "%{$searchTerm}%")
                  ->orWhere('phone', 'like', "%{$searchTerm}%");
            });
        }

        // Status filter
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Designation filter
        if ($request->has('designation') && !empty($request->designation)) {
            $query->where('designation', 'like', "%{$request->designation}%");
        }

        // Date range filter
        if ($request->has('date_from') && !empty($request->date_from)) {
            $query->whereDate('date_of_joining', '>=', $request->date_from);
        }

        if ($request->has('date_to') && !empty($request->date_to)) {
            $query->whereDate('date_of_joining', '<=', $request->date_to);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        
        $allowedSortFields = ['name', 'designation', 'email', 'phone', 'status', 'date_of_joining', 'created_at'];
        if (in_array($sortBy, $allowedSortFields)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        // Pagination
        $perPage = $request->get('per_page', 10);
        $employees = $query->paginate($perPage);

        return response()->json([
            'status' => 'success',
            'data' => $employees
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'designation' => 'required|string|max:255',
            'phone' => 'required|string|max:20|unique:employees,phone',
            'email' => 'required|email|max:255|unique:employees,email',
            'status' => 'sometimes|in:active,inactive',
            'date_of_joining' => 'nullable|date',
            'address' => 'nullable|string|max:500',
            'salary' => 'nullable|numeric|min:0',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        DB::beginTransaction();

        try {
            // Handle photo upload
            if ($request->hasFile('photo')) {
                $photo = $request->file('photo');
                $filename = time() . '_' . Str::random(10) . '.' . $photo->getClientOriginalExtension();
                
                // Store the file in public/storage/employees directory
                $photo->move(public_path('storage/employees'), $filename);
                $validated['photo'] = $filename;
            }

            // Set the creator
            $validated['created_by'] = auth()->id();

            $employee = Employee::create($validated);
            $employee->load(['creator', 'updater']);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Employee created successfully',
                'data' => $employee
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            
            // Delete uploaded photo if transaction fails
            if (isset($validated['photo']) && file_exists(public_path('storage/employees/' . $validated['photo']))) {
                unlink(public_path('storage/employees/' . $validated['photo']));
            }

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create employee: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Employee $employee)
    {
        $employee->load(['creator', 'updater']);
        
        return response()->json([
            'status' => 'success',
            'data' => $employee
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Employee $employee)
    {
        // Log the incoming request data for debugging
        \Log::info('Employee update request data', [
            'employee_id' => $employee->id,
            'request_all' => $request->all(),
            'request_method' => $request->method(),
            'has_files' => $request->hasFile('photo'),
            'content_type' => $request->header('Content-Type')
        ]);

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'designation' => 'required|string|max:255',
            'phone' => [
                'required',
                'string',
                'max:20',
                Rule::unique('employees')->ignore($employee->id)
            ],
            'email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('employees')->ignore($employee->id)
            ],
            'status' => 'sometimes|in:active,inactive',
            'date_of_joining' => 'nullable|date',
            'address' => 'nullable|string|max:500',
            'salary' => 'nullable|numeric|min:0',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        DB::beginTransaction();

        try {
            $oldPhoto = $employee->photo;

            // Handle photo upload
            if ($request->hasFile('photo')) {
                $photo = $request->file('photo');
                $filename = time() . '_' . Str::random(10) . '.' . $photo->getClientOriginalExtension();
                
                // Store the new file
                $photo->move(public_path('storage/employees'), $filename);
                $validated['photo'] = $filename;

                // Delete old photo if exists
                if ($oldPhoto && file_exists(public_path('storage/employees/' . $oldPhoto))) {
                    unlink(public_path('storage/employees/' . $oldPhoto));
                }
            }

            // Set the updater
            $validated['updated_by'] = auth()->id();

            $employee->update($validated);
            $employee->load(['creator', 'updater']);

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Employee updated successfully',
                'data' => $employee
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            // Delete uploaded photo if transaction fails
            if (isset($validated['photo']) && file_exists(public_path('storage/employees/' . $validated['photo']))) {
                unlink(public_path('storage/employees/' . $validated['photo']));
            }

            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update employee: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Employee $employee)
    {
        DB::beginTransaction();

        try {
            $photoPath = $employee->photo;

            $employee->delete();

            // Delete photo file if exists
            if ($photoPath && file_exists(public_path('storage/employees/' . $photoPath))) {
                unlink(public_path('storage/employees/' . $photoPath));
            }

            DB::commit();

            return response()->json([
                'status' => 'success',
                'message' => 'Employee deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete employee: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get employees for dropdown
     */
    public function dropdown()
    {
        $employees = Employee::where('status', 'active')
                           ->select('id', 'name', 'designation')
                           ->orderBy('name')
                           ->get();

        return response()->json([
            'status' => 'success',
            'data' => $employees
        ]);
    }

    /**
     * Get employee statistics
     */
    public function statistics()
    {
        $stats = [
            'total_employees' => Employee::count(),
            'active_employees' => Employee::where('status', 'active')->count(),
            'inactive_employees' => Employee::where('status', 'inactive')->count(),
            'employees_by_designation' => Employee::select('designation')
                                                ->selectRaw('count(*) as count')
                                                ->groupBy('designation')
                                                ->orderBy('count', 'desc')
                                                ->get(),
            'recent_joinings' => Employee::whereDate('date_of_joining', '>=', now()->subDays(30))
                                       ->count(),
            'average_salary' => Employee::whereNotNull('salary')->avg('salary')
        ];

        return response()->json([
            'status' => 'success',
            'data' => $stats
        ]);
    }

    /**
     * Bulk status update
     */
    public function bulkStatusUpdate(Request $request)
    {
        $validated = $request->validate([
            'employee_ids' => 'required|array',
            'employee_ids.*' => 'exists:employees,id',
            'status' => 'required|in:active,inactive'
        ]);

        $updatedCount = Employee::whereIn('id', $validated['employee_ids'])
                              ->update([
                                  'status' => $validated['status'],
                                  'updated_by' => auth()->id()
                              ]);

        return response()->json([
            'status' => 'success',
            'message' => "Successfully updated {$updatedCount} employees",
            'data' => ['updated_count' => $updatedCount]
        ]);
    }
}

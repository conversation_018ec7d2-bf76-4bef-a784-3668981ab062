import React from 'react';
import { X } from 'lucide-react';

const StandardModal = ({ 
  isOpen,
  showModal, 
  onClose,
  closeModal, 
  modalMode = 'create', 
  title, 
  icon: Icon, 
  children,
  size = 'lg',
  maxWidth = 'max-w-lg',
  maxHeight = 'max-h-[90vh]'
}) => {
  // Support both prop naming conventions
  const isModalOpen = isOpen ?? showModal;
  const handleClose = onClose ?? closeModal;
  
  // Set size based on size prop
  const sizeClasses = {
    'sm': 'max-w-sm',
    'md': 'max-w-md', 
    'lg': 'max-w-lg',
    'xl': 'max-w-4xl',
    '2xl': 'max-w-6xl'
  };
  
  const modalMaxWidth = sizeClasses[size] || maxWidth;
  
  if (!isModalOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50 animate-fadeIn">
      <div className={`bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full ${modalMaxWidth} ${maxHeight} overflow-hidden transform transition-all duration-300 animate-slideIn`}>
        {/* Modal Header */}
        <div className="relative bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                {Icon && <Icon className="w-5 h-5 text-white" />}
              </div>
              <h3 className="text-lg font-semibold text-white">
                {title}
              </h3>
            </div>
            <button
              onClick={handleClose}
              className="p-2 hover:bg-white/20 rounded-lg transition-colors duration-200 group"
            >
              <X className="w-5 h-5 text-white group-hover:rotate-90 transition-transform duration-200" />
            </button>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-300 to-blue-400"></div>
        </div>

        {/* Modal Body */}
        <div className="p-6 max-h-[calc(90vh-120px)] overflow-y-auto">
          {children}
        </div>
      </div>
    </div>
  );
};

export default StandardModal;

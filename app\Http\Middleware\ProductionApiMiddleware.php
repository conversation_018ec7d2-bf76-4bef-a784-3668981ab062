<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ProductionApiMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        // Ensure JSON responses for API requests
        if ($request->is('api/*')) {
            $request->headers->set('Accept', 'application/json');
        }

        try {
            $response = $next($request);

            // Ensure JSON content type for API responses
            if ($request->is('api/*') && !$response instanceof JsonResponse) {
                if (is_string($response->getContent())) {
                    $response->headers->set('Content-Type', 'application/json');
                }
            }

            // Add security headers for production
            if (app()->environment('production')) {
                $response->headers->set('X-Content-Type-Options', 'nosniff');
                $response->headers->set('X-Frame-Options', 'DENY');
                $response->headers->set('X-XSS-Protection', '1; mode=block');
                $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');
            }

            return $response;

        } catch (\Exception $e) {
            // Handle exceptions gracefully for API requests
            if ($request->is('api/*')) {
                $status = method_exists($e, 'getStatusCode') ? $e->getStatusCode() : 500;
                
                return response()->json([
                    'success' => false,
                    'message' => app()->environment('production') ? 'An error occurred' : $e->getMessage(),
                    'error_code' => $status,
                    'timestamp' => now()->toISOString(),
                ], $status);
            }

            throw $e;
        }
    }
}

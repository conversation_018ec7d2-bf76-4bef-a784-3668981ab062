import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  IdCard, 
  FileText, 
  Upload, 
  X, 
  Eye,
  Camera,
  CreditCard
} from 'lucide-react';
import SimpleImageUpload from '@/components/ui/SimpleImageUpload';

const DocumentUpload = ({ 
  documentType, 
  onDocumentTypeChange,
  nidFront, 
  onNidFrontChange,
  nidBack, 
  onNidBackChange,
  passportPhoto, 
  onPassportPhotoChange,
  disabled = false
}) => {
  const [activeTab, setActiveTab] = useState(documentType || 'nid');

  const handleTabChange = (tabType) => {
    setActiveTab(tabType);
    onDocumentTypeChange(tabType);
    
    // Clear other document fields when switching tabs
    if (tabType === 'nid') {
      onPassportPhotoChange('');
    } else if (tabType === 'passport') {
      onNidFrontChange('');
      onNidBackChange('');
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <Label className="text-base font-medium">Identity Documents</Label>
        <p className="text-sm text-gray-500 mt-1">
          Upload NID or Passport documents for verification
        </p>
      </div>

      {/* Tab Selector */}
      <div className="flex space-x-2">
        <Button
          type="button"
          variant={activeTab === 'nid' ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleTabChange('nid')}
          disabled={disabled}
          className="flex items-center gap-2"
        >
          <IdCard className="h-4 w-4" />
          NID Card
        </Button>
        <Button
          type="button"
          variant={activeTab === 'passport' ? 'default' : 'outline'}
          size="sm"
          onClick={() => handleTabChange('passport')}
          disabled={disabled}
          className="flex items-center gap-2"
        >
          <CreditCard className="h-4 w-4" />
          Passport
        </Button>
      </div>

      {/* Document Upload Areas */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            {activeTab === 'nid' ? (
              <>
                <IdCard className="h-5 w-5" />
                National ID Card
              </>
            ) : (
              <>
                <CreditCard className="h-5 w-5" />
                Passport
              </>
            )}
          </CardTitle>
          <CardDescription>
            {activeTab === 'nid' 
              ? 'Upload clear photos of both sides of the NID card'
              : 'Upload a clear photo of the passport information page'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {activeTab === 'nid' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <SimpleImageUpload
                  value={nidFront}
                  onChange={onNidFrontChange}
                  label="NID Front Side"
                  placeholder="Upload NID front side"
                  disabled={disabled}
                />
              </div>
              <div className="space-y-2">
                <SimpleImageUpload
                  value={nidBack}
                  onChange={onNidBackChange}
                  label="NID Back Side"
                  placeholder="Upload NID back side"
                  disabled={disabled}
                />
              </div>
            </div>
          ) : (
            <div className="max-w-md">
              <SimpleImageUpload
                value={passportPhoto}
                onChange={onPassportPhotoChange}
                label="Passport Photo Page"
                placeholder="Upload passport information page"
                disabled={disabled}
              />
            </div>
          )}

          {/* Document Status */}
          <div className="flex items-center gap-2 pt-2">
            <Label className="text-sm">Document Status:</Label>
            {activeTab === 'nid' ? (
              <div className="flex gap-2">
                <Badge variant={nidFront ? 'default' : 'secondary'}>
                  Front: {nidFront ? 'Uploaded' : 'Not uploaded'}
                </Badge>
                <Badge variant={nidBack ? 'default' : 'secondary'}>
                  Back: {nidBack ? 'Uploaded' : 'Not uploaded'}
                </Badge>
              </div>
            ) : (
              <Badge variant={passportPhoto ? 'default' : 'secondary'}>
                {passportPhoto ? 'Uploaded' : 'Not uploaded'}
              </Badge>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DocumentUpload;

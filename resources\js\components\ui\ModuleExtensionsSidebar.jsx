import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './card';
import { Badge } from './badge';
import { Button } from './button';
import { 
  Package, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  RefreshCw, 
  Settings, 
  Info, 
  Code,
  Server,
  Database,
  Folder,
  Eye,
  EyeOff,
  Download,
  Upload
} from 'lucide-react';
import { moduleAvailabilityService } from '../../services/moduleAvailabilityService';
import { showAlertMethods as showAlert } from '../../utils/sweetAlert';
import { useTranslation } from '../../hooks/useTranslation';

const ModuleExtensionsSidebar = ({ isOpen, onClose }) => {
  const { t } = useTranslation();
  const [modules, setModules] = useState([]);
  const [loading, setLoading] = useState(false);
  const [showDetails, setShowDetails] = useState({});
  const [filter, setFilter] = useState('all'); // 'all', 'available', 'unavailable', 'errors'

  // Load modules immediately when component mounts (always available)
  useEffect(() => {
    loadModules();
  }, []);

  const loadModules = async () => {
    setLoading(true);
    try {
      const response = await moduleAvailabilityService.getAvailableModules();
      if (response && response.success) {
        // Transform the modules data to include additional metadata
        const transformedModules = (response.data.modules || []).map(module => ({
          ...module,
          // Add computed properties for easier filtering
          hasIssues: module.errors && module.errors.length > 0,
          isFullyFunctional: module.available && module.controller_exists && module.model_exists,
          statusText: moduleAvailabilityService.getModuleStatusText(module)
        }));
        
        setModules(transformedModules);
    
      } else {
       
        // Fallback to predefined modules if API fails
        setModules(getDefaultModules());
      }
    } catch (error) {
      console.error('Error loading modules:', error);
      // Always show default modules even if API fails - menu is always available
      setModules(getDefaultModules());
    } finally {
      setLoading(false);
    }
  };

  // Predefined modules that are always shown regardless of API status
  // Only include modules that have corresponding controllers and models
  const getDefaultModules = () => {
    return [
      {
        name: 'Land Owners',
        key: 'land-owners',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: true,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'Land Acquisition',
        key: 'land-acquisition',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: false,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'Projects',
        key: 'projects',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: false,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'Employees',
        key: 'employees',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: false,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'Contractors',
        key: 'contractors',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: false,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'Vendors',
        key: 'vendors',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: false,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'Vendor Types',
        key: 'vendor-types',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: false,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'Property Amenities',
        key: 'property-amenities',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: false,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'Roles',
        key: 'roles',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: false,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'Countries',
        key: 'countries',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: false,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'States',
        key: 'states',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: false,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'Cities',
        key: 'cities',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: false,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'Currencies',
        key: 'currencies',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: false,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'Languages',
        key: 'languages',
        available: true,
        controller_exists: true,
        model_exists: true,
        is_laravel_module: false,
        errors: [],
        statusText: 'Always Available'
      },
      {
        name: 'Settings',
        key: 'settings',
        available: true,
        controller_exists: false,
        model_exists: false,
        is_laravel_module: false,
        errors: ['No dedicated controller - uses configuration files'],
        statusText: 'Configuration Based'
      }
    ];
  };

  const toggleDetails = (moduleKey) => {
    setShowDetails(prev => ({
      ...prev,
      [moduleKey]: !prev[moduleKey]
    }));
  };

  const getStatusIcon = (module) => {
    // Always show as available since this menu is always accessible
    if (module.available || module.statusText === 'Always Available') {
      return <CheckCircle className="h-5 w-5 text-green-500" />;
    } else if (module.errors && module.errors.length > 0) {
      return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
    } else {
      return <XCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const getStatusBadge = (module) => {
    // Always show as available since this menu is always accessible
    if (module.available || module.statusText === 'Always Available') {
      return <Badge variant="default" className="bg-green-100 text-green-800">Available</Badge>;
    } else if (module.errors && module.errors.length > 0) {
      return <Badge variant="destructive" className="bg-yellow-100 text-yellow-800">Issues</Badge>;
    } else {
      return <Badge variant="secondary" className="bg-red-100 text-red-800">Unavailable</Badge>;
    }
  };

  const getFilteredModules = () => {
    switch (filter) {
      case 'available':
        return modules.filter(module => module.available || module.statusText === 'Always Available');
      case 'unavailable':
        return modules.filter(module => !module.available && module.statusText !== 'Always Available' && (!module.errors || module.errors.length === 0));
      case 'errors':
        return modules.filter(module => module.errors && module.errors.length > 0);
      default:
        return modules;
    }
  };

  const getFilterStats = () => {
    const available = modules.filter(module => module.available || module.statusText === 'Always Available').length;
    const withErrors = modules.filter(module => module.errors && module.errors.length > 0).length;
    const unavailable = modules.filter(module => !module.available && module.statusText !== 'Always Available' && (!module.errors || module.errors.length === 0)).length;
    
    return { total: modules.length, available, withErrors, unavailable };
  };

  const stats = getFilterStats();
  const filteredModules = getFilteredModules();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Sidebar */}
      <div className="relative ml-auto w-96 h-full bg-white shadow-xl overflow-hidden flex flex-col">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold flex items-center gap-2">
                <Package className="h-6 w-6" />
                Module Extensions
              </h2>
              <p className="text-blue-100 text-sm mt-1">
                Always available module navigation and management
              </p>
            </div>
            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={loadModules}
                disabled={loading}
                className="text-white hover:bg-white/20"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="text-white hover:bg-white/20"
              >
                ×
              </Button>
            </div>
          </div>
          
          {/* Stats */}
          <div className="grid grid-cols-4 gap-2 mt-4">
            <div className="text-center">
              <div className="text-lg font-bold">{stats.total}</div>
              <div className="text-xs text-blue-100">Total</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-200">{stats.available}</div>
              <div className="text-xs text-blue-100">Available</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-yellow-200">{stats.withErrors}</div>
              <div className="text-xs text-blue-100">Issues</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-red-200">{stats.unavailable}</div>
              <div className="text-xs text-blue-100">Missing</div>
            </div>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="bg-gray-50 px-4 py-3 border-b">
          <div className="flex gap-1">
            {[
              { key: 'all', label: 'All', count: stats.total },
              { key: 'available', label: 'Available', count: stats.available },
              { key: 'errors', label: 'Issues', count: stats.withErrors },
              { key: 'unavailable', label: 'Missing', count: stats.unavailable }
            ].map(({ key, label, count }) => (
              <Button
                key={key}
                variant={filter === key ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setFilter(key)}
                className="text-xs"
              >
                {label} ({count})
              </Button>
            ))}
          </div>
        </div>

        {/* Module List */}
        <div className="flex-1 overflow-y-auto p-4 space-y-3">
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto text-blue-500 mb-2" />
              <p className="text-gray-500">Loading modules...</p>
            </div>
          ) : filteredModules.length === 0 ? (
            <div className="text-center py-8">
              <Package className="h-12 w-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">No modules in this filter</p>
              <p className="text-gray-400 text-sm">
                {filter === 'all' 
                  ? 'Loading modules...'
                  : `No modules match the "${filter}" filter`
                }
              </p>
            </div>
          ) : (
            filteredModules.map((module, index) => (
              <Card key={module.key || index} className="transition-all duration-200 hover:shadow-md">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3">
                      {getStatusIcon(module)}
                      <div className="flex-1">
                        <CardTitle className="text-base">{module.name}</CardTitle>
                        <p className="text-sm text-gray-500 mt-1">
                          Key: <code className="bg-gray-100 px-1 rounded text-xs">{module.key}</code>
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(module)}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleDetails(module.key)}
                      >
                        {showDetails[module.key] ? (
                          <EyeOff className="h-4 w-4" />
                        ) : (
                          <Eye className="h-4 w-4" />
                        )}
                      </Button>
                    </div>
                  </div>
                </CardHeader>

                {/* Module Details */}
                {showDetails[module.key] && (
                  <CardContent className="pt-0">
                    <div className="space-y-3">
                      {/* Component Status */}
                      <div className="grid grid-cols-2 gap-3">
                        <div className="flex items-center gap-2 text-sm">
                          <Code className="h-4 w-4" />
                          <span>Controller:</span>
                          {module.controller_exists ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-500" />
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Database className="h-4 w-4" />
                          <span>Model:</span>
                          {module.model_exists ? (
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          ) : (
                            <XCircle className="h-4 w-4 text-red-500" />
                          )}
                        </div>
                      </div>

                      {/* Laravel Module Status */}
                      {module.is_laravel_module && (
                        <div className="flex items-center gap-2 text-sm">
                          <Folder className="h-4 w-4" />
                          <span>Laravel Module:</span>
                          <Badge variant="outline" className="text-xs">
                            Enabled
                          </Badge>
                        </div>
                      )}

                      {/* Errors */}
                      {module.errors && module.errors.length > 0 && (
                        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                          <div className="flex items-center gap-2 text-sm font-medium text-yellow-800 mb-2">
                            <AlertTriangle className="h-4 w-4" />
                            Issues Found
                          </div>
                          <ul className="text-xs text-yellow-700 space-y-1">
                            {module.errors.map((error, idx) => (
                              <li key={idx} className="flex items-start gap-1">
                                <span className="block w-1 h-1 bg-yellow-500 rounded-full mt-2 flex-shrink-0"></span>
                                <span>{error}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* Success Message */}
                      {(module.available || module.statusText === 'Always Available') && (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                          <div className="flex items-center gap-2 text-sm font-medium text-green-800">
                            <CheckCircle className="h-4 w-4" />
                            {module.statusText === 'Always Available' ? 'Module is always available' : 'Module is fully functional'}
                          </div>
                          <p className="text-xs text-green-700 mt-1">
                            {module.statusText === 'Always Available' 
                              ? 'This module is part of the core system and is always accessible.'
                              : 'Controller and model are available and working correctly.'
                            }
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                )}
              </Card>
            ))
          )}
        </div>

        {/* Footer Actions */}
        <div className="border-t bg-gray-50 p-4">
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="flex-1">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button variant="outline" size="sm" className="flex-1">
              <Upload className="h-4 w-4 mr-2" />
              Install Module
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ModuleExtensionsSidebar;

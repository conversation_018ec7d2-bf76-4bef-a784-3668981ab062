<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Customer;  // <-- Import your model here
use Illuminate\Support\Str;

class CustomerSeeder extends Seeder
{
    public function run(): void
    {
        for ($i = 1; $i <= 20; $i++) {
            Customer::create([
                'name'    => 'Customer ' . $i,
                'email'   => 'customer' . $i . '@example.com',
                'phone'   => '01' . rand(500000000, 599999999),
                'status'  => rand(0, 1) ? 'active' : 'inactive',
                'address' => fake()->address(),
            ]);
        }
    }
}
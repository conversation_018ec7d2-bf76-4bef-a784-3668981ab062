import axios from 'axios';
import { API_BASE_URL } from '../config/api.js';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Accept': 'application/json',
  },
  timeout: 30000, // 30 second timeout
});

// Add auth token interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
  (response) => response.data,
  (error) => {
    console.error('Module API Error:', error);
    throw error;
  }
);

class ModuleAvailabilityService {
  /**
   * Get all available modules with their status
   */
  async getAvailableModules() {
    try {
      const response = await api.get('/modules/available');
      return response;
    } catch (error) {
      console.error('Failed to fetch available modules:', error);
      throw error;
    }
  }

  /**
   * Get sidebar configuration based on available modules
   */
  async getSidebarConfig() {
    try {
      const response = await api.get('/modules/sidebar-config');
      return response;
    } catch (error) {
      console.error('Failed to fetch sidebar config:', error);
      throw error;
    }
  }

  /**
   * Check if a specific module is available
   */
  async checkModule(moduleKey) {
    try {
      const response = await api.get(`/modules/check/${moduleKey}`);
      return response;
    } catch (error) {
      console.error(`Failed to check module ${moduleKey}:`, error);
      throw error;
    }
  }

  /**
   * Check multiple modules at once
   */
  async checkModules(moduleKeys) {
    try {
      const promises = moduleKeys.map(key => this.checkModule(key));
      const results = await Promise.allSettled(promises);
      
      const moduleStatus = {};
      moduleKeys.forEach((key, index) => {
        const result = results[index];
        if (result.status === 'fulfilled') {
          moduleStatus[key] = result.value.data;
        } else {
          moduleStatus[key] = {
            name: key,
            key: key,
            available: false,
            controller_exists: false,
            model_exists: false,
            is_laravel_module: false,
            errors: ['Failed to check module availability']
          };
        }
      });
      
      return {
        success: true,
        data: moduleStatus
      };
    } catch (error) {
      console.error('Failed to check multiple modules:', error);
      throw error;
    }
  }

  /**
   * Get filtered navigation items based on module availability
   */
  async getFilteredNavigation() {
    try {
      const sidebarConfig = await this.getSidebarConfig();
      
      if (!sidebarConfig.success) {
        throw new Error('Failed to get sidebar configuration');
      }

      const { sidebar_config, available_modules, module_details } = sidebarConfig.data;
      
      // Convert to the format expected by the Sidebar component
      const filteredNavigation = {
        mainNavigation: sidebar_config.main_navigation
          .filter(item => item.available || item.required)
          .map(item => ({ 
            moduleKey: item.key, 
            available: item.available,
            required: item.required 
          })),
        
        documentsNavigation: sidebar_config.documents_navigation
          .filter(item => item.available || item.required)
          .map(item => ({ 
            moduleKey: item.key, 
            available: item.available,
            required: item.required 
          })),
        
        availableModules: available_modules,
        moduleDetails: module_details
      };

      return {
        success: true,
        data: filteredNavigation
      };
    } catch (error) {
      console.error('Failed to get filtered navigation:', error);
      throw error;
    }
  }

  /**
   * Get module status text based on availability
   */
  getModuleStatusText(module) {
    if (module.available && module.controller_exists && module.model_exists) {
      return 'Fully Functional';
    } else if (module.available) {
      return 'Available with Issues';
    } else if (module.controller_exists || module.model_exists) {
      return 'Partially Available';
    } else {
      return 'Not Available';
    }
  }

  /**
   * Refresh module cache
   */
  async refreshModuleCache() {
    try {
      const response = await api.post('/modules/refresh-cache');
      return response;
    } catch (error) {
      console.error('Error refreshing module cache:', error);
      throw error;
    }
  }

  /**
   * Get detailed information about a specific module
   */
  async getModuleDetails(moduleKey) {
    try {
      const response = await api.get(`/modules/${moduleKey}/details`);
      return response;
    } catch (error) {
      console.error('Error fetching module details:', error);
      throw error;
    }
  }

  /**
   * Toggle module status (enable/disable)
   */
  async toggleModuleStatus(moduleKey, enable = true) {
    try {
      const response = await api.post(`/modules/${moduleKey}/${enable ? 'enable' : 'disable'}`);
      return response;
    } catch (error) {
      console.error('Error toggling module status:', error);
      throw error;
    }
  }

  /**
   * Install a new module
   */
  async installModule(moduleData) {
    try {
      const response = await api.post('/modules/install', moduleData);
      return response;
    } catch (error) {
      console.error('Error installing module:', error);
      throw error;
    }
  }

  /**
   * Export module report in different formats
   */
  async exportModuleReport(format = 'json') {
    try {
      const response = await api.get(`/modules/export?format=${format}`, {
        responseType: format === 'csv' ? 'blob' : 'json'
      });
      
      if (format === 'csv') {
        // Handle CSV download
        const blob = new Blob([response.data], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `modules-report-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
        
        return { success: true, message: 'Report downloaded successfully' };
      }
      
      return response;
    } catch (error) {
      console.error('Error exporting module report:', error);
      throw error;
    }
  }

  /**
   * Get module icon based on type
   */
  getModuleIcon(moduleKey) {
    const icons = {
      'landowners': '🏠',
      'projects': '🏗️',
      'employees': '👥',
      'contractors': '🔧',
      'vendors': '🏢',
      'vendortypes': '📋',
      'units': '🏘️',
      'properties': '🏢',
      'finance': '💰',
      'reports': '📊',
      'settings': '⚙️',
      'users': '👤',
      'roles': '🔐'
    };
    
    return icons[moduleKey.toLowerCase()] || '📦';
  }

  /**
   * Get module color theme
   */
  getModuleColor(moduleKey) {
    const colors = {
      'landowners': 'green',
      'projects': 'blue',
      'employees': 'purple',
      'contractors': 'orange',
      'vendors': 'indigo',
      'vendortypes': 'gray',
      'units': 'yellow',
      'properties': 'red',
      'finance': 'emerald',
      'reports': 'teal',
      'settings': 'slate',
      'users': 'pink',
      'roles': 'violet'
    };
    
    return colors[moduleKey.toLowerCase()] || 'blue';
  }
}

export const moduleAvailabilityService = new ModuleAvailabilityService();

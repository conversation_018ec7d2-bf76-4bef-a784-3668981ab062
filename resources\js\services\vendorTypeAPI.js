import api from '../config/api';

const vendorTypeAPI = {
  // Get all vendor types with filters
  getVendorTypes: (params = {}) => {
    return api.get('/vendor-types', { params });
  },

  // Get vendor type by ID
  getVendorType: (id) => {
    return api.get(`/vendor-types/${id}`);
  },

  // Create new vendor type
  createVendorType: (data) => {
    return api.post('/vendor-types', data);
  },

  // Update vendor type
  updateVendorType: (id, data) => {
    return api.put(`/vendor-types/${id}`, data);
  },

  // Delete vendor type
  deleteVendorType: (id) => {
    return api.delete(`/vendor-types/${id}`);
  },

  // Get vendor types for dropdown
  getVendorTypesDropdown: () => {
    return api.get('/vendor-types-dropdown');
  },

  // Get vendor type statistics
  getVendorTypeStatistics: () => {
    return api.get('/vendor-types-statistics');
  },

  // Bulk status update
  bulkStatusUpdate: (vendorTypeIds, status) => {
    return api.post('/vendor-types/bulk-status-update', {
      vendor_type_ids: vendorTypeIds,
      status: status
    });
  }
};

export default vendorTypeAPI;

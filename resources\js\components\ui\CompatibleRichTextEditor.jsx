import React, { useMemo, useEffect, useRef, forwardRef } from 'react';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import '../../../css/rich-text-editor.css';

// Wrapper component to handle React Quill compatibility issues
const CompatibleRichTextEditor = forwardRef(({ 
  value, 
  onChange, 
  placeholder = "Enter text...", 
  disabled = false,
  height = "150px"
}, ref) => {
  const quillRef = useRef(null);
  const mountedRef = useRef(false);

  // Handle mount lifecycle properly
  useEffect(() => {
    mountedRef.current = true;
    return () => {
      mountedRef.current = false;
    };
  }, []);

  // Suppress React Quill deprecation warnings
  useEffect(() => {
    const originalError = console.error;
    const originalWarn = console.warn;
    
    const suppressMessage = (message) => {
      if (typeof message === 'string') {
        return message.includes('findDOMNode') || 
               message.includes('DOMNodeInserted') ||
               message.includes('mutation event') ||
               message.includes('[Deprecation]') ||
               (message.includes('Warning:') && message.includes('ReactQuill'));
      }
      return false;
    };
    
    console.error = (...args) => {
      if (!suppressMessage(args[0])) {
        originalError.apply(console, args);
      }
    };
    
    console.warn = (...args) => {
      if (!suppressMessage(args[0])) {
        originalWarn.apply(console, args);
      }
    };

    return () => {
      console.error = originalError;
      console.warn = originalWarn;
    };
  }, []);

  const modules = useMemo(() => ({
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      [{ 'indent': '-1'}, { 'indent': '+1' }],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'align': [] }],
      ['link'],
      ['clean']
    ],
  }), []);

  const formats = [
    'header',
    'bold', 'italic', 'underline', 'strike',
    'list', 'bullet', 'indent',
    'color', 'background',
    'align',
    'link'
  ];

  // Safe onChange handler
  const handleChange = (content) => {
    if (mountedRef.current && onChange) {
      onChange(content);
    }
  };

  return (
    <div className="rich-text-editor" style={{ position: 'relative' }}>
      <ReactQuill
        ref={(el) => {
          quillRef.current = el;
          if (ref) {
            if (typeof ref === 'function') {
              ref(el);
            } else {
              ref.current = el;
            }
          }
        }}
        theme="snow"
        value={value || ''}
        onChange={handleChange}
        modules={modules}
        formats={formats}
        placeholder={placeholder}
        readOnly={disabled}
        style={{
          height: height,
          marginBottom: disabled ? '0px' : '42px'
        }}
        preserveWhitespace
        bounds={'.rich-text-editor'}
      />
    </div>
  );
});

CompatibleRichTextEditor.displayName = 'CompatibleRichTextEditor';

export default CompatibleRichTextEditor;

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('land_addresses', function (Blueprint $table) {
            // Add new columns for the updated structure (check if they don't already exist)
            if (!Schema::hasColumn('land_addresses', 'country_id')) {
                $table->unsignedBigInteger('country_id')->nullable()->after('land_acquisition_id');
                $table->foreign('country_id')->references('id')->on('countries')->onDelete('set null');
            }
            
            if (!Schema::hasColumn('land_addresses', 'state_id')) {
                $table->unsignedBigInteger('state_id')->nullable()->after('country_id');
                $table->foreign('state_id')->references('id')->on('states')->onDelete('set null');
            }
            
            if (!Schema::hasColumn('land_addresses', 'city_text')) {
                $table->string('city_text')->nullable()->after('state_id'); // Renamed to avoid conflict
            }
            
            if (!Schema::hasColumn('land_addresses', 'specific_address')) {
                $table->text('specific_address')->nullable()->after('city_text');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('land_addresses', function (Blueprint $table) {
            // Drop foreign key constraints first if they exist
            if (Schema::hasColumn('land_addresses', 'country_id')) {
                $table->dropForeign(['country_id']);
                $table->dropColumn('country_id');
            }
            
            if (Schema::hasColumn('land_addresses', 'state_id')) {
                $table->dropForeign(['state_id']);
                $table->dropColumn('state_id');
            }
            
            if (Schema::hasColumn('land_addresses', 'city_text')) {
                $table->dropColumn('city_text');
            }
            
            if (Schema::hasColumn('land_addresses', 'specific_address')) {
                $table->dropColumn('specific_address');
            }
        });
    }
};

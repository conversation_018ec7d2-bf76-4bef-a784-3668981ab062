import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from '@/hooks/useTranslation';
import LanguageSwitcher from '@/components/ui/LanguageSwitcher';
import { 
  Globe, 
  Users, 
  Settings, 
  ShieldCheck, 
  Building2, 
  Languages,
  TestTube
} from 'lucide-react';

const TranslationTestPage = () => {
  const { t, currentLanguage, isLoaded } = useTranslation();

  if (!isLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        <span className="ml-3 text-lg">Loading translations...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <TestTube className="h-8 w-8" />
            Translation System Test
          </h1>
          <p className="text-muted-foreground mt-2">
            Current Language: <Badge variant="secondary">{currentLanguage}</Badge>
          </p>
        </div>
      </div>

      {/* Language Switcher */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-1">
          <LanguageSwitcher />
        </div>

        {/* Translation Tests */}
        <div className="lg:col-span-2 space-y-4">
          {/* Common Translations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Common Translations
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex flex-wrap gap-2">
                <Button size="sm">{t('common.buttons.add')}</Button>
                <Button size="sm" variant="outline">{t('common.buttons.edit')}</Button>
                <Button size="sm" variant="destructive">{t('common.buttons.delete')}</Button>
                <Button size="sm" variant="secondary">{t('common.buttons.save')}</Button>
                <Button size="sm" variant="ghost">{t('common.buttons.cancel')}</Button>
              </div>
              <div className="flex flex-wrap gap-2 mt-3">
                <Badge>{t('common.status.active')}</Badge>
                <Badge variant="secondary">{t('common.status.inactive')}</Badge>
                <Badge variant="destructive">{t('common.status.pending')}</Badge>
                <Badge variant="outline">{t('common.status.approved')}</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Navigation Translations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Navigation Translations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="font-medium">{t('common.navigation.dashboard')}</p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="font-medium">{t('common.navigation.landOwners')}</p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="font-medium">{t('common.navigation.landAcquisition')}</p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="font-medium">{t('common.navigation.language')}</p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="font-medium">{t('common.navigation.currency')}</p>
                </div>
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="font-medium">{t('common.navigation.roleManagement')}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Module Translations */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Land Owners */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  {t('landOwners.title')}
                </CardTitle>
                <CardDescription>{t('landOwners.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p><strong>{t('landOwners.fields.fullName')}:</strong> John Doe</p>
                  <p><strong>{t('landOwners.fields.email')}:</strong> <EMAIL></p>
                  <p><strong>{t('landOwners.fields.phone')}:</strong> ******-0123</p>
                  <p><strong>{t('landOwners.fields.status')}:</strong> 
                    <Badge className="ml-2">{t('common.status.active')}</Badge>
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Language Management */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Languages className="h-5 w-5" />
                  {t('language.title')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p><strong>{t('language.fields.name')}:</strong> English</p>
                  <p><strong>{t('language.fields.code')}:</strong> en</p>
                  <p><strong>{t('language.fields.direction')}:</strong> {t('language.direction.ltr')}</p>
                  <p><strong>{t('language.fields.status')}:</strong> 
                    <Badge className="ml-2">{t('common.status.active')}</Badge>
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Currency Management */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  {t('currency.title')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p><strong>{t('currency.fields.name')}:</strong> US Dollar</p>
                  <p><strong>{t('currency.fields.code')}:</strong> USD</p>
                  <p><strong>{t('currency.fields.symbol')}:</strong> $</p>
                  <p><strong>{t('currency.fields.exchangeRate')}:</strong> 1.0000</p>
                </div>
              </CardContent>
            </Card>

            {/* Role Management */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShieldCheck className="h-5 w-5" />
                  {t('roles.title')}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <p><strong>{t('roles.fields.name')}:</strong> Administrator</p>
                  <p><strong>{t('roles.fields.description')}:</strong> Full system access</p>
                  <p><strong>{t('roles.fields.permissions')}:</strong></p>
                  <div className="flex flex-wrap gap-1 mt-2">
                    <Badge variant="outline" size="sm">{t('roles.permissions.create')}</Badge>
                    <Badge variant="outline" size="sm">{t('roles.permissions.read')}</Badge>
                    <Badge variant="outline" size="sm">{t('roles.permissions.update')}</Badge>
                    <Badge variant="outline" size="sm">{t('roles.permissions.delete')}</Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Messages Test */}
          <Card>
            <CardHeader>
              <CardTitle>Message Translations</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800">{t('common.messages.success')}</p>
                </div>
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-800">{t('common.messages.error')}</p>
                </div>
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <p className="text-yellow-800">{t('common.messages.warning')}</p>
                </div>
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <p className="text-blue-800">{t('common.messages.info')}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default TranslationTestPage;

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('land_owners', function (Blueprint $table) {
            // Check if the name column exists before dropping
            if (Schema::hasColumn('land_owners', 'name')) {
                $table->dropColumn('name');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('land_owners', function (Blueprint $table) {
            // Add back the name column
            if (!Schema::hasColumn('land_owners', 'name')) {
                $table->string('name')->after('id');
            }
        });
    }
};

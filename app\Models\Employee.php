<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\AuditableTrait;
use App\Traits\EnvironmentAwareTrait;

class Employee extends Model
{
    use HasFactory, AuditableTrait, EnvironmentAwareTrait;

    protected $fillable = [
        'name',
        'designation',
        'phone',
        'email',
        'status',
        'date_of_joining',
        'address',
        'photo',
        'salary',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'date_of_joining' => 'datetime',
        'salary' => 'decimal:2'
    ];

    /**
     * Get the user who created this employee
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this employee
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Get the full photo URL
     */
    public function getPhotoUrlAttribute()
    {
        if ($this->photo) {
            return url('storage/employees/' . $this->photo);
        }
        return null;
    }
}

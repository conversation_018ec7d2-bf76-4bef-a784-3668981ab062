import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { TrendingUp, TrendingDown } from 'lucide-react';

const MetricCard = ({ 
  title, 
  value, 
  change, 
  trend = 'up', 
  description,
  className = '' 
}) => {
  const isPositive = trend === 'up';
  const TrendIcon = isPositive ? TrendingUp : TrendingDown;
  
  return (
    <Card className={`p-6 ${className}`}>
      <CardContent className="p-0">
        {/* Header with title and trend indicator */}
        <div className="flex items-center justify-between mb-2">
          <p className="text-sm font-medium text-muted-foreground">
            {title}
          </p>
          <div className="flex items-center text-xs">
            <TrendIcon className={`mr-1 h-3 w-3 ${isPositive ? 'text-green-600' : 'text-red-600'}`} />
            <span className={`font-medium ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
              {change}
            </span>
          </div>
        </div>
        
        {/* Main value */}
        <div className="text-3xl font-bold text-foreground mb-2">
          {value}
        </div>
        
        {/* Description with trend icon */}
        <div className="flex items-center text-sm text-muted-foreground">
          <TrendIcon className={`mr-1 h-3 w-3 ${isPositive ? 'text-green-600' : 'text-red-600'}`} />
          <span>{description}</span>
        </div>
      </CardContent>
    </Card>
  );
};

export default MetricCard;

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Migrate existing user names to first_name and last_name
        DB::statement("
            UPDATE users 
            SET first_name = TRIM(SUBSTRING_INDEX(name, ' ', 1)),
                last_name = TRIM(SUBSTRING_INDEX(name, ' ', -1))
            WHERE name IS NOT NULL AND name != ''
        ");
        
        // Handle cases where name has only one word
        DB::statement("
            UPDATE users 
            SET last_name = ''
            WHERE first_name = last_name AND name IS NOT NULL AND name != ''
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restore name field from first_name and last_name
        DB::statement("
            UPDATE users 
            SET name = TRIM(CONCAT(COALESCE(first_name, ''), ' ', COALESCE(last_name, '')))
            WHERE (first_name IS NOT NULL AND first_name != '') OR (last_name IS NOT NULL AND last_name != '')
        ");
    }
};

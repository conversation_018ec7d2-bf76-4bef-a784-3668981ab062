export default {
  // Elementos comuns da interface
  common: {
    buttons: {
      add: 'Adicionar',
      edit: 'Editar',
      delete: 'Deleta<PERSON>',
      save: '<PERSON><PERSON>',
      cancel: '<PERSON><PERSON><PERSON>',
      confirm: 'Confirmar',
      close: '<PERSON><PERSON><PERSON>',
      search: '<PERSON><PERSON>',
      filter: 'Filtrar',
      export: 'Exportar',
      import: 'Importar',
      refresh: 'Atualizar',
      loading: 'Carregando...',
      submit: 'Enviar',
      reset: 'Resetar',
      clear: 'Limpar',
      view: 'Visualizar',
      download: 'Baixar',
      upload: 'Carregar'
    },
    status: {
      active: 'Ativo',
      inactive: 'Inativo',
      pending: 'Pendente',
      approved: 'Aprovado',
      rejected: 'Rejeitado',
      completed: 'Completo',
      draft: 'Ras<PERSON>nho',
      published: 'Publicado'
    },
    messages: {
      success: 'Operação completada com sucesso',
      error: 'Ocorreu um erro',
      warning: 'Aviso',
      info: 'Informação',
      loading: 'Carregando, aguarde...',
      noData: 'Nenhum dado disponível',
      confirmDelete: 'Tem certeza de que deseja deletar este item?',
      confirmAction: 'Tem certeza de que deseja executar esta ação?',
      saved: 'Alterações salvas com sucesso',
      deleted: 'Item deletado com sucesso',
      updated: 'Item atualizado com sucesso',
      created: 'Item criado com sucesso'
    },
    navigation: {
      dashboard: 'Painel',
      analytics: 'Análises',
      landOwners: 'Proprietários de Terra',
      landAcquisition: 'Aquisição de Terra',
      lifecycle: 'Ciclo de Vida',
      country: 'País',
      language: 'Idioma',
      currency: 'Moeda',
      customers: 'Clientes',
      orders: 'Pedidos',
      components: 'Componentes',
      reports: 'Relatórios',
      roleManagement: 'Gerenciamento de Papéis',
      wordAssistant: 'Assistente de Palavras',
      settings: 'Configurações'
    },
    forms: {
      required: 'Este campo é obrigatório',
      invalidEmail: 'Por favor, insira um endereço de email válido',
      invalidPhone: 'Por favor, insira um número de telefone válido',
      passwordMismatch: 'As senhas não coincidem',
      minLength: 'Comprimento mínimo: {min} caracteres',
      maxLength: 'Comprimento máximo: {max} caracteres',
      selectOption: 'Por favor, selecione uma opção',
      invalidDate: 'Por favor, insira uma data válida',
      invalidNumber: 'Por favor, insira um número válido'
    },
    table: {
      name: 'Nome',
      code: 'Código',
      status: 'Status',
      actions: 'Ações',
      createdAt: 'Criado em',
      updatedAt: 'Atualizado em',
      noRecords: 'Nenhum registro encontrado',
      showing: 'Mostrando {start} a {end} de {total} entradas',
      previous: 'Anterior',
      next: 'Próximo',
      rowsPerPage: 'Linhas por página'
    },
    auditLog: 'Log de Auditoria',
    loading: 'Carregando...',
    noData: 'Nenhum dado disponível'
  },

  // Painel
  dashboard: {
    title: 'Painel',
    description: 'Aqui está o que está acontecendo com seu negócio hoje.',
    welcome: 'Bem-vindo de volta, {name}!',
    overview: 'Visão Geral',
    statistics: 'Estatísticas',
    recentActivity: 'Atividade Recente',
    quickActions: 'Ações Rápidas',
    viewReport: 'Ver Relatório',
    statistics: {
      totalRevenue: 'Receita Total',
      subscriptions: 'Assinaturas',
      sales: 'Vendas',
      activeNow: 'Ativo Agora',
      fromLastMonth: 'do mês passado',
      fromLastHour: 'da última hora'
    }
  },

  // Gerenciamento de Idiomas
  language: {
    title: 'Gerenciamento de Idiomas',
    addLanguage: 'Adicionar Idioma',
    editLanguage: 'Editar Idioma',
    default: 'Padrão',
    current: 'Atual',
    select: 'Selecionar',
    fields: {
      name: 'Nome',
      code: 'Código',
      nativeName: 'Nome Nativo',
      flag: 'Bandeira (emoji)',
      direction: 'Direção',
      status: 'Status',
      isDefault: 'Definir como idioma padrão'
    },
    direction: {
      ltr: 'Esquerda para Direita (LTR)',
      rtl: 'Direita para Esquerda (RTL)'
    },
    statistics: {
      totalLanguages: 'Total de Idiomas',
      activeLanguages: 'Idiomas Ativos',
      defaultLanguage: 'Idioma Padrão',
      ltrLanguages: 'Idiomas LTR',
      rtlLanguages: 'Idiomas RTL'
    },
    messages: {
      setDefault: 'Definir como Idioma Padrão?',
      setDefaultText: 'Isso tornará este idioma o padrão para o sistema',
      setDefaultConfirm: 'Sim, definir como padrão!',
      defaultUpdated: 'Idioma padrão atualizado com sucesso',
      onlyActiveCanBeDefault: 'Apenas idiomas ativos podem ser definidos como padrão'
    }
  },

  // Gerenciamento de Moedas
  currency: {
    title: 'Gerenciamento de Moedas',
    addCurrency: 'Adicionar Moeda',
    editCurrency: 'Editar Moeda',
    fields: {
      name: 'Nome',
      code: 'Código',
      symbol: 'Símbolo',
      exchangeRate: 'Taxa de Câmbio',
      isDefault: 'Definir como moeda padrão',
      isActive: 'Ativo'
    },
    statistics: {
      totalCurrencies: 'Total de Moedas',
      activeCurrencies: 'Moedas Ativas',
      defaultCurrency: 'Moeda Padrão',
      averageRate: 'Taxa Média'
    },
    messages: {
      setDefault: 'Definir como Moeda Padrão?',
      setDefaultText: 'Isso tornará esta moeda a padrão para o sistema',
      setDefaultConfirm: 'Sim, definir como padrão!',
      defaultUpdated: 'Moeda padrão atualizada com sucesso'
    }
  },

  // Proprietários de Terra
  landOwners: {
    title: 'Proprietários de Terra',
    description: 'Lista de todos os proprietários de terra no sistema',
    searchTitle: 'Buscar Proprietários de Terra',
    searchPlaceholder: 'Buscar por nome, nome do pai, telefone, número ID, ou email...',
    addOwner: 'Adicionar Novo Proprietário',
    editOwner: 'Editar Proprietário de Terra',
    fields: {
      fullName: 'Nome Completo',
      firstName: 'Primeiro Nome',
      lastName: 'Sobrenome',
      fatherName: 'Nome do Pai',
      motherName: 'Nome da Mãe',
      email: 'Email',
      phone: 'Telefone',
      address: 'Endereço',
      city: 'Cidade',
      state: 'Estado',
      zipCode: 'CEP',
      country: 'País',
      dateOfBirth: 'Data de Nascimento',
      nationalId: 'ID Nacional',
      nidNumber: 'Número ID',
      status: 'Status',
      photo: 'Foto',
      documentType: 'Tipo de Documento',
      nidFront: 'ID Frente',
      nidBack: 'ID Verso',
      passportPhoto: 'Foto do Passaporte'
    },
    statistics: {
      totalOwners: 'Total de Proprietários',
      activeOwners: 'Proprietários Ativos',
      newThisMonth: 'Novos Este Mês',
      totalLands: 'Total de Terras'
    },
    messages: {
      confirmDelete: 'Tem certeza de que deseja deletar este proprietário de terra?',
      deleteSuccess: 'Proprietário de terra deletado com sucesso',
      createSuccess: 'Proprietário de terra criado com sucesso',
      updateSuccess: 'Proprietário de terra atualizado com sucesso',
      loadError: 'Falha ao carregar proprietários de terra. Tente novamente.',
      deleteError: 'Falha ao deletar proprietário de terra. Tente novamente.',
      createError: 'Falha ao criar proprietário de terra. Tente novamente.',
      updateError: 'Falha ao atualizar proprietário de terra. Tente novamente.'
    }
  },

  // Aquisição de Terra
  landAcquisition: {
    title: 'Aquisição de Terra',
    addAcquisition: 'Adicionar Aquisição de Terra',
    editAcquisition: 'Editar Aquisição de Terra',
    fields: {
      landSize: 'Tamanho da Terra',
      location: 'Localização',
      price: 'Preço',
      acquisitionDate: 'Data de Aquisição',
      status: 'Status',
      description: 'Descrição',
      documents: 'Documentos'
    },
    statistics: {
      totalAcquisitions: 'Total de Aquisições',
      completedDeals: 'Negócios Completos',
      totalValue: 'Valor Total',
      averageSize: 'Tamanho Médio'
    }
  },

  // Gerenciamento de Usuários
  users: {
    title: 'Gerenciamento de Usuários',
    profile: 'Perfil',
    fields: {
      firstName: 'Primeiro Nome',
      lastName: 'Sobrenome',
      email: 'Email',
      phone: 'Telefone',
      role: 'Papel',
      company: 'Empresa',
      bio: 'Biografia',
      timezone: 'Fuso Horário',
      language: 'Idioma'
    },
    messages: {
      profileUpdated: 'Perfil atualizado com sucesso',
      passwordChanged: 'Senha alterada com sucesso'
    }
  },

  // Gerenciamento de Papéis
  roles: {
    title: 'Gerenciamento de Papéis',
    addRole: 'Adicionar Papel',
    editRole: 'Editar Papel',
    fields: {
      name: 'Nome',
      description: 'Descrição',
      permissions: 'Permissões',
      modules: 'Módulos Acessíveis'
    },
    permissions: {
      create: 'Criar',
      read: 'Ler',
      update: 'Atualizar',
      delete: 'Deletar',
      export: 'Exportar',
      manage: 'Gerenciar',
      use: 'Usar'
    }
  },

  // Configurações
  settings: {
    title: 'Configurações',
    general: 'Configurações Gerais',
    profile: 'Configurações de Perfil',
    security: 'Configurações de Segurança',
    notifications: 'Configurações de Notificações',
    language: 'Idioma e Localização',
    languageDescription: 'Escolha seu idioma preferido',
    appearance: 'Aparência',
    system: 'Configurações do Sistema'
  },

  // Autenticação
  auth: {
    login: 'Entrar',
    logout: 'Sair',
    register: 'Registrar',
    forgotPassword: 'Esqueci a Senha',
    resetPassword: 'Redefinir Senha',
    changePassword: 'Alterar Senha',
    currentPassword: 'Senha Atual',
    newPassword: 'Nova Senha',
    confirmPassword: 'Confirmar Senha',
    rememberMe: 'Lembrar-me',
    loginSuccess: 'Login bem-sucedido',
    logoutSuccess: 'Logout bem-sucedido',
    invalidCredentials: 'Email ou senha inválidos',
    sessionExpired: 'Sua sessão expirou. Faça login novamente.'
  },

  // Data e Hora
  date: {
    today: 'Hoje',
    yesterday: 'Ontem',
    tomorrow: 'Amanhã',
    thisWeek: 'Esta Semana',
    thisMonth: 'Este Mês',
    thisYear: 'Este Ano',
    lastWeek: 'Semana Passada',
    lastMonth: 'Mês Passado',
    lastYear: 'Ano Passado',
    formats: {
      short: 'DD/MM/YYYY',
      medium: 'DD MMM YYYY',
      long: 'DD MMMM YYYY',
      full: 'dddd, DD MMMM YYYY'
    }
  },

  // Upload de Arquivo
  upload: {
    selectFile: 'Selecionar Arquivo',
    dropFile: 'Soltar arquivo aqui',
    uploading: 'Carregando...',
    uploadSuccess: 'Arquivo carregado com sucesso',
    uploadError: 'Falha no upload',
    invalidFileType: 'Tipo de arquivo inválido',
    fileTooLarge: 'Arquivo muito grande',
    maxSize: 'Tamanho máximo do arquivo: {size}MB'
  },

  // Gerenciamento de Países
  country: {
    title: 'Gerenciamento de Países',
    description: 'Gerenciar países e suas informações no sistema',
    searchTitle: 'Buscar Países',
    searchPlaceholder: 'Buscar por nome, código, ou continente...',
    addCountry: 'Adicionar País',
    editCountry: 'Editar País',
    fields: {
      name: 'Nome',
      code: 'Código',
      isoCode: 'Código ISO',
      capital: 'Capital',
      currency: 'Moeda',
      phoneCode: 'Código de Telefone',
      continent: 'Continente',
      population: 'População',
      status: 'Status'
    },
    statistics: {
      totalCountries: 'Total de Países',
      activeCountries: 'Países Ativos',
      inactiveCountries: 'Países Inativos'
    },
    messages: {
      confirmDelete: 'Tem certeza de que deseja deletar este país?',
      deleteSuccess: 'País deletado com sucesso',
      createSuccess: 'País criado com sucesso',
      updateSuccess: 'País atualizado com sucesso',
      loadError: 'Falha ao carregar países. Tente novamente.',
      deleteError: 'Falha ao deletar país. Tente novamente.',
      createError: 'Falha ao criar país. Tente novamente.',
      updateError: 'Falha ao atualizar país. Tente novamente.'
    }
  }
};

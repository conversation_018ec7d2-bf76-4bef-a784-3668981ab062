export default {
  // Elementos comunes de la interfaz
  common: {
    buttons: {
      add: '<PERSON><PERSON><PERSON>ü<PERSON>',
      edit: 'Bear<PERSON><PERSON>',
      delete: '<PERSON>ö<PERSON>',
      save: '<PERSON><PERSON><PERSON><PERSON>',
      cancel: 'Abbrechen',
      confirm: 'Bestätigen',
      close: '<PERSON><PERSON><PERSON>ße<PERSON>',
      search: 'Such<PERSON>',
      filter: 'Filter',
      export: 'Exportieren',
      import: 'Importieren',
      refresh: 'Aktualisieren',
      loading: 'Laden...',
      submit: 'Einreichen',
      reset: 'Zur<PERSON>set<PERSON>',
      clear: 'Lö<PERSON>',
      view: 'An<PERSON><PERSON>',
      download: 'Herunterladen',
      upload: 'Hochladen'
    },
    status: {
      active: 'Aktiv',
      inactive: 'Inaktiv',
      pending: 'Ausstehend',
      approved: 'Genehmigt',
      rejected: 'Abgelehnt',
      completed: 'Abgeschlossen',
      draft: 'Entwurf',
      published: 'Veröffentlicht'
    },
    messages: {
      success: 'Vorgang erfolgreich abgeschlossen',
      error: 'Ein Fehler ist aufgetreten',
      warning: 'Warnung',
      info: 'Information',
      loading: 'Laden, bitte warten...',
      noData: 'Keine Daten verfügbar',
      confirmDelete: 'Sind Sie sicher, dass Sie dieses Element löschen möchten?',
      confirmAction: 'Sind Sie sicher, dass Sie diese Aktion ausführen möchten?',
      saved: 'Änderungen erfolgreich gespeichert',
      deleted: 'Element erfolgreich gelöscht',
      updated: 'Element erfolgreich aktualisiert',
      created: 'Element erfolgreich erstellt'
    },
    navigation: {
      dashboard: 'Dashboard',
      analytics: 'Analytics',
      landOwners: 'Grundbesitzer',
      landAcquisition: 'Grunderwerb',
      lifecycle: 'Lebenszyklus',
      country: 'Land',
      language: 'Sprache',
      currency: 'Währung',
      customers: 'Kunden',
      orders: 'Bestellungen',
      components: 'Komponenten',
      reports: 'Berichte',
      roleManagement: 'Rollenverwaltung',
      wordAssistant: 'Wortassistent',
      settings: 'Einstellungen'
    },
    forms: {
      required: 'Dieses Feld ist erforderlich',
      invalidEmail: 'Bitte geben Sie eine gültige E-Mail-Adresse ein',
      invalidPhone: 'Bitte geben Sie eine gültige Telefonnummer ein',
      passwordMismatch: 'Passwörter stimmen nicht überein',
      minLength: 'Mindestlänge: {min} Zeichen',
      maxLength: 'Maximale Länge: {max} Zeichen',
      selectOption: 'Bitte wählen Sie eine Option',
      invalidDate: 'Bitte geben Sie ein gültiges Datum ein',
      invalidNumber: 'Bitte geben Sie eine gültige Zahl ein'
    },
    table: {
      name: 'Name',
      code: 'Code',
      status: 'Status',
      actions: 'Aktionen',
      createdAt: 'Erstellt am',
      updatedAt: 'Aktualisiert am',
      noRecords: 'Keine Datensätze gefunden',
      showing: 'Zeige {start} bis {end} von {total} Einträgen',
      previous: 'Vorherige',
      next: 'Nächste',
      rowsPerPage: 'Zeilen pro Seite'
    },
    auditLog: 'Prüfprotokoll',
    loading: 'Laden...',
    noData: 'Keine Daten verfügbar'
  },

  // Dashboard
  dashboard: {
    title: 'Dashboard',
    description: 'Hier ist, was heute in Ihrem Unternehmen passiert.',
    welcome: 'Willkommen zurück, {name}!',
    overview: 'Überblick',
    statistics: 'Statistiken',
    recentActivity: 'Letzte Aktivität',
    quickActions: 'Schnellaktionen',
    viewReport: 'Bericht anzeigen',
    statistics: {
      totalRevenue: 'Gesamtumsatz',
      subscriptions: 'Abonnements',
      sales: 'Verkäufe',
      activeNow: 'Jetzt aktiv',
      fromLastMonth: 'vom letzten Monat',
      fromLastHour: 'von der letzten Stunde'
    }
  },

  // Sprachverwaltung
  language: {
    title: 'Sprachverwaltung',
    addLanguage: 'Sprache hinzufügen',
    editLanguage: 'Sprache bearbeiten',
    default: 'Standard',
    current: 'Aktuell',
    select: 'Auswählen',
    fields: {
      name: 'Name',
      code: 'Code',
      nativeName: 'Nativer Name',
      flag: 'Flagge (Emoji)',
      direction: 'Richtung',
      status: 'Status',
      isDefault: 'Als Standardsprache festlegen'
    },
    direction: {
      ltr: 'Links nach rechts (LTR)',
      rtl: 'Rechts nach links (RTL)'
    },
    statistics: {
      totalLanguages: 'Gesamt Sprachen',
      activeLanguages: 'Aktive Sprachen',
      defaultLanguage: 'Standardsprache',
      ltrLanguages: 'LTR Sprachen',
      rtlLanguages: 'RTL Sprachen'
    },
    messages: {
      setDefault: 'Als Standardsprache festlegen?',
      setDefaultText: 'Dies wird diese Sprache zur Standardsprache des Systems machen',
      setDefaultConfirm: 'Ja, als Standard festlegen!',
      defaultUpdated: 'Standardsprache erfolgreich aktualisiert',
      onlyActiveCanBeDefault: 'Nur aktive Sprachen können als Standard festgelegt werden'
    }
  },

  // Währungsverwaltung
  currency: {
    title: 'Währungsverwaltung',
    addCurrency: 'Währung hinzufügen',
    editCurrency: 'Währung bearbeiten',
    fields: {
      name: 'Name',
      code: 'Code',
      symbol: 'Symbol',
      exchangeRate: 'Wechselkurs',
      isDefault: 'Als Standardwährung festlegen',
      isActive: 'Aktiv'
    },
    statistics: {
      totalCurrencies: 'Gesamt Währungen',
      activeCurrencies: 'Aktive Währungen',
      defaultCurrency: 'Standardwährung',
      averageRate: 'Durchschnittskurs'
    },
    messages: {
      setDefault: 'Als Standardwährung festlegen?',
      setDefaultText: 'Dies wird diese Währung zur Standardwährung des Systems machen',
      setDefaultConfirm: 'Ja, als Standard festlegen!',
      defaultUpdated: 'Standardwährung erfolgreich aktualisiert'
    }
  },

  // Grundbesitzer
  landOwners: {
    title: 'Grundbesitzer',
    description: 'Liste aller Grundbesitzer im System',
    searchTitle: 'Grundbesitzer suchen',
    searchPlaceholder: 'Suchen nach Name, Vaters Name, Telefon, NID-Nummer oder E-Mail...',
    addOwner: 'Neuen Besitzer hinzufügen',
    editOwner: 'Grundbesitzer bearbeiten',
    fields: {
      fullName: 'Vollständiger Name',
      firstName: 'Vorname',
      lastName: 'Nachname',
      fatherName: 'Vaters Name',
      motherName: 'Mutters Name',
      email: 'E-Mail',
      phone: 'Telefon',
      address: 'Adresse',
      city: 'Stadt',
      state: 'Bundesland',
      zipCode: 'Postleitzahl',
      country: 'Land',
      dateOfBirth: 'Geburtsdatum',
      nationalId: 'Nationale ID',
      nidNumber: 'NID-Nummer',
      status: 'Status',
      photo: 'Foto',
      documentType: 'Dokumenttyp',
      nidFront: 'NID Vorderseite',
      nidBack: 'NID Rückseite',
      passportPhoto: 'Passfoto'
    },
    statistics: {
      totalOwners: 'Gesamt Besitzer',
      activeOwners: 'Aktive Besitzer',
      newThisMonth: 'Neue diesen Monat',
      totalLands: 'Gesamt Grundstücke'
    },
    messages: {
      confirmDelete: 'Sind Sie sicher, dass Sie diesen Grundbesitzer löschen möchten?',
      deleteSuccess: 'Grundbesitzer erfolgreich gelöscht',
      createSuccess: 'Grundbesitzer erfolgreich erstellt',
      updateSuccess: 'Grundbesitzer erfolgreich aktualisiert',
      loadError: 'Fehler beim Laden der Grundbesitzer. Bitte versuchen Sie es erneut.',
      deleteError: 'Fehler beim Löschen des Grundbesitzers. Bitte versuchen Sie es erneut.',
      createError: 'Fehler beim Erstellen des Grundbesitzers. Bitte versuchen Sie es erneut.',
      updateError: 'Fehler beim Aktualisieren des Grundbesitzers. Bitte versuchen Sie es erneut.'
    }
  },

  // Grunderwerb
  landAcquisition: {
    title: 'Grunderwerb',
    addAcquisition: 'Grunderwerb hinzufügen',
    editAcquisition: 'Grunderwerb bearbeiten',
    fields: {
      landSize: 'Grundstücksgröße',
      location: 'Standort',
      price: 'Preis',
      acquisitionDate: 'Erwerbsdatum',
      status: 'Status',
      description: 'Beschreibung',
      documents: 'Dokumente'
    },
    statistics: {
      totalAcquisitions: 'Gesamt Erwerbungen',
      completedDeals: 'Abgeschlossene Geschäfte',
      totalValue: 'Gesamtwert',
      averageSize: 'Durchschnittsgröße'
    }
  },

  // Benutzerverwaltung
  users: {
    title: 'Benutzerverwaltung',
    profile: 'Profil',
    fields: {
      firstName: 'Vorname',
      lastName: 'Nachname',
      email: 'E-Mail',
      phone: 'Telefon',
      role: 'Rolle',
      company: 'Unternehmen',
      bio: 'Biografie',
      timezone: 'Zeitzone',
      language: 'Sprache'
    },
    messages: {
      profileUpdated: 'Profil erfolgreich aktualisiert',
      passwordChanged: 'Passwort erfolgreich geändert'
    }
  },

  // Rollenverwaltung
  roles: {
    title: 'Rollenverwaltung',
    addRole: 'Rolle hinzufügen',
    editRole: 'Rolle bearbeiten',
    fields: {
      name: 'Name',
      description: 'Beschreibung',
      permissions: 'Berechtigungen',
      modules: 'Zugängliche Module'
    },
    permissions: {
      create: 'Erstellen',
      read: 'Lesen',
      update: 'Aktualisieren',
      delete: 'Löschen',
      export: 'Exportieren',
      manage: 'Verwalten',
      use: 'Verwenden'
    }
  },

  // Einstellungen
  settings: {
    title: 'Einstellungen',
    general: 'Allgemeine Einstellungen',
    profile: 'Profil-Einstellungen',
    security: 'Sicherheitseinstellungen',
    notifications: 'Benachrichtigungseinstellungen',
    language: 'Sprache & Lokalisierung',
    languageDescription: 'Wählen Sie Ihre bevorzugte Sprache',
    appearance: 'Erscheinungsbild',
    system: 'Systemeinstellungen'
  },

  // Authentifizierung
  auth: {
    login: 'Anmelden',
    logout: 'Abmelden',
    register: 'Registrieren',
    forgotPassword: 'Passwort vergessen',
    resetPassword: 'Passwort zurücksetzen',
    changePassword: 'Passwort ändern',
    currentPassword: 'Aktuelles Passwort',
    newPassword: 'Neues Passwort',
    confirmPassword: 'Passwort bestätigen',
    rememberMe: 'Angemeldet bleiben',
    loginSuccess: 'Anmeldung erfolgreich',
    logoutSuccess: 'Abmeldung erfolgreich',
    invalidCredentials: 'Ungültige E-Mail oder Passwort',
    sessionExpired: 'Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an.'
  },

  // Datum und Zeit
  date: {
    today: 'Heute',
    yesterday: 'Gestern',
    tomorrow: 'Morgen',
    thisWeek: 'Diese Woche',
    thisMonth: 'Dieser Monat',
    thisYear: 'Dieses Jahr',
    lastWeek: 'Letzte Woche',
    lastMonth: 'Letzter Monat',
    lastYear: 'Letztes Jahr',
    formats: {
      short: 'DD.MM.YYYY',
      medium: 'DD. MMM YYYY',
      long: 'DD. MMMM YYYY',
      full: 'dddd, DD. MMMM YYYY'
    }
  },

  // Datei-Upload
  upload: {
    selectFile: 'Datei auswählen',
    dropFile: 'Datei hier ablegen',
    uploading: 'Hochladen...',
    uploadSuccess: 'Datei erfolgreich hochgeladen',
    uploadError: 'Upload fehlgeschlagen',
    invalidFileType: 'Ungültiger Dateityp',
    fileTooLarge: 'Datei zu groß',
    maxSize: 'Maximale Dateigröße: {size}MB'
  },

  // Länderverwaltung
  country: {
    title: 'Länderverwaltung',
    description: 'Länder und ihre Informationen im System verwalten',
    searchTitle: 'Länder suchen',
    searchPlaceholder: 'Suchen nach Name, Code oder Kontinent...',
    addCountry: 'Land hinzufügen',
    editCountry: 'Land bearbeiten',
    fields: {
      name: 'Name',
      code: 'Code',
      isoCode: 'ISO-Code',
      capital: 'Hauptstadt',
      currency: 'Währung',
      phoneCode: 'Telefoncode',
      continent: 'Kontinent',
      population: 'Bevölkerung',
      status: 'Status'
    },
    statistics: {
      totalCountries: 'Gesamt Länder',
      activeCountries: 'Aktive Länder',
      inactiveCountries: 'Inaktive Länder'
    },
    messages: {
      confirmDelete: 'Sind Sie sicher, dass Sie dieses Land löschen möchten?',
      deleteSuccess: 'Land erfolgreich gelöscht',
      createSuccess: 'Land erfolgreich erstellt',
      updateSuccess: 'Land erfolgreich aktualisiert',
      loadError: 'Fehler beim Laden der Länder. Bitte versuchen Sie es erneut.',
      deleteError: 'Fehler beim Löschen des Landes. Bitte versuchen Sie es erneut.',
      createError: 'Fehler beim Erstellen des Landes. Bitte versuchen Sie es erneut.',
      updateError: 'Fehler beim Aktualisieren des Landes. Bitte versuchen Sie es erneut.'
    }
  }
};

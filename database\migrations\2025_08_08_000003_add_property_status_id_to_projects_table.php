<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            // Add property_status_id column
            $table->unsignedBigInteger('property_status_id')->nullable()->after('property_type_id');
            
            // Add foreign key constraint
            $table->foreign('property_status_id')->references('id')->on('property_statuses')->onDelete('set null');
            
            // Add index for better performance
            $table->index('property_status_id');
        });

        // Update existing records to map old status enum values to property_status_id
        $this->migrateExistingStatuses();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('projects', function (Blueprint $table) {
            // Drop foreign key and index
            $table->dropForeign(['property_status_id']);
            $table->dropIndex(['property_status_id']);
            
            // Drop the column
            $table->dropColumn('property_status_id');
        });
    }

    /**
     * Migrate existing status enum values to property_status_id
     */
    private function migrateExistingStatuses(): void
    {
        // Get property status mappings
        $statusMappings = [
            'planning' => 1,
            'under_construction' => 2,
            'completed' => 3,
            'sold' => 4,
            'rented' => 5,
            'maintenance' => 6,
        ];

        // Update projects with corresponding property_status_id
        foreach ($statusMappings as $oldStatus => $statusId) {
            DB::table('projects')
                ->where('status', $oldStatus)
                ->update(['property_status_id' => $statusId]);
        }

        // For any status values that don't match, set to 'planning' (id: 1)
        DB::table('projects')
            ->whereNull('property_status_id')
            ->update(['property_status_id' => 1]);
    }
};

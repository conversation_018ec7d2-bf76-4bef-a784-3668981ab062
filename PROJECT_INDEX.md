# Real Estate Management System - Project Index

## Project Overview
This is a comprehensive real estate management system built with Laravel 12 backend and React frontend, designed to manage properties, land acquisitions, projects, customers, vendors, and employees.

### Technology Stack
- **Backend**: Laravel 12 (PHP 8.2+)
- **Frontend**: React 18 with Vite
- **Styling**: Tailwind CSS
- **UI Components**: Radix UI primitives
- **Authentication**: Laravel Sanctum
- **Module System**: nwidart/laravel-modules
- **Database**: MySQL/MariaDB (via XAMPP)

## Project Structure

### Backend Architecture

#### Core Models (`app/Models/`)
- **User Management**
  - `User.php` - Base user model
  - `Employee.php` - Employee management
  - `Role.php` - Role-based access control

- **Real Estate Core**
  - `Project.php` - Main project entity
  - `ProjectUnit.php` - Individual units within projects
  - `ProjectImage.php` - Project image galleries
  - `ProjectVideo.php` - Project video galleries
  - `UnitDetail.php` - Detailed unit specifications
  - `UnitType.php` - Unit type classifications

- **Land Management**
  - `LandAcquisition.php` - Land acquisition records
  - `LandAddress.php` - Land address information
  - `LandDocument.php` - Land-related documents

- **Location & Geography**
  - `Country.php` - Country master data
  - `State.php` - State/province data
  - `Location.php` - Detailed location information
  - `Language.php` - Multi-language support
  - `Currency.php` - Currency management

- **Business Relations**
  - `Customer.php` - Customer management
  - `Vendor.php` - Vendor management
  - `VendorType.php` - Vendor categorization
  - `Contractor.php` - Contractor management
  - `CommissionAgent.php` - Commission agent records

- **Property Features**
  - `PropertyAmenity.php` - Property amenities

#### Modules System
- **LandOwners Module** (`Modules/LandOwners/`)
  - Self-contained module for land owner management
  - Includes models, controllers, views, and routes
  - Audit trail functionality with `LandOwnerAudit.php`

#### API Routes (`routes/api.php`)
- **Authentication APIs**
  - Registration, login, logout
  - Profile management
  - Password change
  - Token refresh

- **Dropdown/Reference APIs**
  - Countries, states, locations
  - Vendor types
  - Hierarchical location data (countries → states → locations)

#### Database Migrations
**Core Tables**:
- `users` - User authentication and profiles
- `employees` - Employee management
- `customers` - Customer records
- `projects` - Main project data
- `project_units` - Individual units
- `project_images` - Image galleries
- `project_videos` - Video galleries
- `vendors` - Vendor management
- `vendor_types` - Vendor categorization
- `contractors` - Contractor records
- `countries`, `states`, `locations` - Geographic hierarchy
- `languages`, `currencies` - Internationalization
- `property_amenities` - Property features
- `unit_types`, `unit_details` - Unit specifications
- `land_acquisitions`, `land_addresses` - Land management

### Frontend Architecture (`resources/js/`)

#### Core Structure
- **Entry Point**: `main.jsx` - React application bootstrap
- **App**: `app.js` - Main application component
- **Bootstrap**: `bootstrap.js` - Application initialization

#### Components (`components/`)
- **UI Components** (`ui/`)
  - `avatar.jsx` - User avatar component
  - `content-modal.jsx` - Modal dialog system
  - Form components and UI primitives

- **Dashboard Components** (`dashboard/`)
  - `ProjectModal.jsx` - Project creation/editing modal
  - Property management interfaces
  - Data visualization components

#### Application Features
- **Pages** (`pages/`) - Route-based page components
- **Contexts** (`contexts/`) - React context providers
- **Hooks** (`hooks/`) - Custom React hooks
- **Services** (`services/`) - API communication layer
- **Utils** (`utils/`) - Utility functions
- **Translations** (`translations/`) - Internationalization files

#### Configuration
- **i18n.js** - Internationalization setup
- **Vite Config** - Frontend build configuration
- **Tailwind Config** - Styling configuration

## Key Features

### 1. Project Management
- Create and manage real estate projects
- Multi-media support (images, videos)
- Unit management within projects
- Property specifications and amenities

### 2. Land Management
- Land acquisition tracking
- Document management
- Address and location management
- Audit trail for land owner changes

### 3. Stakeholder Management
- Customer relationship management
- Vendor and contractor management
- Employee management
- Commission agent tracking

### 4. Geographic Data
- Hierarchical location system (Country → State → Location)
- Multi-language support
- Currency management

### 5. User Management
- Role-based access control
- Authentication via Laravel Sanctum
- Profile management
- Audit trails

## Development Setup

### Prerequisites
- PHP 8.2+
- Node.js 18+
- XAMPP (for local MySQL)
- Composer

### Installation Commands
```bash
# Backend dependencies
composer install

# Frontend dependencies
npm install

# Database setup
php artisan migrate
php artisan db:seed

# Development servers
php artisan serve          # Backend (port 8000)
npm run dev               # Frontend (Vite dev server)
```

### Build Commands
```bash
# Production build
npm run build

# Testing
php artisan test
```

## File Organization Principles

### Backend (Laravel)
- **Models**: Single responsibility, relationship definitions
- **Controllers**: RESTful API endpoints
- **Migrations**: Database schema versioning
- **Modules**: Encapsulated business logic

### Frontend (React)
- **Components**: Reusable UI elements
- **Pages**: Route-based views
- **Services**: API abstraction layer
- **Hooks**: Stateful logic reuse
- **Utils**: Pure functions and helpers

## API Architecture

### Authentication Flow
1. Register/Login → JWT token
2. Protected routes via Sanctum middleware
3. Token refresh mechanism
4. Multi-session management

### Data Flow
1. React components → Services layer
2. Services → Laravel API routes
3. Controllers → Models → Database
4. Response → JSON API format

### State Management
- React Context for global state
- Custom hooks for data fetching
- Local component state for UI

## Notable Patterns

### Backend Patterns
- **Repository Pattern**: Implied through Eloquent models
- **Service Layer**: Controllers delegate to service classes
- **Observer Pattern**: Model events and listeners
- **Module Pattern**: Separate business domains

### Frontend Patterns
- **Component Composition**: Reusable UI building blocks
- **Hooks Pattern**: Logic separation and reuse
- **Context Pattern**: Global state management
- **Service Layer**: API abstraction

## Security Considerations
- Laravel Sanctum for API authentication
- CSRF protection
- Input validation and sanitization
- Role-based access control
- Audit trails for sensitive operations

This index provides a comprehensive overview of the real estate management system's architecture, components, and development practices.

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('land_owner_audits', function (Blueprint $table) {
            // Add missing columns that the audit system expects
            $table->string('event_type')->nullable()->after('action'); // 'automatic', 'manual'
            $table->string('user_name')->nullable()->after('user_id'); // Cache user name
            $table->string('user_email')->nullable()->after('user_name'); // Cache user email
            $table->string('source')->nullable()->after('user_agent'); // 'api', 'web', etc.
            $table->text('description')->nullable()->after('source'); // Human readable description
            $table->json('metadata')->nullable()->after('description'); // Additional metadata
            
            // Rename 'notes' to match what audit system expects (optional)
            // The audit system currently doesn't use 'notes' column
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('land_owner_audits', function (Blueprint $table) {
            // Remove the columns we added
            $table->dropColumn([
                'event_type',
                'user_name', 
                'user_email',
                'source',
                'description',
                'metadata'
            ]);
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_units', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained('projects')->onDelete('cascade');
            $table->string('unit_number');
            $table->enum('unit_type', ['studio', '1bhk', '2bhk', '3bhk', '4bhk', 'penthouse', 'duplex', 'commercial', 'office', 'shop'])->default('1bhk');
            $table->integer('floor_number')->nullable();
            $table->decimal('area', 10, 2)->nullable();
            $table->integer('bedrooms')->nullable();
            $table->integer('bathrooms')->nullable();
            
            // Pricing
            $table->decimal('rent_price', 15, 2)->nullable();
            $table->decimal('sell_price', 15, 2)->nullable();
            $table->decimal('lease_price', 15, 2)->nullable();
            
            $table->enum('status', ['available', 'sold', 'rented', 'reserved', 'under_construction'])->default('available');
            $table->text('description')->nullable();
            $table->json('amenities')->nullable();
            
            // Audit fields
            $table->unsignedBigInteger('created_by')->nullable();
            $table->unsignedBigInteger('updated_by')->nullable();
            $table->timestamps();
            $table->softDeletes();
            
            // Foreign keys
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
            
            // Indexes
            $table->index(['project_id', 'status']);
            $table->index(['unit_type', 'status']);
            $table->unique(['project_id', 'unit_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_units');
    }
};

<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ProductionDiagnosticsController extends Controller
{
    /**
     * Run production diagnostics to identify issues
     */
    public function diagnose(): JsonResponse
    {
        $diagnostics = [
            'environment' => app()->environment(),
            'app_debug' => config('app.debug'),
            'app_url' => config('app.url'),
            'database_connection' => $this->testDatabaseConnection(),
            'storage_permissions' => $this->checkStoragePermissions(),
            'cache_status' => $this->checkCacheStatus(),
            'session_config' => $this->checkSessionConfig(),
            'sanctum_config' => $this->checkSanctumConfig(),
            'cors_config' => $this->checkCorsConfig(),
            'php_config' => $this->checkPhpConfig(),
            'server_info' => $this->getServerInfo(),
        ];

        return response()->json([
            'success' => true,
            'diagnostics' => $diagnostics,
            'timestamp' => now()->toISOString(),
        ]);
    }

    private function testDatabaseConnection(): array
    {
        try {
            \DB::connection()->getPdo();
            $tables = \DB::select('SHOW TABLES');
            
            return [
                'status' => 'connected',
                'driver' => config('database.default'),
                'host' => config('database.connections.mysql.host'),
                'database' => config('database.connections.mysql.database'),
                'tables_count' => count($tables),
                'land_owners_exists' => \Schema::hasTable('land_owners'),
                'land_owner_audits_exists' => \Schema::hasTable('land_owner_audits'),
                'users_exists' => \Schema::hasTable('users'),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'failed',
                'error' => $e->getMessage(),
            ];
        }
    }

    private function checkStoragePermissions(): array
    {
        $paths = [
            'storage' => storage_path(),
            'storage/logs' => storage_path('logs'),
            'storage/app' => storage_path('app'),
            'storage/framework' => storage_path('framework'),
            'bootstrap/cache' => base_path('bootstrap/cache'),
            'public/landowners' => public_path('landowners'),
        ];

        $permissions = [];
        foreach ($paths as $name => $path) {
            $permissions[$name] = [
                'path' => $path,
                'exists' => file_exists($path),
                'readable' => is_readable($path),
                'writable' => is_writable($path),
                'permissions' => file_exists($path) ? substr(sprintf('%o', fileperms($path)), -4) : 'N/A',
            ];
        }

        return $permissions;
    }

    private function checkCacheStatus(): array
    {
        try {
            return [
                'config_cached' => file_exists(base_path('bootstrap/cache/config.php')),
                'routes_cached' => file_exists(base_path('bootstrap/cache/routes-v7.php')),
                'views_cached' => is_dir(storage_path('framework/views')),
                'cache_driver' => config('cache.default'),
            ];
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    private function checkSessionConfig(): array
    {
        return [
            'driver' => config('session.driver'),
            'lifetime' => config('session.lifetime'),
            'domain' => config('session.domain'),
            'secure' => config('session.secure'),
            'http_only' => config('session.http_only'),
            'same_site' => config('session.same_site'),
        ];
    }

    private function checkSanctumConfig(): array
    {
        return [
            'stateful_domains' => config('sanctum.stateful'),
            'guard' => config('sanctum.guard'),
            'expiration' => config('sanctum.expiration'),
        ];
    }

    private function checkCorsConfig(): array
    {
        return [
            'paths' => config('cors.paths', []),
            'allowed_methods' => config('cors.allowed_methods', []),
            'allowed_origins' => config('cors.allowed_origins', []),
            'allowed_headers' => config('cors.allowed_headers', []),
            'supports_credentials' => config('cors.supports_credentials', false),
        ];
    }

    private function checkPhpConfig(): array
    {
        return [
            'version' => PHP_VERSION,
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'file_uploads' => ini_get('file_uploads') ? 'enabled' : 'disabled',
            'extensions' => [
                'pdo_mysql' => extension_loaded('pdo_mysql'),
                'gd' => extension_loaded('gd'),
                'curl' => extension_loaded('curl'),
                'openssl' => extension_loaded('openssl'),
                'mbstring' => extension_loaded('mbstring'),
            ],
        ];
    }

    private function getServerInfo(): array
    {
        return [
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown',
            'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'unknown',
            'script_filename' => $_SERVER['SCRIPT_FILENAME'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'https' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
        ];
    }
}

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import StandardModal from "@/components/ui/StandardModal";
import { currencyAPI } from '../../services/currencyAPI';
import { useTranslation } from '@/hooks/useTranslation';
import Swal from 'sweetalert2';
import { 
  Plus, 
  Search, 
  Edit2, 
  Trash2, 
  DollarSign,
  Power,
  TrendingUp,
  ToggleLeft,
  ToggleRight
} from 'lucide-react';

const CurrencyPage = () => {
  const { t } = useTranslation();
  
  const [currencies, setCurrencies] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showModal, setShowModal] = useState(false);
  const [editingCurrency, setEditingCurrency] = useState(null);
  const [statistics, setStatistics] = useState({});
  const [pagination, setPagination] = useState({});

  const [formData, setFormData] = useState({
    name: '',
    code: '',
    symbol: '',
    exchange_rate: '1.0000',
    is_default: false,
    is_active: true
  });

  useEffect(() => {
    fetchCurrencies();
    fetchStatistics();
  }, [searchTerm]);

  const fetchCurrencies = async (page = 1) => {
    try {
      setLoading(true);
      const params = {
        search: searchTerm,
        page,
        per_page: 10,
        sort_by: 'name',
        sort_order: 'asc'
      };
      
      const response = await currencyAPI.index(params);
      if (response.success) {
        setCurrencies(response.data.data);
        setPagination({
          current_page: response.data.current_page,
          last_page: response.data.last_page,
          total: response.data.total
        });
      } else {
        throw new Error(response.message || 'Failed to fetch currencies');
      }
    } catch (error) {
      console.error('Error fetching currencies:', error);
      Swal.fire({
        title: 'Error!',
        text: 'Failed to load currencies',
        icon: 'error',
        confirmButtonText: 'OK'
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await currencyAPI.getStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error('Error fetching statistics:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      let response;
      if (editingCurrency) {
        response = await currencyAPI.update(editingCurrency.id, formData);
      } else {
        response = await currencyAPI.store(formData);
      }

      if (response.success) {
        Swal.fire({
          title: 'Success!',
          text: response.message,
          icon: 'success',
          confirmButtonText: 'OK'
        });
        
        setShowModal(false);
        resetForm();
        fetchCurrencies();
        fetchStatistics();
      } else {
        throw new Error(response.message || 'Operation failed');
      }
    } catch (error) {
      console.error('Error saving currency:', error);
      Swal.fire({
        title: 'Error!',
        text: error.message || 'Failed to save currency',
        icon: 'error',
        confirmButtonText: 'OK'
      });
    }
  };

  const handleEdit = (currency) => {
    setEditingCurrency(currency);
    setFormData({
      name: currency.name,
      code: currency.code,
      symbol: currency.symbol,
      exchange_rate: currency.exchange_rate.toString(),
      is_default: currency.is_default,
      is_active: currency.is_active
    });
    setShowModal(true);
  };

  const handleDelete = async (currency) => {
    try {
      const result = await Swal.fire({
        title: 'Are you sure?',
        text: `Do you want to delete ${currency.name}?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, delete it!'
      });

      if (result.isConfirmed) {
        const response = await currencyAPI.destroy(currency.id);
        
        if (response.success) {
          Swal.fire({
            title: 'Deleted!',
            text: response.message,
            icon: 'success',
            confirmButtonText: 'OK'
          });
          
          fetchCurrencies();
          fetchStatistics();
        } else {
          throw new Error(response.message);
        }
      }
    } catch (error) {
      console.error('Error deleting currency:', error);
      Swal.fire({
        title: 'Error!',
        text: error.message || 'Failed to delete currency',
        icon: 'error',
        confirmButtonText: 'OK'
      });
    }
  };

  const handleToggleStatus = async (currency) => {
    try {
      const response = await currencyAPI.toggleStatus(currency.id);
      
      if (response.success) {
        Swal.fire({
          title: 'Success!',
          text: response.message,
          icon: 'success',
          timer: 1500,
          showConfirmButton: false
        });
        
        fetchCurrencies();
        fetchStatistics();
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      console.error('Error toggling status:', error);
      Swal.fire({
        title: 'Error!',
        text: error.message || 'Failed to update status',
        icon: 'error',
        confirmButtonText: 'OK'
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      code: '',
      symbol: '',
      exchange_rate: '1.0000',
      is_default: false,
      is_active: true
    });
    setEditingCurrency(null);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    resetForm();
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Currency Management</h1>
          <p className="text-muted-foreground">
            Manage currencies and exchange rates for your application.
          </p>
        </div>
        <Button onClick={() => setShowModal(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Add Currency
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-800">Total Currencies</CardTitle>
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <DollarSign className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900">{statistics.total_currencies || 0}</div>
            <p className="text-xs text-blue-600 mt-1">All registered currencies</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-800">Active Currencies</CardTitle>
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
              <Power className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">{statistics.active_currencies || 0}</div>
            <p className="text-xs text-green-600 mt-1">Currently available currencies</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-800">Inactive Currencies</CardTitle>
            <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
              <ToggleLeft className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-900">{statistics.inactive_currencies || 0}</div>
            <p className="text-xs text-red-600 mt-1">Not currently active</p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-50 to-orange-100 border-orange-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-orange-800">Default Currency</CardTitle>
            <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-900">
              {statistics.default_currency?.code || 'None'}
            </div>
            <p className="text-xs text-orange-600 mt-1">System default currency</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filter */}
      <Card>
        <CardHeader>
          <CardTitle>Search Currencies</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2">
            <Search className="h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search by name, code, or symbol..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="max-w-sm"
            />
          </div>
        </CardContent>
      </Card>

      {/* Currencies Table */}
      <Card>
        <CardHeader>
          <CardTitle>Currencies</CardTitle>
          <CardDescription>
            A list of all currencies in the system.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center h-32">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : (
            <div className="space-y-4">
              {currencies.length === 0 ? (
                <p className="text-center text-muted-foreground py-8">
                  No currencies found.
                </p>
              ) : (
                <div className="space-y-2">
                  {currencies.map((currency) => (
                    <div
                      key={currency.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-accent"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                          <span className="text-lg font-bold text-primary">
                            {currency.symbol}
                          </span>
                        </div>
                        <div>
                          <div className="flex items-center space-x-2">
                            <h3 className="font-semibold">{currency.name}</h3>
                            <Badge variant="secondary">{currency.code}</Badge>
                            {currency.is_default && (
                              <Badge variant="default">Default</Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">
                            Exchange Rate: {currency.exchange_rate}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleStatus(currency)}
                          className={currency.is_active ? 'text-green-600' : 'text-red-600'}
                        >
                          {currency.is_active ? (
                            <ToggleRight className="h-4 w-4" />
                          ) : (
                            <ToggleLeft className="h-4 w-4" />
                          )}
                        </Button>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(currency)}
                        >
                          <Edit2 className="h-4 w-4" />
                        </Button>
                        
                        {!currency.is_default && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDelete(currency)}
                            className="text-red-600 hover:text-red-800"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {/* Pagination */}
              {pagination.last_page > 1 && (
                <div className="flex items-center justify-between pt-4">
                  <p className="text-sm text-muted-foreground">
                    Showing page {pagination.current_page} of {pagination.last_page} 
                    ({pagination.total} total currencies)
                  </p>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchCurrencies(pagination.current_page - 1)}
                      disabled={pagination.current_page === 1}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => fetchCurrencies(pagination.current_page + 1)}
                      disabled={pagination.current_page === pagination.last_page}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Modal */}
      <StandardModal 
        showModal={showModal} 
        closeModal={handleCloseModal}
        modalMode={editingCurrency ? 'edit' : 'create'}
        title={editingCurrency ? 'Edit Currency' : 'Add New Currency'}
        icon={editingCurrency ? Edit2 : Plus}
        maxWidth="max-w-md"
      >
        <div className="text-center pb-4 border-b border-gray-200 dark:border-gray-700">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
            <DollarSign className="w-8 h-8 text-white" />
          </div>
          <p className="text-gray-600 dark:text-gray-400">
            {editingCurrency ? 'Update currency information' : 'Create a new currency'}
          </p>
        </div>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">Currency Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="e.g., US Dollar"
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="code">Code</Label>
                  <Input
                    id="code"
                    value={formData.code}
                    onChange={(e) => handleInputChange('code', e.target.value.toUpperCase())}
                    placeholder="USD"
                    maxLength={3}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="symbol">Symbol</Label>
                  <Input
                    id="symbol"
                    value={formData.symbol}
                    onChange={(e) => handleInputChange('symbol', e.target.value)}
                    placeholder="$"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="exchange_rate">Exchange Rate</Label>
                <Input
                  id="exchange_rate"
                  type="number"
                  step="0.0001"
                  min="0.0001"
                  value={formData.exchange_rate}
                  onChange={(e) => handleInputChange('exchange_rate', e.target.value)}
                  required
                />
              </div>

              <div className="flex items-center space-x-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.is_default}
                    onChange={(e) => handleInputChange('is_default', e.target.checked)}
                  />
                  <span className="text-sm">Set as default currency</span>
                </label>

                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={formData.is_active}
                    onChange={(e) => handleInputChange('is_active', e.target.checked)}
                  />
                  <span className="text-sm">Active</span>
                </label>
              </div>

              <div className="flex justify-end space-x-2 pt-4">
                <Button type="button" variant="outline" onClick={handleCloseModal}>
                  Cancel
                </Button>
                <Button type="submit">
                  {editingCurrency ? 'Update' : 'Create'} Currency
                </Button>
              </div>
            </form>
        </StandardModal>
    </div>
  );
};

export default CurrencyPage;

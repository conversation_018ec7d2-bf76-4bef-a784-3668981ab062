<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('property_stages', function (Blueprint $table) {
            // Add slug field for URL-friendly identifiers
            $table->string('slug', 100)->unique()->after('name');

            // Add color field for UI representation
            $table->string('color', 7)->default('#3B82F6')->after('description'); // Hex color code

            // Add icon field for UI representation
            $table->string('icon', 50)->nullable()->after('color'); // Icon name for UI

            // Add status field for active/inactive states
            $table->enum('status', ['active', 'inactive'])->default('active')->after('icon');

            // Add sort_order for custom ordering
            $table->integer('sort_order')->default(0)->after('status');

            // Add metadata field for additional configuration
            $table->json('metadata')->nullable()->after('sort_order');

            // Add completion percentage field
            $table->decimal('completion_percentage', 5, 2)->default(0)->after('metadata');

            // Add indexes for better performance
            $table->index(['status', 'sort_order']);
            $table->index('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('property_stages', function (Blueprint $table) {
            // Drop indexes first
            $table->dropIndex(['status', 'sort_order']);
            $table->dropIndex(['slug']);

            // Drop columns
            $table->dropColumn([
                'slug',
                'color',
                'icon',
                'status',
                'sort_order',
                'metadata',
                'completion_percentage'
            ]);
        });
    }
};
